# Earn功能实现计划

- [x] 1. 扩展导航系统支持Earn Tab
  - 更新TabDestination枚举添加EARN选项
  - 修改BottomNavigationBar组件支持4个Tab布局
  - 更新MainActivity中的导航逻辑支持Earn页面
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2. 创建Earn功能的核心数据模型
  - 创建TaskStatus、TaskType枚举类
  - 实现Task数据类定义任务结构
  - 创建EarnData数据类聚合页面数据
  - _Requirements: 10.1, 10.2, 10.3, 11.1_

- [x] 3. 实现EarnContract定义MVI架构接口
  - 创建EarnIntent密封类定义用户意图
  - 实现EarnUiState数据类管理UI状态
  - 定义EarnEffect密封类处理副作用
  - _Requirements: 11.1, 11.2, 11.3_

- [x] 4. 实现TimeManager时间管理工具类
  - 创建getCurrentDateString方法获取当前日期
  - 实现getTimeUntilMidnight方法计算倒计时
  - 添加isNewDay方法判断日期变更
  - 编写TimeManager的单元测试
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 5. 实现EarnStorage数据存储层
  - 创建EarnStorage类使用MMKV存储Earn数据
  - 实现金币相关的存储和读取方法
  - 实现签到功能的数据持久化逻辑
  - 实现任务状态的存储和查询方法
  - 添加转盘游戏次数统计功能
  - 编写EarnStorage的单元测试
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7_

- [x] 6. 实现EarnRepository业务逻辑层
  - 创建EarnRepository类整合数据访问
  - 实现getEarnData方法构建页面数据
  - 实现claimTask方法处理任务奖励领取
  - 添加buildDailyTasks方法构建每日任务列表
  - 添加buildOtherTasks方法构建其他任务列表
  - 实现任务状态验证和更新逻辑
  - 编写EarnRepository的单元测试
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8_

- [x] 7. 实现EarnViewModel状态管理
  - 创建EarnViewModel类继承BaseViewModel
  - 实现Intent处理逻辑分发用户操作
  - 添加数据加载和刷新功能
  - 实现任务领取的业务逻辑处理
  - 添加导航Effect的触发逻辑
  - 实现倒计时更新的协程逻辑
  - 编写EarnViewModel的单元测试
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 5.1, 5.2, 5.3, 5.4, 5.5, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 8. 创建Earn页面UI组件
  - 实现EarnScreen主要Compose组件
  - 创建CoinBalanceSection显示金币余额和提现按钮
  - 实现MoreGamesSection显示三个游戏入口（Lucky Wheel、Egg Smash、Lucky Scratch）
  - 创建DailyTasksSection每日任务列表
  - 实现OtherTasksSection其他任务列表
  - 添加CountdownTimer倒计时显示组件
  - 实现游戏卡片的视觉设计和点击交互
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.3, 3.4, 3.5, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 8.1, 8.2, 8.3, 8.4, 8.5, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6, 12.7_

- [x] 9. 实现任务项UI组件
  - 创建TaskItem组件显示单个任务
  - 实现任务状态的视觉区分（Go/Claim/勾选）
  - 添加任务图标和奖励金币显示
  - 实现按钮点击的交互逻辑
  - 添加任务进度显示功能
  - _Requirements: 4.4, 4.5, 4.6, 4.7, 6.4, 6.5, 6.6, 7.4, 7.5, 7.6, 7.7, 12.3, 12.4_

- [x] 10. 实现签到功能逻辑
  - 在EarnRepository中实现每日签到检查
  - 添加连续签到天数计算逻辑
  - 实现签到状态的UI更新
  - 添加签到奖励的发放机制
  - 实现签到中断时的状态重置
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 7.8_

- [x] 11. 实现步数任务集成
  - 集成现有StepRepository获取当日步数
  - 实现步数任务状态的实时更新逻辑
  - 添加步数目标达成检测
  - 实现步数任务的奖励发放
  - 添加跳转到Steps页面的导航逻辑
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7_

- [x] 12. 实现游戏集成系统
  - 创建GameType枚举定义三种游戏类型（LUCKY_WHEEL、EGG_SMASH、LUCKY_SCRATCH）
  - 实现GameEntry数据模型描述游戏入口
  - 添加游戏导航的Intent和Effect定义
  - 实现MoreGamesSection中三个游戏的点击导航
  - 添加Lucky Wheel（9宫格转盘）游戏页面的导航支持
  - 添加Egg Smash（敲金蛋）游戏页面的导航支持
  - 添加Lucky Scratch（刮刮乐）游戏页面的导航支持
  - 实现转盘游戏次数统计功能
  - 添加游戏任务状态更新逻辑
  - 实现从各游戏页面返回时的数据刷新
  - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8_

- [ ] 13. 实现倒计时功能
  - 在EarnViewModel中添加倒计时协程
  - 实现每分钟更新倒计时显示
  - 添加午夜时间重置任务状态逻辑
  - 实现页面进入时倒计时的准确显示
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 14. 集成Earn Tab到主导航
  - 更新TabDestination枚举添加EARN
  - 修改BottomNavigationBar支持4个Tab
  - 在MainActivity中添加Earn页面路由
  - 更新UnifiedTopAppBar支持Earn页面
  - 添加Earn Tab的图标资源
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [ ] 15. 实现奖励动画效果
  - 创建金币增加的动画组件
  - 实现任务完成时的视觉反馈
  - 添加按钮状态变化的过渡动画
  - _Requirements: 11.7, 12.4_

- [ ] 16. 添加错误处理和边界情况
  - 实现数据加载失败的错误处理
  - 添加任务状态异常的恢复机制
  - 实现网络异常时的本地数据回退
  - 添加时间异常时的备用计算方式
  - _Requirements: 所有需求的错误处理_

- [ ] 17. 编写集成测试
  - 创建Earn功能的端到端测试
  - 测试完整的签到流程
  - 验证任务完成和领取流程
  - 测试跨日期的数据重置功能
  - 验证与Steps页面的导航集成
  - _Requirements: 所有需求的集成测试_

- [ ] 18. 性能优化和最终调试
  - 优化任务列表的渲染性能
  - 实现数据缓存减少重复计算
  - 优化倒计时更新的性能影响
  - 进行内存泄漏检查和修复
  - 最终的功能验证和bug修复
  - _Requirements: 性能和稳定性要求_