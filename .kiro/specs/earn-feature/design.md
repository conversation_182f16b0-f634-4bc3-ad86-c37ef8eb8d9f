# Earn功能设计文档

## 概述

Earn功能是对现有Android计步工具软件的重要扩展，通过游戏化元素和任务系统激励用户保持运动习惯。该功能将作为第四个Tab集成到现有的底部导航系统中，提供金币奖励机制、每日任务系统、签到功能和小游戏入口。

## 架构设计

### 整体架构

Earn功能将遵循现有应用的MVI（Model-View-Intent）架构模式，使用Jetpack Compose构建UI，通过Hilt进行依赖注入。

```mermaid
graph TB
    A[EarnScreen] --> B[EarnViewModel]
    B --> C[EarnRepository]
    C --> D[EarnStorage]
    C --> E[StepRepository]
    C --> F[TimeManager]
    
    B --> G[EarnContract]
    G --> H[EarnUiState]
    G --> I[EarnIntent]
    G --> J[EarnEffect]
    
    D --> K[MMKV Storage]
    E --> L[Room Database]
```

### 核心组件

#### 1. UI层 (Presentation Layer)
- **EarnScreen**: 主要的Compose UI组件
- **EarnViewModel**: 管理UI状态和业务逻辑
- **EarnContract**: 定义Intent、State和Effect

#### 2. 数据层 (Data Layer)
- **EarnRepository**: 数据访问的统一接口
- **EarnStorage**: 使用MMKV存储Earn相关数据
- **TimeManager**: 管理时间相关逻辑（倒计时、日期判断）

#### 3. 模型层 (Domain Layer)
- **Task**: 任务数据模型
- **EarnData**: Earn页面数据模型
- **TaskStatus**: 任务状态枚举

## 组件和接口设计

### 数据模型

```kotlin
// 任务状态枚举
enum class TaskStatus {
    NOT_COMPLETED,  // 未完成 - 显示Go按钮
    COMPLETED,      // 已完成未领取 - 显示Claim按钮
    CLAIMED         // 已领取 - 显示勾选标记
}

// 任务类型枚举
enum class TaskType {
    DAILY_CHECK_IN,     // 每日签到
    LUCKY_WHEEL,        // 转盘游戏
    STEPS_1000,         // 1000步任务
    STEPS_3000,         // 3000步任务
    CHECK_IN_2_DAYS,    // 连续签到2天
    CHECK_IN_3_DAYS     // 连续签到3天
}

// 任务数据模型
data class Task(
    val type: TaskType,
    val title: String,
    val description: String = "",
    val reward: Int,            // 奖励金币数量
    val status: TaskStatus,
    val progress: Int = 0,      // 当前进度
    val target: Int = 1,        // 目标值
    val iconRes: Int
)

// Earn页面数据模型
data class EarnData(
    val coins: Int,
    val dailyTasks: List<Task>,
    val otherTasks: List<Task>,
    val timeUntilReset: String,  // 倒计时显示
    val consecutiveCheckIns: Int  // 连续签到天数
)
```

### Contract定义

```kotlin
// Intent定义
sealed class EarnIntent : UiIntent {
    object LoadData : EarnIntent()
    object RefreshData : EarnIntent()
    data class ClaimTask(val taskType: TaskType) : EarnIntent()
    data class NavigateToTask(val taskType: TaskType) : EarnIntent()
    object NavigateToLuckyWheel : EarnIntent()
    object NavigateToSteps : EarnIntent()
    object UpdateCountdown : EarnIntent()
}

// UI状态定义
data class EarnUiState(
    val coins: Int = 0,
    val dailyTasks: List<Task> = emptyList(),
    val otherTasks: List<Task> = emptyList(),
    val timeUntilReset: String = "24:00",
    val consecutiveCheckIns: Int = 0,
    val isLoading: Boolean = false,
    val currentSteps: Int = 0
) : UiState

// Effect定义
sealed class EarnEffect : UiEffect {
    object NavigateToSteps : EarnEffect()
    object NavigateToLuckyWheel : EarnEffect()
    data class ShowRewardAnimation(val coins: Int) : EarnEffect()
    data class ShowError(val message: String) : EarnEffect()
}
```

### 存储设计

#### EarnStorage类设计

```kotlin
@Singleton
class EarnStorage @Inject constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        // 金币相关
        private const val KEY_COINS = "earn_coins"
        
        // 签到相关
        private const val KEY_LAST_CHECK_IN_DATE = "last_check_in_date"
        private const val KEY_CONSECUTIVE_CHECK_INS = "consecutive_check_ins"
        
        // 任务状态相关（按日期存储）
        private const val KEY_DAILY_TASKS_PREFIX = "daily_tasks_"
        private const val KEY_OTHER_TASKS_PREFIX = "other_tasks_"
        
        // 转盘游戏相关
        private const val KEY_LUCKY_WHEEL_COUNT_PREFIX = "lucky_wheel_count_"
        
        // 奖励配置
        const val REWARD_DAILY_CHECK_IN = 10
        const val REWARD_STEPS_1000 = 5
        const val REWARD_STEPS_3000 = 15
        const val REWARD_CHECK_IN_2_DAYS = 20
        const val REWARD_CHECK_IN_3_DAYS = 50
        const val REWARD_LUCKY_WHEEL_DAILY = 30
    }
    
    // 金币操作
    suspend fun getCoins(): Int
    suspend fun addCoins(amount: Int)
    
    // 签到操作
    suspend fun checkIn(): Boolean  // 返回是否签到成功
    suspend fun getConsecutiveCheckIns(): Int
    suspend fun isCheckedInToday(): Boolean
    
    // 任务状态操作
    suspend fun getTaskStatus(taskType: TaskType, date: String): TaskStatus
    suspend fun updateTaskStatus(taskType: TaskType, date: String, status: TaskStatus)
    
    // 转盘游戏操作
    suspend fun getLuckyWheelCount(date: String): Int
    suspend fun incrementLuckyWheelCount(date: String)
}
```

### Repository设计

```kotlin
@Singleton
class EarnRepository @Inject constructor(
    private val earnStorage: EarnStorage,
    private val stepRepository: StepRepository,
    private val timeManager: TimeManager
) {
    
    suspend fun getEarnData(): EarnData {
        val today = timeManager.getCurrentDateString()
        val coins = earnStorage.getCoins()
        val currentSteps = stepRepository.getTodaySteps()
        val consecutiveCheckIns = earnStorage.getConsecutiveCheckIns()
        
        val dailyTasks = buildDailyTasks(today, currentSteps)
        val otherTasks = buildOtherTasks(consecutiveCheckIns)
        val timeUntilReset = timeManager.getTimeUntilMidnight()
        
        return EarnData(
            coins = coins,
            dailyTasks = dailyTasks,
            otherTasks = otherTasks,
            timeUntilReset = timeUntilReset,
            consecutiveCheckIns = consecutiveCheckIns
        )
    }
    
    suspend fun claimTask(taskType: TaskType): Result<Int> {
        // 验证任务是否可以领取
        // 更新任务状态
        // 增加金币
        // 返回奖励金币数量
    }
    
    private suspend fun buildDailyTasks(date: String, currentSteps: Int): List<Task>
    private suspend fun buildOtherTasks(consecutiveCheckIns: Int): List<Task>
}
```

### 时间管理器设计

```kotlin
@Singleton
class TimeManager @Inject constructor() {
    
    fun getCurrentDateString(): String {
        return SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
    }
    
    fun getTimeUntilMidnight(): String {
        val now = Calendar.getInstance()
        val midnight = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        
        val diff = midnight.timeInMillis - now.timeInMillis
        val hours = diff / (1000 * 60 * 60)
        val minutes = (diff % (1000 * 60 * 60)) / (1000 * 60)
        
        return String.format("%02d:%02d", hours, minutes)
    }
    
    fun isNewDay(lastDate: String): Boolean {
        return getCurrentDateString() != lastDate
    }
}
```

## 数据模型

### 任务配置

| 任务类型 | 奖励金币 | 重置周期 | 完成条件 |
|---------|---------|---------|---------|
| 每日签到 | 10 | 每日 | 点击签到 |
| 转盘游戏 | 30 | 每日 | 完成10次转盘 |
| 1000步 | 5 | 每日 | 当日步数≥1000 |
| 3000步 | 15 | 每日 | 当日步数≥3000 |
| 连续签到2天 | 20 | 一次性 | 连续签到2天 |
| 连续签到3天 | 50 | 一次性 | 连续签到3天 |

### 存储结构

#### MMKV存储键值设计

```
earn_coins: Int                           // 当前金币总数
last_check_in_date: String               // 最后签到日期
consecutive_check_ins: Int               // 连续签到天数

daily_tasks_2024-01-01_DAILY_CHECK_IN: String    // 每日任务状态
daily_tasks_2024-01-01_LUCKY_WHEEL: String       
daily_tasks_2024-01-01_STEPS_1000: String        
daily_tasks_2024-01-01_STEPS_3000: String        

other_tasks_CHECK_IN_2_DAYS: String      // 其他任务状态
other_tasks_CHECK_IN_3_DAYS: String      

lucky_wheel_count_2024-01-01: Int        // 每日转盘游戏次数
```

## 错误处理

### 异常处理策略

1. **网络异常**: 所有数据本地存储，无网络依赖
2. **存储异常**: 使用try-catch包装MMKV操作，提供默认值
3. **时间异常**: 系统时间异常时使用备用时间计算方式
4. **状态异常**: 任务状态不一致时重置为默认状态

### 数据一致性保证

1. **原子操作**: 金币增加和任务状态更新使用事务性操作
2. **状态验证**: 每次加载时验证任务状态的合法性
3. **数据修复**: 检测到数据异常时自动修复或重置

## 测试策略

### 单元测试

1. **EarnViewModel测试**
   - Intent处理逻辑测试
   - 状态更新测试
   - Effect触发测试

2. **EarnRepository测试**
   - 数据获取逻辑测试
   - 任务状态计算测试
   - 奖励发放测试

3. **EarnStorage测试**
   - 数据存储和读取测试
   - 签到逻辑测试
   - 时间相关逻辑测试

### 集成测试

1. **完整流程测试**
   - 签到流程测试
   - 任务完成和领取流程测试
   - 跨日期数据重置测试

2. **边界条件测试**
   - 午夜时间切换测试
   - 连续签到中断测试
   - 数据异常恢复测试

## 性能考虑

### 内存优化

1. **懒加载**: 任务列表按需构建
2. **状态缓存**: 避免重复计算任务状态
3. **及时释放**: ViewModel销毁时清理资源

### 存储优化

1. **批量操作**: 多个任务状态更新合并为批量操作
2. **压缩存储**: 任务状态使用枚举索引而非字符串
3. **定期清理**: 清理过期的历史任务数据

### UI性能

1. **列表优化**: 使用LazyColumn优化任务列表渲染
2. **动画优化**: 奖励动画使用轻量级实现
3. **倒计时优化**: 使用协程定时器避免频繁更新

## 游戏集成设计

### 游戏类型定义

```kotlin
enum class GameType {
    LUCKY_WHEEL,    // 9宫格转盘游戏
    EGG_SMASH,      // 敲金蛋游戏  
    LUCKY_SCRATCH   // 刮刮乐游戏
}

data class GameEntry(
    val type: GameType,
    val title: String,
    val iconRes: Int,
    val isAvailable: Boolean = true
)
```

### 游戏导航设计

```kotlin
// 在EarnIntent中添加游戏导航
sealed class EarnIntent : UiIntent {
    // ... 其他Intent
    data class NavigateToGame(val gameType: GameType) : EarnIntent()
}

// 在EarnEffect中添加游戏导航效果
sealed class EarnEffect : UiEffect {
    // ... 其他Effect
    data class NavigateToGame(val gameType: GameType) : EarnEffect()
}
```

### 刮刮乐游戏设计

刮刮乐游戏（Lucky Scratch Game）是一种基于水果图案匹配的刮奖游戏：

#### 游戏规则
1. 游戏开始时，显示6张刮刮卡，表面覆盖着粉色涂层
2. 玩家用手指在屏幕上滑动，逐一刮开每张卡片
3. 每张卡片刮开后会显示一种水果图案（共有6种不同的水果图案）
4. 当所有6张卡片都刮开后，系统检查图案匹配情况：
   - 如果6张卡片显示的是同一种水果图案，获得**顶格奖励**（高额金币）
   - 如果6张卡片显示的是不同水果图案，获得**标准奖励**（普通金币）
5. 游戏完成后根据匹配结果给予相应的金币奖励
6. 每日有限定的免费游戏次数

#### 水果图案类型
- 🍉 西瓜
- 🍇 葡萄  
- 🍊 橙子
- 🥑 牛油果
- 🍐 梨子
- 🍒 樱桃

#### 游戏数据模型

```kotlin
enum class FruitType {
    WATERMELON,     // 西瓜
    GRAPE,          // 葡萄
    ORANGE,         // 橙子
    AVOCADO,        // 牛油果
    PEAR,           // 梨子
    CHERRY          // 樱桃
}

data class ScratchCard(
    val id: Int,
    val fruitType: FruitType,
    val isScratched: Boolean = false,
    val scratchedPercentage: Float = 0f
)

enum class ScratchRewardType {
    JACKPOT,        // 顶格奖励（全部图案相同）
    STANDARD        // 标准奖励（图案不同）
}

data class ScratchGameResult(
    val rewardType: ScratchRewardType,
    val coinReward: Int,
    val matchingFruit: FruitType? = null  // 如果是顶格奖励，记录匹配的水果类型
)

data class ScratchGameState(
    val cards: List<ScratchCard>,
    val isGameComplete: Boolean = false,
    val canScratch: Boolean = true,
    val dailyFreeChances: Int = 3,  // 每日免费次数
    val usedChances: Int = 0,
    val gameResult: ScratchGameResult? = null
)
```

#### 奖励配置
- **顶格奖励**：100金币（6张卡片显示相同水果图案）
- **标准奖励**：20金币（6张卡片显示不同水果图案）
- **每日免费次数**：3次

## 扩展性设计

### 新任务类型支持

1. **任务工厂模式**: 支持动态添加新任务类型
2. **配置化奖励**: 奖励金额可通过配置文件调整
3. **插件化游戏**: 游戏入口支持动态配置，支持三种游戏类型的扩展

### 国际化支持

1. **多语言资源**: 所有文本使用资源文件管理
2. **时间格式**: 支持不同地区的时间显示格式
3. **货币显示**: 支持不同地区的货币符号

### 数据分析支持

1. **事件埋点**: 关键用户行为埋点
2. **性能监控**: 页面加载和操作响应时间监控
3. **用户行为**: 任务完成率和用户活跃度统计