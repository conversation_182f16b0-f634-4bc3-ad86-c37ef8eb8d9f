# Earn功能需求文档

## 项目介绍

本项目旨在为现有的Android计步工具软件添加Earn功能模块，通过游戏化元素和任务系统激励用户保持运动习惯。Earn页面将作为第四个Tab添加到现有的底部导航栏中，提供金币系统、每日任务、签到功能和小游戏入口。

## 功能需求

### 需求1：Earn Tab导航集成

**用户故事：** 作为用户，我希望能够通过底部Tab访问Earn页面，以便参与游戏化的运动激励活动。

#### 验收标准

1. 当用户打开应用时，系统应在底部导航栏显示四个Tab：Steps、Report、Profile、Earn
2. 当用户点击Earn Tab时，系统应切换到Earn页面并高亮显示当前Tab
3. 当用户在Earn页面时，系统应保持底部导航栏可见且功能正常
4. 当用户从其他页面切换到Earn页面时，系统应保持页面状态和数据

### 需求2：金币余额显示

**用户故事：** 作为用户，我希望能够查看我的金币余额，以便了解我的奖励积累情况。

#### 验收标准

1. 当用户进入Earn页面时，系统应在页面顶部显示当前金币余额
2. 当用户的金币数量发生变化时，系统应实时更新余额显示
3. 当用户点击"Withdraw"按钮时，系统应显示提现功能（暂时显示占位功能）
4. 当用户查看余额时，系统应显示格式为"2134 = $ NA"的金币和等值显示

### 需求3：游戏入口区域

**用户故事：** 作为用户，我希望能够看到可用的游戏选项，以便选择参与不同的游戏活动。

#### 验收标准

1. 当用户查看Earn页面时，系统应显示"More games"区域
2. 当用户查看游戏区域时，系统应显示三个游戏选项：Lucky Wheel（9宫格转盘）、Egg Smash（敲金蛋）、Lucky Scratch（刮刮乐）
3. 当用户点击Lucky Wheel时，系统应跳转到9宫格转盘游戏页面
4. 当用户点击Egg Smash时，系统应跳转到敲金蛋游戏页面
5. 当用户点击Lucky Scratch时，系统应跳转到刮刮乐游戏页面
5. 当用户查看游戏选项时，系统应显示吸引人的游戏图标和名称

### 需求4：每日任务系统

**用户故事：** 作为用户，我希望能够完成每日任务获得奖励，以便通过日常活动赚取金币。

#### 验收标准

1. 当用户查看Earn页面时，系统应显示"Daily tasks"区域
2. 当用户查看每日任务时，系统应显示距离下一天的实时倒计时
3. 当用户查看每日任务时，系统应包含以下任务：
   - Daily check in（每日签到）
   - Lucky Wheel（转盘游戏，0/10次）
   - 1000 steps（1000步目标）
   - 3000 steps（3000步目标）
4. 当用户完成任务但未领取奖励时，系统应显示"Claim"按钮
5. 当用户未完成任务时，系统应显示"Go"按钮
6. 当用户已领取奖励时，系统应显示勾选标记且按钮不可点击
7. 当用户点击步数任务的"Go"按钮时，系统应跳转到Steps页面

### 需求5：签到功能

**用户故事：** 作为用户，我希望能够每天签到一次获得奖励，以便通过简单的日常操作获得金币。

#### 验收标准

1. 当用户每天首次查看签到任务时，系统应显示"Claim"按钮（如果还未签到）
2. 当用户点击签到任务的"Claim"按钮时，系统应：
   - 给用户增加签到奖励金币
   - 将按钮状态改为勾选标记
   - 使按钮变为不可点击状态
   - 更新用户的签到记录
3. 当用户已完成当日签到时，系统应显示勾选标记且不可再次点击
4. 当新的一天开始时，系统应重置签到状态，允许用户再次签到
5. 当用户连续签到时，系统应记录连续签到天数

### 需求6：步数任务系统

**用户故事：** 作为用户，我希望通过完成步数目标获得金币奖励，以便激励我保持运动习惯。

#### 验收标准

1. 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取（显示"Claim"按钮）
2. 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取（显示"Claim"按钮）
3. 当用户的当日步数未达到目标时，系统应显示"Go"按钮
4. 当用户点击步数任务的"Claim"按钮时，系统应：
   - 给用户增加步数任务奖励金币
   - 将按钮状态改为勾选标记
   - 使按钮变为不可点击状态
   - 更新任务完成记录
5. 当用户点击步数任务的"Go"按钮时，系统应跳转到Steps页面
6. 当用户已领取步数任务奖励时，系统应显示勾选标记且不可再次领取
7. 当新的一天开始时，系统应重置步数任务状态

### 需求7：其他任务系统

**用户故事：** 作为用户，我希望通过完成连续签到任务获得额外奖励，以便激励我保持长期的使用习惯。

#### 验收标准

1. 当用户查看Earn页面时，系统应显示"Other tasks"区域
2. 当用户查看其他任务时，系统应包含：
   - Check in 2 days（连续签到2天）
   - Check in 3 days（连续签到3天）
3. 当用户连续签到达到要求天数但未领取奖励时，系统应显示"Claim"按钮
4. 当用户连续签到未达到要求天数时，系统应显示"Go"按钮
5. 当用户已领取连续签到奖励时，系统应显示勾选标记且不可再次领取
6. 当用户点击连续签到任务的"Claim"按钮时，系统应：
   - 给用户增加连续签到奖励金币
   - 将按钮状态改为勾选标记
   - 使按钮变为不可点击状态
7. 当用户点击连续签到任务的"Go"按钮时，系统应跳转到签到任务或显示提示信息
8. 当用户的连续签到记录中断时，系统应重置连续签到任务进度

### 需求8：实时倒计时功能

**用户故事：** 作为用户，我希望能够看到距离下一天的倒计时，以便了解任务重置的时间。

#### 验收标准

1. 当用户查看Daily tasks区域时，系统应在右侧显示倒计时时间
2. 当倒计时显示时，系统应使用"HH:MM"格式显示剩余时间
3. 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
4. 当倒计时到达00:00时，系统应：
   - 重置所有每日任务状态
   - 更新倒计时为下一天的24小时
   - 刷新页面显示状态
5. 当用户重新进入Earn页面时，系统应显示准确的剩余时间

### 需求9：游戏集成系统

**用户故事：** 作为用户，我希望能够参与各种小游戏获得金币奖励，以便通过娱乐方式激励运动。

#### 验收标准

1. 当用户点击More games区域的Lucky Wheel时，系统应跳转到9宫格转盘游戏页面
2. 当用户点击More games区域的Egg Smash时，系统应跳转到敲金蛋游戏页面
3. 当用户点击More games区域的Lucky Scratch时，系统应跳转到刮刮乐游戏页面
4. 当用户点击Daily tasks中的Lucky Wheel任务时，系统应跳转到转盘游戏页面
5. 当用户在任何游戏中获得奖励时，系统应更新用户的金币余额
6. 当用户完成转盘游戏次数达到每日限制时，系统应更新Lucky Wheel任务状态为可领取
7. 当用户从任何游戏页面返回Earn页面时，系统应刷新任务状态和金币余额
8. 当用户参与游戏时，系统应记录游戏次数和获得的奖励

### 需求10：数据持久化

**用户故事：** 作为用户，我希望我的金币余额和任务进度能够被保存，以便下次打开应用时能够继续使用。

#### 验收标准

1. 当用户获得金币时，系统应立即保存金币余额到本地存储
2. 当用户完成任务时，系统应保存任务完成状态和时间戳
3. 当用户签到时，系统应保存签到记录和连续签到天数
4. 当用户重新打开应用时，系统应恢复之前的金币余额和任务状态
5. 当系统重启后，系统应能够恢复所有Earn相关的数据
6. 系统应使用以下存储方式：
   - 金币余额：使用MMKV存储
   - 任务状态：使用MMKV存储，按日期组织
   - 签到记录：使用MMKV存储，记录签到日期和连续天数
7. 当新的一天开始时，系统应自动重置每日任务状态但保留历史记录

### 需求11：奖励金币配置

**用户故事：** 作为用户，我希望通过完成不同任务获得合理的金币奖励，以便感受到成就感和激励效果。

#### 验收标准

1. 当用户完成每日签到时，系统应奖励10金币
2. 当用户完成1000步任务时，系统应奖励5金币
3. 当用户完成3000步任务时，系统应奖励15金币
4. 当用户完成连续签到2天任务时，系统应奖励20金币
5. 当用户完成连续签到3天任务时，系统应奖励50金币
6. 当用户完成Lucky Wheel每日任务时，系统应奖励30金币
7. 当用户获得奖励时，系统应显示金币增加的动画效果
8. 当用户查看任务时，系统应显示每个任务对应的金币奖励数量

### 需求12：用户界面设计

**用户故事：** 作为用户，我希望Earn页面界面美观且易于使用，以便获得良好的用户体验。

#### 验收标准

1. 当用户查看Earn页面时，系统应使用与应用整体风格一致的设计
2. 当用户查看页面时，系统应使用清晰的视觉层次和布局
3. 当用户查看任务状态时，系统应使用不同的颜色和图标区分不同状态：
   - 未完成：显示蓝色"Go"按钮
   - 可领取：显示橙色"Claim"按钮
   - 已完成：显示灰色勾选标记
4. 当用户操作按钮时，系统应提供适当的视觉反馈
5. 当用户查看游戏区域时，系统应显示吸引人的游戏卡片设计
6. 当用户查看倒计时时，系统应使用易读的字体和颜色
7. 当用户滚动页面时，系统应保持流畅的滚动体验