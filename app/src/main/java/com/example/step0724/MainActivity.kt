package com.example.step0724

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.step0724.data.permission.PermissionManager
import com.example.step0724.data.permission.PermissionResultManager
import com.example.step0724.data.service.StepCountingService
import com.example.step0724.navigation.Destination
import com.example.step0724.navigation.TabDestination
import com.example.step0724.ui.components.BottomNavigationBar
import com.example.step0724.ui.components.UnifiedTopAppBar
import com.example.step0724.ui.main.MainEffect
import com.example.step0724.ui.main.MainIntent
import com.example.step0724.ui.main.MainViewModel
import com.example.step0724.ui.permissions.PermissionsScreen
import com.example.step0724.ui.profile.ProfileScreen
import com.example.step0724.ui.report.ReportScreen
import com.example.step0724.ui.settings.SettingsScreen
import com.example.step0724.ui.steps.StepsScreen
import com.example.step0724.ui.steps.StepsViewModel
import com.example.step0724.ui.earn.EarnScreen
import com.example.step0724.ui.earn.EarnViewModel
import android.content.Intent
import com.example.step0724.ui.games.GamesActivity
import com.example.step0724.ui.theme.PrimaryBackground
import com.example.step0724.ui.theme.Step0724Theme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var permissionManager: PermissionManager
    
    @Inject
    lateinit var stepCountingService: StepCountingService
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startStepCountingService()
        } else {
            permissionManager.recordActivityRecognitionDenied()
        }
        PermissionResultManager.notifyPermissionResult(
            Manifest.permission.ACTIVITY_RECOGNITION, 
            isGranted
        )
    }
    
    private val requestNotificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            permissionManager.onNotificationPermissionGranted()
        } else {
            permissionManager.recordNotificationDenied()
        }
        PermissionResultManager.notifyPermissionResult(
            Manifest.permission.POST_NOTIFICATIONS, 
            isGranted
        )
    }
    
    // 权限请求处理函数
    fun requestActivityRecognitionPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            permissionManager.smartRequestActivityRecognitionPermission(this)
        } else {
        }
    }
    
    fun requestNotificationPermission() {
        permissionManager.smartRequestNotificationPermission(this, requestNotificationPermissionLauncher)
    }
    
    fun requestBatteryOptimizationPermission() {
        permissionManager.requestBatteryOptimizationPermission(this)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 检查并请求权限
        checkAndRequestPermissions()
        
        setContent {
            Step0724Theme {
                MainScreen(
                    activity = this
                )
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 应用恢复时检查权限并确保服务运行
        if (permissionManager.hasActivityRecognitionPermission()) {
            startStepCountingService()
        }
    }
    
    private fun checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            when {
                ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.ACTIVITY_RECOGNITION
                ) == PackageManager.PERMISSION_GRANTED -> {
                    startStepCountingService()
                }
                shouldShowRequestPermissionRationale(Manifest.permission.ACTIVITY_RECOGNITION) -> {
                    requestPermissionLauncher.launch(Manifest.permission.ACTIVITY_RECOGNITION)
                }
                else -> {
                    requestPermissionLauncher.launch(Manifest.permission.ACTIVITY_RECOGNITION)
                }
            }
        }
    }
    
    private fun startStepCountingService() {
        Log.d("MainActivity", "Attempting to start step counting service...")
        if (!stepCountingService.isRunning()) {
            stepCountingService.startCounting()
        } else {
            Log.d("MainActivity", "Step counting service is already running")
        }
    }
}

@Composable
fun MainScreen(
    activity: MainActivity,
    viewModel: MainViewModel = hiltViewModel()
) {
    val mainNavController = rememberNavController()
    val navBackStackEntry by mainNavController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    val coroutineScope = rememberCoroutineScope()
    
    val reportViewModel: com.example.step0724.ui.report.ReportViewModel = hiltViewModel()
    
    var isNavigating by remember { mutableStateOf(false) }
    
    fun navigateWithDebounce(destination: String, action: () -> Unit) {
        if (!isNavigating && currentRoute != destination) {
            isNavigating = true
            action()
            coroutineScope.launch {
                delay(500)
                isNavigating = false
            }
        }
    }

    Scaffold(
        containerColor = PrimaryBackground,
        modifier = Modifier.fillMaxSize(),
        topBar = {
            when (currentRoute) {
                "main_tabs" -> {
                    val state by viewModel.state.collectAsState()
                    when (state.selectedTab) {
                        TabDestination.STEPS -> {
                            val earnViewModel: EarnViewModel = hiltViewModel()
                            val earnState by earnViewModel.state.collectAsState()
                            
                            UnifiedTopAppBar(
                                showCoins = true,
                                coins = earnState.coins,
                                showSettingsButton = true,
                                onSettingsClick = {
                                    navigateWithDebounce(Destination.SETTINGS) {
                                        mainNavController.navigate(Destination.SETTINGS) {
                                            launchSingleTop = true
                                        }
                                    }
                                }
                            )
                        }
                        TabDestination.REPORT -> {
                            val reportState by reportViewModel.state.collectAsState()
                            
                            UnifiedTopAppBar(
                                title = "Report",
                                showDataTypeSelector = true,
                                selectedDataType = reportState.selectedDataType,
                                isDataTypeSelectorExpanded = reportState.showDataTypeDropdown,
                                onDataTypeSelectorToggle = {
                                    reportViewModel.sendIntent(com.example.step0724.ui.report.ReportContract.Intent.ToggleDataTypeDropdown)
                                },
                                onDataTypeSelected = { dataType ->
                                    reportViewModel.sendIntent(com.example.step0724.ui.report.ReportContract.Intent.SelectDataType(dataType))
                                }
                            )
                        }
                        TabDestination.PROFILE -> {
                            UnifiedTopAppBar(
                                title = "Profile",
                                showSettingsButton = true,
                                onSettingsClick = {
                                    navigateWithDebounce(Destination.SETTINGS) {
                                        mainNavController.navigate(Destination.SETTINGS) {
                                            launchSingleTop = true
                                        }
                                    }
                                }
                            )
                        }
                        TabDestination.EARN -> {
                            val earnViewModel: EarnViewModel = hiltViewModel()
                            val earnState by earnViewModel.state.collectAsState()
                            
                            UnifiedTopAppBar(
                                title = "Earn",
                                showEarnLayout = true,
                                earnCoins = earnState.coins,
                                showSettingsButton = true,
                                onSettingsClick = {
                                    navigateWithDebounce(Destination.SETTINGS) {
                                        mainNavController.navigate(Destination.SETTINGS) {
                                            launchSingleTop = true
                                        }
                                    }
                                },
                                onWithdrawClick = {
                                    // TODO: Implement withdraw functionality
                                }
                            )
                        }
                    }
                }
                Destination.SETTINGS -> {
                    UnifiedTopAppBar(
                        title = "Settings",
                        showBackButton = true,
                        onBackClick = {
                            if (!isNavigating) {
                                mainNavController.popBackStack()
                            }
                        }
                    )
                }
                Destination.PERMISSIONS -> {
                    UnifiedTopAppBar(
                        title = "Permissions",
                        showBackButton = true,
                        onBackClick = {
                            if (!isNavigating) {
                                mainNavController.popBackStack()
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = mainNavController,
            startDestination = "main_tabs",
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            composable("main_tabs") {
                MainTabsScreen(
                    viewModel = viewModel,
                    reportViewModel = reportViewModel,
                    mainNavController = mainNavController,
                    activity = activity
                )
            }
            
            composable(Destination.SETTINGS) {
                SettingsScreen(
                    onNavigateToPermissions = {
                        navigateWithDebounce(Destination.PERMISSIONS) {
                            mainNavController.navigate(Destination.PERMISSIONS) {
                                launchSingleTop = true
                            }
                        }
                    },
                    onNavigateBack = {
                        if (!isNavigating) {
                            mainNavController.popBackStack()
                        }
                    }
                )
            }
            
            composable(Destination.PERMISSIONS) {
                PermissionsScreen(
                    onNavigateBack = {
                        if (!isNavigating) {
                            mainNavController.popBackStack()
                        }
                    },
                    onRequestActivityRecognition = {
                        activity.requestActivityRecognitionPermission()
                    },
                    onRequestBatteryOptimization = {
                        activity.requestBatteryOptimizationPermission()
                    },
                    onRequestNotification = {
                        activity.requestNotificationPermission()
                    }
                )
            }
            
        }
    }
}

@Composable
fun MainTabsScreen(
    viewModel: MainViewModel,
    reportViewModel: com.example.step0724.ui.report.ReportViewModel,
    mainNavController: NavHostController,
    activity: MainActivity
) {
    val tabNavController = rememberNavController()
    val state by viewModel.state.collectAsState()
    val navBackStackEntry by tabNavController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    val coroutineScope = rememberCoroutineScope()
    
    var isNavigating by remember { mutableStateOf(false) }
    
    fun navigateWithDebounce(destination: String, action: () -> Unit) {
        if (!isNavigating) {
            isNavigating = true
            action()
            // 500ms后重置导航状态
            coroutineScope.launch {
                delay(500)
                isNavigating = false
            }
        }
    }

    LaunchedEffect(currentRoute) {
        currentRoute?.let { route ->
            val destination = TabDestination.values().find { it.route == route }
            destination?.let { tab ->
                if (tab != state.selectedTab) {
                    viewModel.sendIntent(MainIntent.SyncTabWithNavigation(tab))
                }
            }
        }
    }

    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is MainEffect.NavigateToTab -> {
                    tabNavController.navigate(effect.destination.route) {
                        popUpTo(tabNavController.graph.startDestinationId) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        NavHost(
            navController = tabNavController,
            startDestination = TabDestination.STEPS.route,
            modifier = Modifier.weight(1f)
        ) {
            composable(TabDestination.STEPS.route) {
                StepsScreen(
                    onNavigateToLuckyWheel = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "lucky_wheel")
                        activity.startActivity(intent)
                    },
                    onNavigateToEggSmash = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "egg_smash")
                        activity.startActivity(intent)
                    },
                    onNavigateToLuckyScratch = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "lucky_scratch")
                        activity.startActivity(intent)
                    }
                )
            }
            composable(TabDestination.REPORT.route) {
                ReportScreen(viewModel = reportViewModel)
            }
            composable(TabDestination.PROFILE.route) {
                ProfileScreen()
            }
            composable(TabDestination.EARN.route) {
                EarnScreen(
                    onNavigateToSteps = {
                        // Navigate to Steps tab when step task "Go" button is clicked
                        viewModel.sendIntent(MainIntent.SelectTab(TabDestination.STEPS))
                    },
                    onNavigateToLuckyWheel = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "lucky_wheel")
                        activity.startActivity(intent)
                    },
                    onNavigateToEggSmash = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "egg_smash")
                        activity.startActivity(intent)
                    },
                    onNavigateToLuckyScratch = {
                        val intent = Intent(activity, GamesActivity::class.java)
                        intent.putExtra("game_type", "lucky_scratch")
                        activity.startActivity(intent)
                    }
                )
            }
        }
        
        // 底部导航栏
        BottomNavigationBar(
            selectedTab = state.selectedTab,
            onTabSelected = { tab ->
                viewModel.sendIntent(MainIntent.SelectTab(tab))
            }
        )
    }
}
