package com.example.step0724.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.example.step0724.data.service.StepCountingServiceManager
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * 开机启动接收器 - 需求8.7：设备重启时能够从上次保存的步数继续累计
 */
@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {

    @Inject
    lateinit var serviceManager: StepCountingServiceManager

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            // 设备重启后自动启动步数统计服务
            serviceManager.restartServiceIfNeeded()
        }
    }
}