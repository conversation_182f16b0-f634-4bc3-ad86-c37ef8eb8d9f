package com.example.step0724.ui.profile.dialogs

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.domain.usecase.UnitConversionUseCase
import kotlinx.coroutines.launch
import kotlin.math.roundToInt
import com.example.step0724.R

@Composable
fun WeightDialog(
    currentWeight: Double,
    unitSystem: UnitSystem,
    unitConversionUseCase: UnitConversionUseCase,
    onConfirm: (Double) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isMetric = unitSystem == UnitSystem.METRIC
    val displayWeight = if (isMetric) currentWeight else unitConversionUseCase.kgToLbs(currentWeight)
    val unit = if (isMetric) "kg" else "lbs"
    val minWeight = if (isMetric) 30.0 else 66.0
    val maxWeight = if (isMetric) 200.0 else 440.0

    var selectedWeight by remember { mutableDoubleStateOf(displayWeight) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Weight",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.text_primary),
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                ScalePicker(
                    minValue = minWeight,
                    maxValue = maxWeight,
                    initialValue = displayWeight,
                    onValueChange = { selectedWeight = it }
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = String.format("%.1f", selectedWeight),
                        fontSize = 26.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(R.color.primary_blue)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = unit,
                        fontSize = 12.sp,
                        color = colorResource(R.color.text_primary)
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.light_gray)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                    Button(
                        onClick = {
                            val weightInKg = if (isMetric) {
                                selectedWeight
                            } else {
                                unitConversionUseCase.lbsToKg(selectedWeight)
                            }
                            onConfirm(weightInKg)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.primary_blue)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Save",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
private fun ScalePicker(
    minValue: Double,
    maxValue: Double,
    initialValue: Double,
    onValueChange: (Double) -> Unit
) {
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current

    val tickWidth = 10.dp
    val tickWidthPx = with(density) { tickWidth.toPx() }

    val totalTicks = ((maxValue - minValue) / 0.1).roundToInt() + 1
    val listState = rememberLazyListState()

    // 标志来防止初始定位被滚动事件覆盖
    var hasInitialized by remember { mutableStateOf(false) }



    LaunchedEffect(listState.isScrollInProgress) {
        if (!listState.isScrollInProgress && hasInitialized) {
            val centerOffset = listState.firstVisibleItemScrollOffset.toFloat()
            val centerIndex = listState.firstVisibleItemIndex + (centerOffset / tickWidthPx).roundToInt()

            if (centerIndex >= 0 && centerIndex < totalTicks) {
                val snappedValue = minValue + centerIndex * 0.1
                onValueChange(snappedValue)
                scope.launch {
                    listState.animateScrollToItem(centerIndex)
                }
            }
        }
    }

    // 初始定位到传入的值
    LaunchedEffect(initialValue, totalTicks) {
        if (totalTicks > 0 && !hasInitialized) {
            val initialIndex = ((initialValue - minValue) / 0.1).roundToInt().coerceIn(0, totalTicks - 1)
            listState.scrollToItem(initialIndex)
            // 同时更新选中的值
            val actualValue = minValue + initialIndex * 0.1
            onValueChange(actualValue)
            // 标记为已初始化
            hasInitialized = true
        }
    }

    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp),
        contentAlignment = Alignment.Center
    ) {
        // 使用实际的组件宽度来计算正确的内容填充
        val actualContentPadding = (maxWidth / 2) - (tickWidth / 2)
        LazyRow(
            state = listState,
            contentPadding = PaddingValues(horizontal = actualContentPadding),
            modifier = Modifier.fillMaxSize()
        ) {
            items(totalTicks) { index ->
                ScaleTick(index = index, width = tickWidth, minValue = minValue)
            }
        }

        // Center Indicator - Fixed on top of the LazyRow
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.TopCenter)
        ) {
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(30.dp)
                    .background(color = colorResource(R.color.primary_blue))
            )
            Spacer(modifier = Modifier.height(4.dp))
            Canvas(modifier = Modifier.size(12.dp, 8.dp)) {
                val path = Path().apply {
                    moveTo(size.width / 2f, 0f) // Point UP
                    lineTo(0f, size.height)
                    lineTo(size.width, size.height)
                    close()
                }
                drawPath(path, color = Color(0xFF4893FC))
            }
        }
    }
}

@Composable
private fun ScaleTick(
    index: Int,
    width: Dp,
    minValue: Double
) {
    // 计算当前刻度对应的实际值
    val currentValue = minValue + index * 0.1

    // 将值乘以10来避免浮点数精度问题，然后检查是否为整数的倍数
    val valueX10 = (currentValue * 10).roundToInt()

    // 检查是否为整数值（valueX10 能被10整除）
    val isMajorTick = valueX10 % 10 == 0

    // 检查是否为半整数值（valueX10 能被5整除但不能被10整除）
    val isMediumTick = valueX10 % 5 == 0 && !isMajorTick

    val tickHeight = when {
        isMajorTick -> 34.dp   // 长刻度线用于整数值
        isMediumTick -> 22.dp  // 中等刻度线用于半整数值
        else -> 16.dp          // 短刻度线用于其他值
    }

    Box(
        modifier = Modifier
            .width(width)
            .fillMaxHeight(),
        contentAlignment = Alignment.TopCenter
    ) {
        Box(
            modifier = Modifier
                .width(1.dp)
                .height(tickHeight)
                .background(Color(0xFFE0ECFB))
        )
    }
}