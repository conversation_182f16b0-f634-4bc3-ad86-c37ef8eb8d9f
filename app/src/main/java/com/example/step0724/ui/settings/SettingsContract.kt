package com.example.step0724.ui.settings

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState

sealed class SettingsIntent : UiIntent {
    object NavigateToPermissions : SettingsIntent()
    object ContactUs : SettingsIntent()
    object OpenPrivacyPolicy : SettingsIntent()
    object OpenUserAgreement : SettingsIntent()
}

data class SettingsUiState(
    val isLoading: Boolean = false
) : UiState

sealed class SettingsEffect : UiEffect {
    object NavigateToPermissions : SettingsEffect()
}