package com.example.step0724.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.R
import com.example.step0724.ui.theme.Blue300
import com.example.step0724.ui.theme.Blue500
import com.example.step0724.ui.theme.Gray300
import com.example.step0724.ui.theme.PrimaryBlue
import com.example.step0724.ui.theme.TextPrimary
import com.example.step0724.ui.theme.TextSecondary
import kotlin.math.min

/**
 * 圆形步数进度组件
 * 使用 mipmap/bit_progress.png 作为背景图片
 * 使用 mipmap/icon_step_bar.png 作为中心步数图标
 */
@Composable
fun CircularStepProgress(
    currentSteps: Int,
    goalSteps: Int,
    onGoalClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val progress = min(currentSteps.toFloat() / goalSteps.toFloat(), 1f)
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 1000),
        label = "progress"
    )

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 圆形进度区域
        Box(
            modifier = Modifier.height(240.dp),
            contentAlignment = Alignment.Center
        ) {
            // 背景图片
            Image(
                painter = painterResource(id = R.mipmap.bit_progress),
                contentDescription = "Progress Background",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .offset(y = (-20).dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End,
                    modifier = Modifier.widthIn(min = 60.dp)
                ) {
                    Text(
                        text = "0",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = PrimaryBlue
                    )
                }


                Spacer(modifier = Modifier.width(180.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier = Modifier
                        .widthIn(min = 60.dp)
                        .clickable { onGoalClick() }
                        .padding(4.dp)
                ) {
                    Text(
                        text = goalSteps.toString(),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = PrimaryBlue
                    )
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit),
                        contentDescription = "Edit Goal",
                        tint = PrimaryBlue,
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            // 进度条
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                val strokeWidth = 16.dp.toPx() // 进度条宽度
                val radius = (size.minDimension - strokeWidth) / 2 - 21.dp.toPx() // 往里缩10dp
                val arcSize = radius * 2
                val topLeft = Offset(
                    x = (size.width - arcSize) / 2,
                    y = (size.height - arcSize) / 2
                )

                // 进度圆环
                val sweepAngle = animatedProgress * 270f // 画270度，留出底部90度空隙
                drawArc(
                    brush = Brush.sweepGradient(
                        colors = listOf(PrimaryBlue.copy(alpha = 0.7f), PrimaryBlue)
                    ),
                    startAngle = 135f, // 从左下角开始
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    topLeft = topLeft,
                    size = Size(arcSize, arcSize),
                    style = Stroke(
                        width = strokeWidth,
                        cap = StrokeCap.Round
                    )
                )
            }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.icon_step_bar),
                    contentDescription = "Steps",
                    modifier = Modifier.size(32.dp),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = currentSteps.toString(),
                    fontSize = 26.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )
            }
        }
    }
}