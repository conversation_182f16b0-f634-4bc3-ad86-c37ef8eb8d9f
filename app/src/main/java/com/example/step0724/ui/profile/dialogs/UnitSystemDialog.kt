package com.example.step0724.ui.profile.dialogs

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.R

/**
 * 单位制式选择弹窗 - 基于需求4.10和需求9.1-9.2
 * 需求4.10：提供公制(kg/cm)和英制(lbs/ft)选项，默认为公制单位
 * 需求9.1：公制单位使用km、kg、cm
 * 需求9.2：英制单位使用mi、lbs、ft和in
 */
@Composable
fun UnitSystemDialog(
    currentUnitSystem: UnitSystem,
    onConfirm: (UnitSystem) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedUnitSystem by remember { mutableStateOf(currentUnitSystem) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "Metric & Imperial Unit",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.text_primary),
                    modifier = Modifier
                        .fillMaxWidth(),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
                
                // 单位制式选项 - 卡片式选择
                val unitOptions = listOf(
                    UnitSystem.IMPERIAL to "lbs / ft",
                    UnitSystem.METRIC to "kg / cm"
                )
                
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    unitOptions.forEach { (unitSystem, displayName) ->
                        val isSelected = selectedUnitSystem == unitSystem
                        
                        Card(
                            modifier = Modifier
                                .width(130.dp)
                                .clickable { selectedUnitSystem = unitSystem },
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = if (isSelected) colorResource(R.color.light_blue) else colorResource(R.color.light_gray)
                            ),
                            border = if (isSelected) BorderStroke(2.dp, colorResource(R.color.primary_blue)) else null
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = displayName,
                                    fontSize = 16.sp,
                                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                    color = if (isSelected) colorResource(R.color.primary_blue) else colorResource(R.color.text_secondary)
                                )
                            }
                        }
                    }
                }

                // 按钮区域 - 需求5.4：保存、取消逻辑
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮 - 需求5.5：取消设置时不保存更改
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.light_gray)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                    
                    // 确认按钮 - 需求5.4：完成设置后保存设置并关闭弹窗
                    Button(
                        onClick = { onConfirm(selectedUnitSystem) },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.primary_blue)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Save",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

