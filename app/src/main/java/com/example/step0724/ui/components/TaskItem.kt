package com.example.step0724.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.R
import com.example.step0724.data.model.Task
import com.example.step0724.data.model.TaskStatus
import com.example.step0724.ui.theme.*

/**
 * TaskItem组件 - 显示单个任务的UI组件
 * 
 * 功能特性：
 * - 显示任务图标、标题、描述和奖励金币
 * - 根据任务状态显示不同的操作按钮（Go/Claim/勾选）
 * - 支持任务进度显示
 * - 提供点击交互逻辑
 * 
 * @param task 任务数据模型
 * @param onClick 点击回调函数
 * @param modifier 修饰符
 */
@Composable
fun TaskItem(
    task: Task,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White, shape = RoundedCornerShape(16.dp))
            .padding(16.dp)
            .clickable(enabled = task.status != TaskStatus.CLAIMED && task.status != TaskStatus.NOT_ELIGIBLE) {
                onClick()
            },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：任务图标和信息
        TaskInfoSection(
            task = task,
            modifier = Modifier.weight(1f)
        )

        // 右侧：奖励和操作按钮
        TaskActionSection(
            task = task,
            onClick = onClick
        )
    }
}

/**
 * 任务信息区域 - 显示图标、标题、描述和进度
 */
@Composable
private fun TaskInfoSection(
    task: Task,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 任务图标
        Image(
            painter = painterResource(id = task.iconRes),
            contentDescription = null,
            modifier = Modifier.size(34.dp)
        )
        
        // 任务详细信息
        TaskDetails(task = task)
    }
}

/**
 * 任务详细信息 - 显示标题和进度（如果需要）
 */
@Composable
private fun TaskDetails(
    task: Task
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        // 任务标题，对于需要显示进度的任务，在标题后添加进度
        val titleWithProgress = if (shouldShowProgress(task)) {
            "${task.title} (${task.progress}/${task.target})"
        } else {
            task.title
        }
        
        Text(
            text = titleWithProgress,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = colorResource(R.color.text_primary)
        )
    }
}

/**
 * 判断是否需要显示进度
 */
private fun shouldShowProgress(task: Task): Boolean {
    return task.target > 1 && (
        task.title.contains("Lucky Wheel", ignoreCase = true) ||
        task.title.contains("Check in", ignoreCase = true) ||
        task.title.contains("签到", ignoreCase = true)
    )
}


/**
 * 任务操作区域 - 只显示操作按钮
 */
@Composable
private fun TaskActionSection(
    task: Task,
    onClick: () -> Unit
) {
    // 只显示操作按钮
    TaskActionButton(
        status = task.status,
        onClick = onClick
    )
}


/**
 * 任务操作按钮 - 根据状态显示不同的按钮
 */
@Composable
private fun TaskActionButton(
    status: TaskStatus,
    onClick: () -> Unit
) {
    when (status) {
        TaskStatus.NOT_COMPLETED -> {
            Button(
                onClick = onClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = PrimaryBlue
                ),
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(min = 70.dp),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = "Go",
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        TaskStatus.NOT_ELIGIBLE -> {
            Button(
                onClick = { /* 不可点击 */ },
                enabled = false,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE0E0E0), // 灰色背景
                    disabledContainerColor = Color(0xFFE0E0E0)
                ),
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(min = 70.dp),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = "Claim",
                    color = Color(0xFF9E9E9E), // 灰色文字
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        TaskStatus.COMPLETED -> {
            Button(
                onClick = onClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = colorResource(R.color.primary_origin)
                ),
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(min = 70.dp),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = "Claim",
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        TaskStatus.CLAIMED -> {
            Box(
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(min = 70.dp)
                    .background(
                        color = Color(0xFFE0ECFB),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_check_blue),
                    contentDescription = "Completed",
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}
