package com.example.step0724.ui.report

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.data.model.ChartPoint
import com.example.step0724.data.model.DataType
import com.example.step0724.data.model.TimeRange

/**
 * Report页面的MVI契约 - 基于需求3的数据报告页面要求
 */
object ReportContract {
    
    /**
     * 时间范围数据状态
     */
    data class TimeRangeData(
        val chartData: List<ChartPoint> = emptyList(),
        val totalValue: Double = 0.0,
        val averageValue: Double = 0.0,
        val isLoading: Boolean = false,
        val currentDate: String = ""
    )
    
    /**
     * UI状态 - 基于需求3的Report页面状态管理，支持多个时间范围
     */
    data class UiState(
        val selectedDataType: DataType = DataType.STEPS,
        val showDataTypeDropdown: Boolean = false,
        val dayData: TimeRangeData = TimeRangeData(),
        val weekData: TimeRangeData = TimeRangeData(),
        val monthData: TimeRangeData = TimeRangeData()
    ) : com.example.step0724.core.mvi.UiState
    
    /**
     * 用户意图 - 基于需求3的用户交互需求
     */
    sealed class Intent : com.example.step0724.core.mvi.UiIntent {
        // 需求3.9：数据类型下拉菜单选择
        object ToggleDataTypeDropdown : Intent()
        data class SelectDataType(val dataType: DataType) : Intent()
        
        // 日期导航 - 针对特定时间范围
        data class NavigatePrevious(val timeRange: TimeRange) : Intent()
        data class NavigateNext(val timeRange: TimeRange) : Intent()
        
        // 数据刷新
        object RefreshData : Intent()
    }
    
    /**
     * 副作用 - 处理一次性事件
     */
    sealed class SideEffect : com.example.step0724.core.mvi.UiEffect {
        data class ShowError(val message: String) : SideEffect()
    }
}