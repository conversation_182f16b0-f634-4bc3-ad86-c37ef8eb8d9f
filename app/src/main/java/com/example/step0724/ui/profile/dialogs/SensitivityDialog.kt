package com.example.step0724.ui.profile.dialogs

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.step0724.R
import com.example.step0724.data.model.SensitivityLevel

/**
 * 灵敏度设置弹窗 - 基于UI设计图的自定义滑块选择器
 * 四个选择点：Low, Medium Low, Medium High, High
 * 三个标签：Low, Medium, High
 */
@Composable
fun SensitivityDialog(
    currentSensitivity: SensitivityLevel,
    onConfirm: (SensitivityLevel) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedSensitivity by remember { mutableStateOf(currentSensitivity) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "Sensitivity",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.text_primary),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // 描述文字
                Text(
                    text = "The higher the sensitivity, the smaller the movement will be counted as steps.",
                    fontSize = 12.sp,
                    color = colorResource(R.color.text_secondary),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 自定义滑块选择器
                CustomSensitivitySlider(
                    selectedSensitivity = selectedSensitivity,
                    onSensitivityChange = { selectedSensitivity = it },
                    modifier = Modifier.fillMaxWidth()
                        .padding(bottom = 16.dp)
                )

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.light_gray)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                    
                    // 保存按钮
                    Button(
                        onClick = { onConfirm(selectedSensitivity) },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.primary_blue)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Save",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CustomSensitivitySlider(
    selectedSensitivity: SensitivityLevel,
    onSensitivityChange: (SensitivityLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    val sensitivityLevels = listOf(
        SensitivityLevel.LOW,
        SensitivityLevel.MEDIUM_LOW,
        SensitivityLevel.MEDIUM_HIGH,
        SensitivityLevel.HIGH
    )
    
    val selectedIndex = sensitivityLevels.indexOf(selectedSensitivity)
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 自定义滑块绘制
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp)
        ) {
            // 绘制线条和圆圈
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                val width = size.width
                val height = size.height
                val centerY = height / 2
                val circleRadius = 12.dp.toPx()
                val lineStrokeWidth = 4.dp.toPx()
                
                // 计算四个点的位置
                val positions = listOf(
                    width * 0.1f,
                    width * 0.37f,
                    width * 0.63f,
                    width * 0.9f
                )
                
                // 定义颜色
                val primaryBlue = Color(0xFF4893FC) // primary_blue
                val lightGray = Color(0xFFE0ECFB)   // 指定的灰色
                
                // 绘制线条
                for (i in 0 until positions.size - 1) {
                    val startX = positions[i] + circleRadius
                    val endX = positions[i + 1] - circleRadius
                    val color = if (i < selectedIndex) primaryBlue else lightGray
                    
                    drawLine(
                        color = color,
                        start = Offset(startX, centerY),
                        end = Offset(endX, centerY),
                        strokeWidth = lineStrokeWidth,
                        cap = StrokeCap.Round
                    )
                }
                
                // 绘制圆圈
                positions.forEachIndexed { index, x ->
                    val isSelected = index == selectedIndex
                    val isActive = index <= selectedIndex
                    
                    // 外圈（所有圆圈都有外圈）
                    drawCircle(
                        color = if (isActive) primaryBlue else lightGray,
                        radius = circleRadius,
                        center = Offset(x, centerY)
                    )
                    
                    // 内圈（所有圆圈都有白色内圈）
                    drawCircle(
                        color = Color.White,
                        radius = circleRadius * 0.65f, // 白色内圈
                        center = Offset(x, centerY)
                    )
                }
            }
            
            // 透明的点击区域
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                sensitivityLevels.forEachIndexed { index, level ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .clickable { onSensitivityChange(level) }
                    )
                }
            }
        }
        

        // 标签
        Row(
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            // Low标签 (对应第1个点)
            Text(
                text = "Low",
                fontSize = 12.sp,
                color = if (selectedIndex == 0) colorResource(R.color.primary_blue) else colorResource(R.color.text_secondary),
                modifier = Modifier.weight(0.27f),
                textAlign = TextAlign.Start
            )
            
            // Medium标签 (对应第2、3个点)
            Text(
                text = "Medium",
                fontSize = 12.sp,
                color = if (selectedIndex == 1 || selectedIndex == 2) colorResource(R.color.primary_blue) else colorResource(R.color.text_secondary),
                modifier = Modifier.weight(0.46f),
                textAlign = TextAlign.Center
            )
            
            // High标签 (对应第4个点)
            Text(
                text = "High",
                fontSize = 12.sp,
                color = if (selectedIndex == 3) colorResource(R.color.primary_blue) else colorResource(R.color.text_secondary),
                modifier = Modifier.weight(0.27f),
                textAlign = TextAlign.End
            )
        }
    }
}