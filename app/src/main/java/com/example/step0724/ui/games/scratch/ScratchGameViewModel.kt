package com.example.step0724.ui.games.scratch

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.model.*
import com.example.step0724.data.repository.EarnRepository
import com.example.step0724.data.storage.GameStorage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.random.Random

/**
 * 刮刮乐游戏ViewModel
 */
@HiltViewModel
class ScratchGameViewModel @Inject constructor(
    private val earnRepository: EarnRepository,
    private val gameStorage: GameStorage
) : MviViewModel<ScratchGameIntent, ScratchGameState, ScratchGameEffect>() {

    companion object {
        private const val JACKPOT_REWARD = 100  // 顶格奖励
        private const val STANDARD_REWARD = 20  // 标准奖励
        private const val SCRATCH_THRESHOLD = 0.7f  // 刮开阈值
        private const val DAILY_FREE_CHANCES = 3  // 每日免费次数
    }

    override fun setInitialState(): ScratchGameState {
        return ScratchGameState()
    }

    override suspend fun handleIntent(intent: ScratchGameIntent) {
        when (intent) {
            is ScratchGameIntent.InitGame -> initGame()
            is ScratchGameIntent.StartNewGame -> startNewGame()
            is ScratchGameIntent.ScratchCard -> scratchCard(intent.cardId, intent.percentage)
            is ScratchGameIntent.ClaimReward -> claimReward()
            is ScratchGameIntent.NavigateBack -> navigateBack()
        }
    }

    private fun initGame() {
        viewModelScope.launch {
            try {
                setState { copy(isLoading = true) }
                
                val coins = earnRepository.getCoins()
                val usedChances = gameStorage.getScratchGameUsedChances()
                
                setState {
                    copy(
                        coins = coins,
                        usedChances = usedChances,
                        dailyFreeChances = DAILY_FREE_CHANCES,
                        isLoading = false
                    )
                }
                
                // 如果还有免费次数，自动开始新游戏
                if (usedChances < DAILY_FREE_CHANCES) {
                    startNewGame()
                }
            } catch (e: Exception) {
                setState { copy(isLoading = false) }
                setEffect { ScratchGameEffect.ShowMessage("初始化游戏失败") }
            }
        }
    }

    private fun startNewGame() {
        viewModelScope.launch {
            try {
                val currentState = state.value
                
                // 检查是否还有免费次数
                if (currentState.usedChances >= DAILY_FREE_CHANCES) {
                    setEffect { ScratchGameEffect.ShowMessage("今日免费次数已用完") }
                    return@launch
                }
                
                // 生成6张刮刮卡
                val cards = generateScratchCards()
                
                setState {
                    copy(
                        cards = cards,
                        isGameComplete = false,
                        canScratch = true,
                        gameResult = null,
                        showRewardDialog = false
                    )
                }
            } catch (e: Exception) {
                setEffect { ScratchGameEffect.ShowMessage("开始新游戏失败") }
            }
        }
    }

    private fun scratchCard(cardId: Int, percentage: Float) {
        val currentState = state.value
        if (!currentState.canScratch || currentState.isGameComplete) return
        
        val updatedCards = currentState.cards.map { card ->
            if (card.id == cardId) {
                val isScratched = percentage >= SCRATCH_THRESHOLD
                card.copy(
                    scratchedPercentage = percentage,
                    isScratched = isScratched
                )
            } else {
                card
            }
        }
        
        setState { copy(cards = updatedCards) }
        
        // 检查是否所有卡片都已刮开
        if (updatedCards.all { it.isScratched }) {
            completeGame(updatedCards)
        }
    }

    private fun completeGame(cards: List<ScratchCard>) {
        viewModelScope.launch {
            try {
                // 检查游戏结果
                val gameResult = calculateGameResult(cards)
                
                // 更新使用次数
                gameStorage.incrementScratchGameUsedChances()
                
                setState {
                    copy(
                        isGameComplete = true,
                        canScratch = false,
                        gameResult = gameResult,
                        showRewardDialog = true,
                        usedChances = usedChances + 1
                    )
                }
                
                setEffect { ScratchGameEffect.ShowRewardDialog(gameResult) }
            } catch (e: Exception) {
                setEffect { ScratchGameEffect.ShowMessage("游戏完成处理失败") }
            }
        }
    }

    private fun claimReward() {
        viewModelScope.launch {
            try {
                val gameResult = state.value.gameResult ?: return@launch
                
                // 添加金币奖励
                earnRepository.addCoins(gameResult.coinReward)
                val newCoins = earnRepository.getCoins()
                
                setState {
                    copy(
                        coins = newCoins,
                        showRewardDialog = false
                    )
                }
                
                setEffect { ScratchGameEffect.UpdateCoins(newCoins) }
                setEffect { ScratchGameEffect.ShowMessage("恭喜获得 ${gameResult.coinReward} 金币！") }
            } catch (e: Exception) {
                setEffect { ScratchGameEffect.ShowMessage("领取奖励失败") }
            }
        }
    }

    private fun navigateBack() {
        setEffect { ScratchGameEffect.NavigateBack }
    }

    private fun generateScratchCards(): List<ScratchCard> {
        val fruitTypes = FruitType.values()
        
        // 随机决定是否给顶格奖励（概率较低）
        val isJackpot = Random.nextFloat() < 0.1f // 10%概率
        
        return if (isJackpot) {
            // 顶格奖励：所有卡片显示相同水果
            val selectedFruit = fruitTypes.random()
            (0..5).map { id ->
                ScratchCard(
                    id = id,
                    fruitType = selectedFruit,
                    isScratched = false,
                    scratchedPercentage = 0f
                )
            }
        } else {
            // 标准奖励：随机分配不同水果
            (0..5).map { id ->
                ScratchCard(
                    id = id,
                    fruitType = fruitTypes.random(),
                    isScratched = false,
                    scratchedPercentage = 0f
                )
            }
        }
    }

    private fun calculateGameResult(cards: List<ScratchCard>): ScratchGameResult {
        val fruitCounts = cards.groupBy { it.fruitType }.mapValues { it.value.size }
        val maxCount = fruitCounts.values.maxOrNull() ?: 0
        
        return if (maxCount == 6) {
            // 顶格奖励：所有卡片相同
            val matchingFruit = fruitCounts.keys.first()
            ScratchGameResult(
                rewardType = ScratchRewardType.JACKPOT,
                coinReward = JACKPOT_REWARD,
                matchingFruit = matchingFruit
            )
        } else {
            // 标准奖励
            ScratchGameResult(
                rewardType = ScratchRewardType.STANDARD,
                coinReward = STANDARD_REWARD,
                matchingFruit = null
            )
        }
    }
}