package com.example.step0724.ui.components

import android.annotation.SuppressLint
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.unit.Velocity
import kotlin.math.abs
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.data.model.ChartPoint
import com.example.step0724.data.model.DataType
import com.example.step0724.data.model.TimeRange
import kotlin.math.max
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.log10
import kotlin.math.pow
import com.example.step0724.R


/**
 * 自定义折线图组件 - 基于需求3.1的曲线折线图要求
 * 需求3.1：以曲线折线图形式展示运动数据
 * 需求3.6：根据实际情况自动调整Y轴刻度
 * 需求3.8：支持左右滑动查看更多时间范围的数据
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun LineChart(
    chartData: List<ChartPoint>,
    dataType: DataType,
    timeRange: TimeRange,
    modifier: Modifier = Modifier
) {
    // 选中的数据点索引
    var selectedPointIndex by remember { mutableStateOf(-1) }
    val density = LocalDensity.current
    val textMeasurer = rememberTextMeasurer()
    if (chartData.isEmpty()) {
        // 空数据状态
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(
                    MaterialTheme.colorScheme.surfaceVariant,
                    RoundedCornerShape(8.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "暂无数据",
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 14.sp
            )
        }
        return
    }
    
    // 图表区域
    Column {
        Box(
            modifier = modifier.fillMaxSize()
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    
                    .pointerInput(chartData, density) {
                        detectHorizontalDragGestures(
                            onDragStart = { offset ->
                                selectedPointIndex = findNearestDataPoint(
                                    offset = offset,
                                    chartData = chartData,
                                    canvasSize = androidx.compose.ui.geometry.Size(size.width.toFloat(), size.height.toFloat()),
                                    density = density
                                )
                            },
                            onHorizontalDrag = { change, _ ->
                                change.consume()
                                val newSelectedIndex = findNearestDataPoint(
                                    offset = change.position,
                                    chartData = chartData,
                                    canvasSize = androidx.compose.ui.geometry.Size(size.width.toFloat(), size.height.toFloat()),
                                    density = density
                                )
                                if (newSelectedIndex != selectedPointIndex) {
                                    selectedPointIndex = newSelectedIndex
                                }
                            }
                        )
                    }
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            // 检测点击的数据点
                            selectedPointIndex = findNearestDataPoint(
                                offset = offset,
                                chartData = chartData,
                                canvasSize = androidx.compose.ui.geometry.Size(size.width.toFloat(), size.height.toFloat()),
                                density = density
                            )
                        }
                    }
            ) {
                drawLineChart(
                    chartData = chartData,
                    dataType = dataType,
                    timeRange = timeRange,
                    selectedPointIndex = selectedPointIndex
                )
            }

            // 选中数据点的标注
            if (selectedPointIndex >= 0 && selectedPointIndex < chartData.size) {
                val selectedPoint = chartData[selectedPointIndex]

                // 使用BoxWithConstraints来获取容器尺寸
                BoxWithConstraints {
                    val containerWidth = maxWidth
                    val containerHeight = maxHeight

                    // 计算选中点在屏幕上的位置
                    val pointPosition = calculatePointPosition(
                        chartData = chartData,
                        pointIndex = selectedPointIndex,
                        canvasSize = androidx.compose.ui.geometry.Size(
                            containerWidth.value * density.density,
                            containerHeight.value * density.density
                        ),
                        density = density
                    )

                    // 计算标注位置（优先显示在下方，空间不够时显示在上方）
                    val tooltipHeight = 40.dp
                    val pointYDp = (pointPosition.y / density.density).dp
                    val bottomSpace = containerHeight - pointYDp - 60.dp // 考虑X轴标签空间
                    val showBelow = bottomSpace >= tooltipHeight

                    // 精确测量标签文本的宽度
                    val labelText = formatValue(selectedPoint.y.toDouble(), dataType)
                    val textStyle = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                    val textLayoutResult = textMeasurer.measure(labelText, textStyle)
                    val textWidthDp = with(density) { textLayoutResult.size.width.toDp() }

                    // 计算标注的总宽度（文本宽度 + padding）
                    val tooltipPadding = 16.dp // 8dp * 2 (左右padding)
                    val tooltipWidth = textWidthDp + tooltipPadding

                    // 计算标注的精确居中位置
                    val pointXDp = (pointPosition.x / density.density).dp
                    val centeredX = pointXDp - (tooltipWidth / 2)

                    // 确保标注不超出边界
                    val finalX = maxOf(
                        0.dp,
                        minOf(centeredX, containerWidth - tooltipWidth)
                    )

                    DataPointTooltip(
                        value = selectedPoint.y,
                        dataType = dataType,
                        modifier = Modifier
                            .offset(
                                x = finalX,
                                y = if (showBelow) {
                                    pointYDp + 15.dp // 显示在下方
                                } else {
                                    maxOf(0.dp, pointYDp - 30.dp) // 显示在上方
                                }
                            )
                    )
                }
            }
        }
        // X轴标签
        XAxisLabels(
            chartData = chartData,
            timeRange = timeRange,
            modifier = Modifier
                .fillMaxWidth()
                .offset(y = (-10).dp)
        )
    }
}

/**
 * 查找最近的数据点（使用与Canvas绘制相同的坐标系统）
 */
private fun findNearestDataPoint(
    offset: Offset,
    chartData: List<ChartPoint>,
    canvasSize: androidx.compose.ui.geometry.Size,
    density: androidx.compose.ui.unit.Density
): Int {
    if (chartData.isEmpty()) return -1
    
    // 使用与Canvas绘制相同的padding计算方式
    val padding = with(density) { 20.dp.toPx() }
    val chartWidth = canvasSize.width - padding * 2
    
    var nearestIndex = -1
    var minDistance = Float.MAX_VALUE
    
    chartData.forEachIndexed { index, _ ->
        val x = padding + (index.toFloat() / (chartData.size - 1).coerceAtLeast(1)) * chartWidth
        val distance = kotlin.math.abs(offset.x - x)
        
        val clickTolerance = with(density) { 30.dp.toPx() } // 使用dp单位的容差
        if (distance < minDistance && distance < clickTolerance) {
            minDistance = distance
            nearestIndex = index
        }
    }
    
    return nearestIndex
}

/**
 * 计算数据点在屏幕上的位置（与Canvas绘制使用相同的坐标系统）
 */
private fun calculatePointPosition(
    chartData: List<ChartPoint>,
    pointIndex: Int,
    canvasSize: androidx.compose.ui.geometry.Size,
    density: androidx.compose.ui.unit.Density
): Offset {
    if (pointIndex < 0 || pointIndex >= chartData.size) return Offset.Zero
    
    // 使用与Canvas绘制相同的padding计算方式
    val padding = with(density) { 20.dp.toPx() }
    val chartWidth = canvasSize.width - padding * 2
    val chartHeight = canvasSize.height - padding * 2
    
    val maxY = chartData.maxOfOrNull { it.y } ?: 0f
    val minY = chartData.minOfOrNull { it.y } ?: 0f
    val yRange = maxY - minY
    val adjustedMaxY = if (yRange == 0f) maxY + 1f else maxY + yRange * 0.1f
    val adjustedMinY = kotlin.math.max(0f, minY - yRange * 0.1f)
    
    val point = chartData[pointIndex]
    val x = padding + (pointIndex.toFloat() / (chartData.size - 1).coerceAtLeast(1)) * chartWidth
    val y = padding + chartHeight - ((point.y - adjustedMinY) / (adjustedMaxY - adjustedMinY)) * chartHeight
    
    return Offset(x, y)
}

/**
 * 绘制折线图的核心逻辑
 */
private fun DrawScope.drawLineChart(
    chartData: List<ChartPoint>,
    dataType: DataType,
    timeRange: TimeRange,
    selectedPointIndex: Int = -1
) {
    if (chartData.isEmpty()) return
    
    val padding = 20.dp.toPx()
    val chartWidth = size.width - padding * 2
    val chartHeight = size.height - padding * 2
    
    // 需求3.6：根据实际情况自动调整Y轴刻度
    val maxY = chartData.maxOfOrNull { it.y } ?: 0f
    val minY = chartData.minOfOrNull { it.y } ?: 0f
    val yRange = maxY - minY
    val adjustedMaxY = if (yRange == 0f) maxY + 1f else maxY + yRange * 0.1f
    val adjustedMinY = max(0f, minY - yRange * 0.1f)
    
    // 绘制网格线
    drawGrid(
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        padding = padding,
        maxY = adjustedMaxY,
        minY = adjustedMinY
    )
    
    // 绘制Y轴标签
    drawYAxisLabels(
        chartHeight = chartHeight,
        padding = padding,
        maxY = adjustedMaxY,
        minY = adjustedMinY,
        dataType = dataType
    )
    
    // 计算数据点坐标
    val points = chartData.mapIndexed { index, point ->
        val x = padding + (index.toFloat() / (chartData.size - 1).coerceAtLeast(1)) * chartWidth
        val y = padding + chartHeight - ((point.y - adjustedMinY) / (adjustedMaxY - adjustedMinY)) * chartHeight
        Offset(x, y)
    }
    
    // 绘制折线
    drawLineWithCurve(points)
    
    // 绘制数据点
    drawDataPoints(points, selectedPointIndex, timeRange, chartData.size)
}

/**
 * 绘制网格线
 */
private fun DrawScope.drawGrid(
    chartWidth: Float,
    chartHeight: Float,
    padding: Float,
    maxY: Float,
    minY: Float
) {
    val gridColor = Color(0xFFF4F5F7)
    val strokeWidth = 1.dp.toPx()
    
    // 水平网格线（Y轴）- 与刻度对应
    val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
    
    // 计算均匀的刻度
    val ticks = calculateNiceYAxisTicks(minY, maxY)
    val actualMinY = ticks.minOrNull() ?: minY
    val actualMaxY = ticks.maxOrNull() ?: maxY

    ticks.forEach { tickValue ->
        // 计算网格线在图表中的位置
        val normalizedPosition = if (actualMaxY == actualMinY) {
            0.5f // 如果范围为0，放在中间
        } else {
            (actualMaxY - tickValue) / (actualMaxY - actualMinY)
        }
        
        val y = padding + normalizedPosition * chartHeight
        val path = Path().apply {
            moveTo(padding, y)
            lineTo(padding + chartWidth, y)
        }

        drawPath(
            path = path,
            color = gridColor,
            style = Stroke(
                width = strokeWidth,
                pathEffect = pathEffect
            )
        )
    }
    
//    // 垂直网格线（X轴）
//    for (i in 0..6) {
//        val x = padding + (i.toFloat() / 6f) * chartWidth
//        drawLine(
//            color = gridColor,
//            start = Offset(x, padding),
//            end = Offset(x, padding + chartHeight),
//            strokeWidth = strokeWidth
//        )
//    }
}

/**
 * 计算均匀的Y轴刻度
 */
private fun calculateNiceYAxisTicks(minValue: Float, maxValue: Float, tickCount: Int = 5): List<Float> {
    // 检查是否为小范围或相等值的情况
    val dataRange = maxValue - minValue
    val isSmallRange = dataRange <= 1f || minValue == maxValue
    
    if (isSmallRange) {
        // 如果范围很小或相等，创建一个合理的刻度范围
        return if (minValue <= 1f && maxValue <= 1f) {
            // 对于接近0或小于1的值，使用标准的0-5范围
            listOf(0f, 1f, 2f, 3f, 4f, 5f).take(tickCount)
        } else {
            // 对于较大的值，以最小值为基础创建合理范围
            val baseValue = minValue.toInt().toFloat()
            val step = when {
                baseValue < 10f -> 1f
                baseValue < 100f -> 10f
                baseValue < 1000f -> 100f
                else -> 1000f
            }
            
            // 创建从baseValue开始的递增序列
            (0 until tickCount).map { i ->
                baseValue + i * step
            }
        }
    }
    
    val range = dataRange
    val roughStep = range / (tickCount - 1)
    
    // 计算一个"好看"的步长
    val magnitude = 10.0.pow(floor(log10(roughStep.toDouble()))).toFloat()
    val normalizedStep = roughStep / magnitude
    
    val niceStep = when {
        normalizedStep <= 1f -> magnitude
        normalizedStep <= 2f -> 2f * magnitude
        normalizedStep <= 5f -> 5f * magnitude
        else -> 10f * magnitude
    }
    
    // 计算起始值和结束值
    val niceMin = floor(minValue / niceStep) * niceStep
    val niceMax = ceil(maxValue / niceStep) * niceStep
    
    // 生成刻度列表
    val ticks = mutableListOf<Float>()
    var currentTick = niceMin
    while (currentTick <= niceMax + niceStep * 0.1f) { // 添加小的容差
        ticks.add(currentTick)
        currentTick += niceStep
    }
    
    // 确保至少有指定数量的刻度且没有重复值
    if (ticks.size < tickCount) {
        // 如果刻度太少，使用更小的步长
        val smallerStep = niceStep / 2f
        ticks.clear()
        currentTick = floor(minValue / smallerStep) * smallerStep
        val smallerMax = ceil(maxValue / smallerStep) * smallerStep
        while (currentTick <= smallerMax + smallerStep * 0.1f) {
            ticks.add(currentTick)
            currentTick += smallerStep
        }
    }
    
    // 去除重复值并确保至少有2个不同的刻度
    val uniqueTicks = ticks.distinct()
    return if (uniqueTicks.size >= 2) {
        uniqueTicks.take(6) // 最多6个刻度，避免过于密集
    } else {
        // 如果去重后刻度太少，强制创建不同的刻度
        val baseValue = uniqueTicks.firstOrNull() ?: 0f
        val step = if (baseValue == 0f) 1f else kotlin.math.abs(baseValue) * 0.2f
        listOf(baseValue, baseValue + step, baseValue + step * 2, baseValue + step * 3, baseValue + step * 4)
    }
}

/**
 * 绘制Y轴标签
 */
private fun DrawScope.drawYAxisLabels(
    chartHeight: Float,
    padding: Float,
    maxY: Float,
    minY: Float,
    dataType: DataType
) {
    val labelColor = Color.Gray
    val textSize = 10.sp.toPx()
    
    // 计算均匀的刻度
    val ticks = calculateNiceYAxisTicks(minY, maxY)
    val actualMinY = ticks.minOrNull() ?: minY
    val actualMaxY = ticks.maxOrNull() ?: maxY
    
    ticks.forEachIndexed { index, tickValue ->
        // 计算刻度在图表中的位置
        val normalizedPosition = if (actualMaxY == actualMinY) {
            0.5f // 如果范围为0，放在中间
        } else {
            (actualMaxY - tickValue) / (actualMaxY - actualMinY)
        }
        
        val y = padding + normalizedPosition * chartHeight
        val formattedValue = formatYAxisValue(tickValue, dataType)
        
        drawContext.canvas.nativeCanvas.drawText(
            formattedValue,
            padding - 8.dp.toPx(),
            y + textSize / 2,
            android.graphics.Paint().apply {
                color = labelColor.toArgb()
                this.textSize = textSize
                textAlign = android.graphics.Paint.Align.RIGHT
            }
        )
    }
}

/**
 * 绘制平滑曲线，特别处理0值避免穿到0轴以下
 */
private fun DrawScope.drawLineWithCurve(points: List<Offset>) {
    if (points.size < 2) return
    
    val path = Path()
    val strokeColor = Color(0xFF2196F3) // 蓝色主题色
    val strokeWidth = 3.dp.toPx()
    
    // 获取图表底部Y坐标（0轴位置）
    val chartBottom = size.height - 20.dp.toPx() // 考虑padding
    
    // 移动到第一个点
    path.moveTo(points[0].x, points[0].y)
    
    if (points.size == 2) {
        // 只有两个点时直接连线
        path.lineTo(points[1].x, points[1].y)
    } else {
        // 使用改进的曲线绘制算法
        for (i in 1 until points.size) {
            val currentPoint = points[i]
            val previousPoint = points[i - 1]
            
            // 检查是否需要特殊处理0值
            val shouldUseSpecialHandling = isZeroValueSegment(points, i)
            
            if (shouldUseSpecialHandling) {
                // 对于0值段，使用水平连接或轻微曲线
                drawZeroValueSegment(path, previousPoint, currentPoint, chartBottom)
            } else {
                // 正常的曲线绘制
                drawNormalCurveSegment(path, points, i)
            }
        }
    }
    
    drawPath(
        path = path,
        color = strokeColor,
        style = Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round,
            join = StrokeJoin.Round
        )
    )
}

/**
 * 检查当前段是否为0值段
 */
private fun DrawScope.isZeroValueSegment(points: List<Offset>, currentIndex: Int): Boolean {
    val currentPoint = points[currentIndex]
    val previousPoint = points[currentIndex - 1]
    
    // 计算图表的实际底部位置（0轴）
    val padding = 20.dp.toPx()
    val chartHeight = size.height - padding * 2
    val chartBottom = padding + chartHeight
    
    // 如果当前点和前一个点都接近底部（0值），则认为是0值段
    val threshold = 3.dp.toPx() // 较小的阈值，更精确地识别0值
    return (currentPoint.y >= chartBottom - threshold) && (previousPoint.y >= chartBottom - threshold)
}

/**
 * 绘制0值段，确保不穿到0轴以下
 */
private fun DrawScope.drawZeroValueSegment(
    path: Path,
    previousPoint: Offset,
    currentPoint: Offset,
    chartBottom: Float
) {
    // 对于0值段，使用接近水平的连接
    if (abs(previousPoint.y - currentPoint.y) < 2.dp.toPx()) {
        // 如果两点Y坐标非常接近，直接水平连线
        path.lineTo(currentPoint.x, currentPoint.y)
    } else {
        // 使用非常轻微的曲线，确保不会穿到0轴以下
        val midX = (previousPoint.x + currentPoint.x) / 2
        val minY = kotlin.math.min(previousPoint.y, currentPoint.y) // 使用较高的点作为控制点
        
        // 控制点稍微向上偏移，确保曲线不会下沉
        val controlY = minY - 1.dp.toPx()
        
        path.quadraticTo(
            midX, controlY,
            currentPoint.x, currentPoint.y
        )
    }
}

/**
 * 绘制正常的曲线段
 */
private fun DrawScope.drawNormalCurveSegment(
    path: Path,
    points: List<Offset>,
    currentIndex: Int
) {
    val currentPoint = points[currentIndex]
    val previousPoint = points[currentIndex - 1]
    
    // 计算控制点以创建平滑曲线
    val controlPoint1: Offset
    val controlPoint2: Offset
    
    // 使用更平滑的控制点计算方法
    val tension = 0.25f // 曲线张力，值越小曲线越平滑
    
    if (currentIndex == 1) {
        // 第一段曲线
        val nextPoint = if (currentIndex + 1 < points.size) points[currentIndex + 1] else currentPoint
        controlPoint1 = Offset(
            previousPoint.x + (currentPoint.x - previousPoint.x) * tension,
            previousPoint.y + (currentPoint.y - previousPoint.y) * tension
        )
        controlPoint2 = Offset(
            currentPoint.x - (nextPoint.x - previousPoint.x) * tension * 0.5f,
            currentPoint.y - (nextPoint.y - previousPoint.y) * tension * 0.5f
        )
    } else if (currentIndex == points.size - 1) {
        // 最后一段曲线
        val prevPrevPoint = points[currentIndex - 2]
        controlPoint1 = Offset(
            previousPoint.x + (currentPoint.x - prevPrevPoint.x) * tension * 0.5f,
            previousPoint.y + (currentPoint.y - prevPrevPoint.y) * tension * 0.5f
        )
        controlPoint2 = Offset(
            currentPoint.x - (currentPoint.x - previousPoint.x) * tension,
            currentPoint.y - (currentPoint.y - previousPoint.y) * tension
        )
    } else {
        // 中间段曲线
        val prevPrevPoint = points[currentIndex - 2]
        val nextPoint = points[currentIndex + 1]
        controlPoint1 = Offset(
            previousPoint.x + (currentPoint.x - prevPrevPoint.x) * tension,
            previousPoint.y + (currentPoint.y - prevPrevPoint.y) * tension
        )
        controlPoint2 = Offset(
            currentPoint.x - (nextPoint.x - previousPoint.x) * tension,
            currentPoint.y - (nextPoint.y - previousPoint.y) * tension
        )
    }
    
    // 使用三次贝塞尔曲线
    path.cubicTo(
        controlPoint1.x, controlPoint1.y,
        controlPoint2.x, controlPoint2.y,
        currentPoint.x, currentPoint.y
    )
}

/**
 * 绘制数据点，支持选中状态
 * 对于month维度，平时最多显示7个数据点（第一个、最后一个必须显示，中间平铺）
 * 但点击时所有数据点都可以被选中和显示
 */
private fun DrawScope.drawDataPoints(
    points: List<Offset>, 
    selectedPointIndex: Int = -1,
    timeRange: TimeRange,
    totalDataCount: Int
) {
    val pointColor = Color(0xFF2196F3)
    val pointRadius = 3.dp.toPx()
    val selectedPointRadius = 5.dp.toPx()
    
    // 计算需要显示的数据点索引
    val visiblePointIndices = if (timeRange == TimeRange.MONTH && totalDataCount > 7) {
        // month维度且数据点超过7个时，只显示7个数据点
        val maxVisiblePoints = 7
        val indices = mutableSetOf<Int>()
        
        // 第一个和最后一个必须显示
        indices.add(0)
        indices.add(totalDataCount - 1)
        
        // 中间点平铺分布
        if (totalDataCount > 2) {
            val remainingPoints = maxVisiblePoints - 2 // 除去首尾两个点
            for (i in 1 until remainingPoints + 1) {
                val index = (i * (totalDataCount - 1) / (maxVisiblePoints - 1))
                if (index > 0 && index < totalDataCount - 1) {
                    indices.add(index)
                }
            }
        }
        
        indices.toList().sorted()
    } else {
        // 其他情况显示所有数据点
        (0 until points.size).toList()
    }
    
    points.forEachIndexed { index, point ->
        val isSelected = index == selectedPointIndex
        val shouldShowPoint = isSelected || visiblePointIndices.contains(index)
        
        if (shouldShowPoint) {
            if (isSelected) {
                // 绘制选中的数据点（空心圆）
                drawCircle(
                    color = Color.White,
                    radius = selectedPointRadius + 2.dp.toPx(),
                    center = point
                )
                drawCircle(
                    color = pointColor,
                    radius = selectedPointRadius + 2.dp.toPx(),
                    center = point,
                    style = Stroke(width = 3.dp.toPx())
                )
                drawCircle(
                    color = Color.White,
                    radius = selectedPointRadius,
                    center = point
                )
            } else {
                // 绘制普通数据点（实心圆）
                drawCircle(
                    color = Color.White,
                    radius = pointRadius + 1.dp.toPx(),
                    center = point
                )
                drawCircle(
                    color = pointColor,
                    radius = pointRadius,
                    center = point
                )
            }
        }
    }
}

/**
 * X轴标签组件
 */
@Composable
private fun XAxisLabels(
    chartData: List<ChartPoint>,
    timeRange: TimeRange,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // 根据时间范围显示不同数量的标签
        val labelIndices = when (timeRange) {
            TimeRange.DAY -> {
                // 显示7个时间点：0, 4, 8, 12, 16, 20, 24 (包含24)
                if (chartData.size >= 24) {
                    listOf(0, 4, 8, 12, 16, 20, 23) // 23对应24:00，因为数组索引从0开始
                } else {
                    // 如果数据不足24个，按比例分配
                    val step = if (chartData.size > 1) (chartData.size - 1) / 6 else 1
                    (0..6).map { i -> (i * step).coerceAtMost(chartData.size - 1) }
                }
            }
            TimeRange.WEEK -> {
                // 显示7天，确保显示所有天
                (0 until chartData.size.coerceAtMost(7)).toList()
            }
            TimeRange.MONTH -> {
                // 确保显示第1天和最后一天，中间均匀分布
                if (chartData.size <= 7) {
                    // 如果数据点不多，显示所有
                    (0 until chartData.size).toList()
                } else {
                    // 确保包含第一天(索引0)和最后一天(索引size-1)
                    val indices = mutableListOf<Int>()
                    indices.add(0) // 第1天
                    
                    // 中间均匀分布5个点
                    val middleCount = 5
                    val step = (chartData.size - 1).toFloat() / (middleCount + 1)
                    for (i in 1..middleCount) {
                        val index = (i * step).toInt().coerceIn(1, chartData.size - 2)
                        if (!indices.contains(index)) {
                            indices.add(index)
                        }
                    }
                    
                    indices.add(chartData.size - 1) // 最后一天
                    indices.sorted().distinct()
                }
            }
        }
        
        labelIndices.forEach { index ->
            val label = if (index < chartData.size) {
                when (timeRange) {
                    TimeRange.DAY -> {
                        // 对于DAY，如果是最后一个索引(23)，显示"24:00"
                        if (index == 23 && chartData.size >= 24) {
                            "24:00"
                        } else {
                            chartData[index].label
                        }
                    }
                    else -> chartData[index].label
                }
            } else ""
            
            Text(
                text = label,
                fontSize = 10.sp,
                color = colorResource(R.color.text_primary),
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 数据点工具提示组件
 */
@Composable
private fun DataPointTooltip(
    value: Float,
    dataType: DataType,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color(0xFF2196F3)),
        shape = RoundedCornerShape(6.dp)
    ) {
        Text(
            text = formatValue(value.toDouble(), dataType),
            color = Color.White,
            fontSize = 10.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(horizontal = 8.dp)
        )
    }
}

// 辅助函数
private fun getChartTitle(dataType: DataType, timeRange: TimeRange): String {
    val dataTypeName = when (dataType) {
        DataType.STEPS -> "步数"
        DataType.DISTANCE -> "距离"
        DataType.CALORIES -> "卡路里"
        DataType.TIME -> "运动时间"
    }
    
    val timeRangeName = when (timeRange) {
        TimeRange.DAY -> "今日"
        TimeRange.WEEK -> "本周"
        TimeRange.MONTH -> "本月"
    }
    
    return "$timeRangeName$dataTypeName"
}

private fun formatYAxisValue(value: Float, dataType: DataType): String {
    return when (dataType) {
        DataType.STEPS -> "${value.toInt()}"
        DataType.DISTANCE -> String.format("%.1f", value)
        DataType.CALORIES -> "${value.toInt()}"
        DataType.TIME -> "${value.toInt()}"
    }
}

private fun formatValue(value: Double, dataType: DataType): String {
    return when (dataType) {
        DataType.STEPS -> "${value.toInt()}"
        DataType.DISTANCE -> String.format("%.2f km", value)
        DataType.CALORIES -> "${value.toInt()} cal"
        DataType.TIME -> "${value.toInt()} min"
    }
}