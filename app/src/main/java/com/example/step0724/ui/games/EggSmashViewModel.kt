package com.example.step0724.ui.games

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.step0724.data.storage.GameStorage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 敲金蛋游戏的ViewModel
 * 管理游戏状态和每日次数
 */
class EggSmashViewModel(application: Application) : AndroidViewModel(application) {
    
    private val gameStorage = GameStorage(application)
    
    private val _dailyChances = MutableStateFlow(0)
    val dailyChances: StateFlow<Int> = _dailyChances.asStateFlow()
    
    private val _canPlay = MutableStateFlow(true)
    val canPlay: StateFlow<Boolean> = _canPlay.asStateFlow()
    
    init {
        updateDailyChances()
    }
    
    /**
     * 更新每日次数
     */
    private fun updateDailyChances() {
        viewModelScope.launch {
            val chances = gameStorage.getDailyChances()
            _dailyChances.value = chances
            _canPlay.value = chances > 0
        }
    }
    
    /**
     * 开始游戏（消耗一次机会）
     */
    fun startGame(): Boolean {
        return if (gameStorage.consumeChance()) {
            updateDailyChances()
            true
        } else {
            false
        }
    }
    
    /**
     * 刷新次数状态
     */
    fun refreshChances() {
        updateDailyChances()
    }
}