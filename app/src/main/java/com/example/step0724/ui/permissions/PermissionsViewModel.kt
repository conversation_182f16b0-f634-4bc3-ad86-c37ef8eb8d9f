package com.example.step0724.ui.permissions

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.permission.PermissionManager
import com.example.step0724.data.permission.PermissionResultCallback
import com.example.step0724.data.permission.PermissionResultManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PermissionsViewModel @Inject constructor(
    private val permissionManager: PermissionManager
) : MviViewModel<PermissionsIntent, PermissionsUiState, PermissionsEffect>(), PermissionResultCallback {

    override fun setInitialState(): PermissionsUiState = PermissionsUiState()

    init {
        sendIntent(PermissionsIntent.LoadPermissions)
        // 注册权限结果回调
        PermissionResultManager.setCallback(this)
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清除权限结果回调
        PermissionResultManager.setCallback(null)
    }

    override suspend fun handleIntent(intent: PermissionsIntent) {
        when (intent) {
            is PermissionsIntent.LoadPermissions -> {
                loadPermissionStates()
            }
            
            is PermissionsIntent.RequestActivityRecognition -> {
                android.util.Log.d("PermissionsViewModel", "RequestActivityRecognition intent received")
                if (!state.value.hasActivityRecognition) {
                    // 先显示权限说明弹窗
                    setState { 
                        copy(
                            showPermissionExplanationDialog = true,
                            currentExplanationPermission = PermissionExplanationType.ACTIVITY_RECOGNITION
                        ) 
                    }
                }
            }
            
            is PermissionsIntent.RequestBatteryOptimization -> {
                android.util.Log.d("PermissionsViewModel", "RequestBatteryOptimization intent received")
                if (!state.value.hasBatteryOptimization) {
                    // 先显示权限说明弹窗
                    setState { 
                        copy(
                            showPermissionExplanationDialog = true,
                            currentExplanationPermission = PermissionExplanationType.BATTERY_OPTIMIZATION
                        ) 
                    }
                }
            }
            
            is PermissionsIntent.RequestNotificationPermission -> {
                android.util.Log.d("PermissionsViewModel", "RequestNotificationPermission intent received")
                if (!state.value.hasNotificationPermission) {
                    // 没有系统权限，先显示权限说明弹窗
                    setState { 
                        copy(
                            showPermissionExplanationDialog = true,
                            currentExplanationPermission = PermissionExplanationType.NOTIFICATION
                        ) 
                    }
                } else {
                    // 已有系统权限，切换通知开关状态
                    toggleNotificationSetting(!state.value.isNotificationEnabled)
                }
            }
            
            is PermissionsIntent.DismissPermissionExplanationDialog -> {
                setState { 
                    copy(
                        showPermissionExplanationDialog = false,
                        currentExplanationPermission = null
                    ) 
                }
            }
            
            is PermissionsIntent.ConfirmPermissionExplanation -> {
                val permissionType = state.value.currentExplanationPermission
                setState { 
                    copy(
                        showPermissionExplanationDialog = false,
                        currentExplanationPermission = null
                    ) 
                }
                
                // 根据权限类型发送对应的Effect
                when (permissionType) {
                    PermissionExplanationType.ACTIVITY_RECOGNITION -> {
                        setEffect { PermissionsEffect.RequestActivityRecognitionPermission }
                    }
                    PermissionExplanationType.BATTERY_OPTIMIZATION -> {
                        setEffect { PermissionsEffect.RequestBatteryOptimizationPermission }
                    }
                    PermissionExplanationType.NOTIFICATION -> {
                        setEffect { PermissionsEffect.RequestNotificationPermission }
                    }
                    null -> {
                        // 不应该发生
                    }
                }
            }
            
            is PermissionsIntent.ToggleNotificationPermission -> {
                toggleNotificationSetting(intent.enabled)
            }
        }
    }

    private fun loadPermissionStates() {
        viewModelScope.launch {
            setState { copy(isLoading = true) }
            
            try {
                val hasActivityRecognition = permissionManager.hasActivityRecognitionPermission()
                val hasBatteryOptimization = permissionManager.hasBatteryOptimizationPermission()
                val hasNotificationPermission = permissionManager.hasNotificationPermission()
                val isNotificationEnabled = permissionManager.isNotificationEnabled()
                
                setState {
                    copy(
                        isLoading = false,
                        hasActivityRecognition = hasActivityRecognition,
                        hasBatteryOptimization = hasBatteryOptimization,
                        hasNotificationPermission = hasNotificationPermission,
                        isNotificationEnabled = isNotificationEnabled,
                        canToggleNotification = hasNotificationPermission
                    )
                }
            } catch (e: Exception) {
                setState { copy(isLoading = false) }
                setEffect { PermissionsEffect.ShowError("Failed to load permissions: ${e.message}") }
            }
        }
    }

    private fun toggleNotificationSetting(enabled: Boolean) {
        viewModelScope.launch {
            try {
                // 更新通知设置状态
                permissionManager.setNotificationEnabled(enabled)
                android.util.Log.d("PermissionsViewModel", "Notification setting toggled to: $enabled")
                
                setState { 
                    copy(isNotificationEnabled = enabled) 
                }
            } catch (e: Exception) {
                setEffect { PermissionsEffect.ShowError("Failed to toggle notification setting: ${e.message}") }
            }
        }
    }


    
    fun refreshPermissions() {
        sendIntent(PermissionsIntent.LoadPermissions)
    }
    
    /**
     * 实现PermissionResultCallback接口
     * 在权限结果返回时立即更新UI状态
     */
    override fun onPermissionResult(permission: String, granted: Boolean) {
        android.util.Log.d("PermissionsViewModel", "Permission result received: $permission = $granted")
        viewModelScope.launch {
            when (permission) {
                android.Manifest.permission.ACTIVITY_RECOGNITION -> {
                    setState { copy(hasActivityRecognition = granted) }
                }
                android.Manifest.permission.POST_NOTIFICATIONS -> {
                    val isNotificationEnabled = permissionManager.isNotificationEnabled()
                    setState { 
                        copy(
                            hasNotificationPermission = granted,
                            isNotificationEnabled = isNotificationEnabled,
                            canToggleNotification = granted
                        ) 
                    }
                }
            }
        }
    }
}