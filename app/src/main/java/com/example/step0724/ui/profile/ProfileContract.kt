package com.example.step0724.ui.profile

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.data.model.*

/**
 * Profile页面的MVI契约 - 基于需求4的Profile页面要求
 */
object ProfileContract {
    
    /**
     * UI状态 - 基于需求4的Profile页面状态
     */
    data class State(
        val userSettings: UserSettings = UserSettings(),
        val isLoading: Boolean = false,
        val showStepGoalDialog: Boolean = false,
        val showSensitivityDialog: Boolean = false,
        val showGenderDialog: Boolean = false,
        val showWeightDialog: Boolean = false,
        val showHeightDialog: Boolean = false,
        val showStepLengthDialog: Boolean = false,
        val showUnitSystemDialog: Boolean = false
    ) : UiState
    
    /**
     * UI事件 - 基于需求4的用户交互
     */
    sealed class Intent : UiIntent {
        // 需求4.1：设置项点击事件
        object OnStepGoalClick : Intent()
        object OnSensitivityClick : Intent()
        object OnGenderClick : Intent()
        object OnWeightClick : Intent()
        object OnHeightClick : Intent()
        object OnStepLengthClick : Intent()
        object OnUnitSystemClick : Intent()
        
        // 弹窗关闭事件
        object OnDismissDialog : Intent()
        
        // 设置更新事件
        data class OnUpdateStepGoal(val goal: Int) : Intent()
        data class OnUpdateSensitivity(val level: SensitivityLevel) : Intent()
        data class OnUpdateGender(val gender: Gender) : Intent()
        data class OnUpdateWeight(val weight: Double) : Intent()
        data class OnUpdateHeight(val height: Double) : Intent()
        data class OnUpdateStepLength(val length: Double) : Intent()
        data class OnUpdateUnitSystem(val system: UnitSystem) : Intent()
    }
    
    /**
     * UI副作用 - 基于需求4的副作用处理
     */
    sealed class Effect : UiEffect {
        data class ShowError(val message: String) : Effect()
        object SettingsSaved : Effect()
    }
}