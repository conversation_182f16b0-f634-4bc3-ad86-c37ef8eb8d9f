package com.example.step0724.ui.games.scratch

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.step0724.R
import com.example.step0724.data.model.ScratchRewardType

/**
 * 刮刮乐游戏界面 - 严格按照设计图实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScratchGameScreen(
    onNavigateBack: () -> Unit = {},
    viewModel: ScratchGameViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    
    // 处理副作用
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is ScratchGameEffect.NavigateBack -> onNavigateBack()
                is ScratchGameEffect.ShowRewardDialog -> {
                    // 奖励对话框已在UI中处理
                }
                is ScratchGameEffect.ShowMessage -> {
                    // 可以添加Toast或Snackbar显示消息
                }
                is ScratchGameEffect.UpdateCoins -> {
                    // 金币更新已在状态中处理
                }
            }
        }
    }
    
    // 初始化游戏
    LaunchedEffect(Unit) {
        viewModel.sendIntent(ScratchGameIntent.InitGame)
    }
    
    // 主背景 - 使用指定的背景图片
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(id = R.drawable.bg_scratch),
                contentScale = ContentScale.Crop
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Lucky Scratch 标题 - 使用指定的标题图片
            Image(
                painter = painterResource(id = R.drawable.title_scratch),
                contentDescription = "Lucky Scratch",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            


            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 游戏区域 - 使用指定的刮刮卡列表背景
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .paint(
                        painter = painterResource(id = R.mipmap.sc_bg_ku),
                        contentScale = ContentScale.FillBounds
                    )
            ) {
                Text(
                    text = "Reward according to the number of the same pattern",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    textAlign = TextAlign.Center,
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )


                Column(
                    modifier = Modifier.padding(24.dp)
                        .align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (state.isLoading) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(280.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                color = Color(0xFFFF69B4)
                            )
                        }
                    } else {
                        // 3x2 刮刮卡网格
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(3),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalArrangement = Arrangement.spacedBy(16.dp),
                            modifier = Modifier.height(240.dp)
                        ) {
                            items(state.cards) { card ->
                                ScratchCardView(
                                    card = card,
                                    onScratch = { percentage ->
                                        viewModel.sendIntent(
                                            ScratchGameIntent.ScratchCard(card.id, percentage)
                                        )
                                    },
                                    modifier = Modifier.size(100.dp)
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 每日免费次数显示 - 粉色圆角背景
                    Card(
                        shape = RoundedCornerShape(20.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFFFB6C1).copy(alpha = 0.3f)
                        )
                    ) {
                        Text(
                            text = "Daily free chances: ${state.dailyFreeChances - state.usedChances}",
                            modifier = Modifier.padding(horizontal = 20.dp, vertical = 8.dp),
                            fontSize = 14.sp,
                            color = Color(0xFFFF69B4),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 开始新游戏按钮
            if (state.isGameComplete || state.cards.isEmpty()) {
                Button(
                    onClick = { viewModel.sendIntent(ScratchGameIntent.StartNewGame) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp)
                        .height(56.dp),
                    enabled = state.usedChances < state.dailyFreeChances,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFFF69B4),
                        disabledContainerColor = Color.Gray
                    ),
                    shape = RoundedCornerShape(28.dp)
                ) {
                    Text(
                        text = if (state.usedChances >= state.dailyFreeChances) "今日次数已用完" else "开始新游戏",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
        }
        
        // 装饰性金币图标 - 左上角
        Box(
            modifier = Modifier
                .offset(x = 20.dp, y = 100.dp)
                .size(24.dp)
                .background(
                    Color(0xFFFFD700),
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        )
        
        // 装饰性金币图标 - 右上角
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .offset(x = (-40).dp, y = 120.dp),
            contentAlignment = Alignment.TopEnd
        ) {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(
                        Color(0xFFFFD700),
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
            )
        }
        
        // 装饰性金币图标 - 左侧中部
        Box(
            modifier = Modifier
                .offset(x = 16.dp, y = 200.dp)
                .size(16.dp)
                .background(
                    Color(0xFFFFD700),
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        )
        
        // 奖励对话框
        if (state.showRewardDialog && state.gameResult != null) {
            RewardDialog(
                gameResult = state.gameResult!!,
                onClaim = { viewModel.sendIntent(ScratchGameIntent.ClaimReward) },
                onDismiss = { viewModel.sendIntent(ScratchGameIntent.ClaimReward) }
            )
        }
    }
}

@Composable
private fun RewardDialog(
    gameResult: com.example.step0724.data.model.ScratchGameResult,
    onClaim: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = if (gameResult.rewardType == ScratchRewardType.JACKPOT) "🎉 顶格奖励!" else "🎁 恭喜获奖!",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                color = Color(0xFFFF69B4),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (gameResult.rewardType == ScratchRewardType.JACKPOT) {
                    Text(
                        text = "所有图案相同！",
                        fontSize = 16.sp,
                        color = Color(0xFFFF6B35),
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                Text(
                    text = "获得 ${gameResult.coinReward} 金币",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFFFA726)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onClaim,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFF69B4)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    "领取奖励", 
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
            }
        },
        shape = RoundedCornerShape(20.dp),
        containerColor = Color.White
    )
}