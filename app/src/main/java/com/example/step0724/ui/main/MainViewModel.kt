package com.example.step0724.ui.main

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.permission.PermissionManager
import com.example.step0724.data.service.StepCountingService
import com.example.step0724.navigation.TabDestination
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val stepCountingService: StepCountingService,
    private val permissionManager: PermissionManager
) : MviViewModel<MainIntent, MainUiState, MainEffect>() {

    override fun setInitialState(): MainUiState = MainUiState()

    init {
        // 应用启动时尝试启动计步服务
        viewModelScope.launch {
            if (permissionManager.hasActivityRecognitionPermission()) {
                if (!stepCountingService.isRunning()) {
                    stepCountingService.startCounting()
                }
            }
        }
    }

    override suspend fun handleIntent(intent: MainIntent) {
        when (intent) {
            is MainIntent.SelectTab -> {
                setState { copy(selectedTab = intent.tab, isMenuVisible = false) }
                setEffect { MainEffect.NavigateToTab(intent.tab) }
            }
            is MainIntent.SyncTabWithNavigation -> {
                // 只更新Tab状态，不触发导航
                setState { copy(selectedTab = intent.tab) }
            }
            is MainIntent.ToggleMenu -> {
                setState { copy(isMenuVisible = !isMenuVisible) }
            }
            is MainIntent.CloseMenu -> {
                setState { copy(isMenuVisible = false) }
            }
            is MainIntent.PermissionGranted -> {
                // 权限授予后启动计步服务
                if (!stepCountingService.isRunning()) {
                    stepCountingService.startCounting()
                }
            }
        }
    }
}