package com.example.step0724.ui.report

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.model.ChartPoint
import com.example.step0724.data.model.DataType
import com.example.step0724.data.model.TimeRange
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.repository.SettingsRepository
import com.example.step0724.domain.usecase.CalculateStatsUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.inject.Inject

/**
 * Report页面ViewModel - 基于需求3的数据报告页面功能
 */
@HiltViewModel
class ReportViewModel @Inject constructor(
    private val stepRepository: StepRepository,
    private val settingsRepository: SettingsRepository,
    private val calculateStatsUseCase: CalculateStatsUseCase
) : MviViewModel<ReportContract.Intent, ReportContract.UiState, ReportContract.SideEffect>() {
    
    private var dayCurrentDate = LocalDate.now()
    private var weekCurrentDate = LocalDate.now()
    private var monthCurrentDate = LocalDate.now()
    
    override fun setInitialState(): ReportContract.UiState {
        return ReportContract.UiState()
    }
    
    init {
        loadAllData()
        observeStepDataChanges()
    }
    
    /**
     * 监听步数数据变化，实时更新Report页面
     * 需求2：实时步数更新应该反映到Report页面
     */
    private fun observeStepDataChanges() {
        viewModelScope.launch {
            // 监听今日步数变化
            stepRepository.getTodaySteps().collect { steps ->
                // 只有当查看的是今天的数据时才自动更新
                val today = LocalDate.now()
                if (dayCurrentDate == today) {
                    loadTimeRangeData(TimeRange.DAY)
                }
                
                // 如果查看的周包含今天，也更新周数据
                val startOfWeek = weekCurrentDate.minusDays(weekCurrentDate.dayOfWeek.value % 7L)
                val endOfWeek = startOfWeek.plusDays(6)
                if (today >= startOfWeek && today <= endOfWeek) {
                    loadTimeRangeData(TimeRange.WEEK)
                }
                
                // 如果查看的月包含今天，也更新月数据
                if (monthCurrentDate.year == today.year && monthCurrentDate.month == today.month) {
                    loadTimeRangeData(TimeRange.MONTH)
                }
            }
        }
    }
    
    override suspend fun handleIntent(intent: ReportContract.Intent) {
        when (intent) {
            is ReportContract.Intent.ToggleDataTypeDropdown -> {
                setState { 
                    copy(showDataTypeDropdown = !showDataTypeDropdown) 
                }
            }
            
            is ReportContract.Intent.SelectDataType -> {
                setState { 
                    copy(
                        selectedDataType = intent.dataType,
                        showDataTypeDropdown = false
                    ) 
                }
                loadAllData()
            }
            
            is ReportContract.Intent.NavigatePrevious -> {
                navigateData(intent.timeRange, -1)
            }
            
            is ReportContract.Intent.NavigateNext -> {
                navigateData(intent.timeRange, 1)
            }
            
            is ReportContract.Intent.RefreshData -> {
                // 强制刷新所有数据
                loadAllData()
            }
        }
    }
    
    private fun loadAllData() {
        loadTimeRangeData(TimeRange.DAY)
        loadTimeRangeData(TimeRange.WEEK)
        loadTimeRangeData(TimeRange.MONTH)
    }
    
    private fun loadTimeRangeData(timeRange: TimeRange) {
        viewModelScope.launch {
            try {
                // 设置加载状态
                setState {
                    when (timeRange) {
                        TimeRange.DAY -> copy(dayData = dayData.copy(isLoading = true))
                        TimeRange.WEEK -> copy(weekData = weekData.copy(isLoading = true))
                        TimeRange.MONTH -> copy(monthData = monthData.copy(isLoading = true))
                    }
                }
                
                val chartData = when (timeRange) {
                    TimeRange.DAY -> loadDayData()
                    TimeRange.WEEK -> loadWeekData()
                    TimeRange.MONTH -> loadMonthData()
                }
                
                val (totalValue, averageValue) = calculateStatistics(chartData, timeRange)
                val currentDate = getCurrentDateString(timeRange)
                
                setState {
                    when (timeRange) {
                        TimeRange.DAY -> copy(
                            dayData = ReportContract.TimeRangeData(
                                chartData = chartData,
                                totalValue = totalValue,
                                averageValue = averageValue,
                                isLoading = false,
                                currentDate = currentDate
                            )
                        )
                        TimeRange.WEEK -> copy(
                            weekData = ReportContract.TimeRangeData(
                                chartData = chartData,
                                totalValue = totalValue,
                                averageValue = averageValue,
                                isLoading = false,
                                currentDate = currentDate
                            )
                        )
                        TimeRange.MONTH -> copy(
                            monthData = ReportContract.TimeRangeData(
                                chartData = chartData,
                                totalValue = totalValue,
                                averageValue = averageValue,
                                isLoading = false,
                                currentDate = currentDate
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                setState {
                    when (timeRange) {
                        TimeRange.DAY -> copy(dayData = dayData.copy(isLoading = false))
                        TimeRange.WEEK -> copy(weekData = weekData.copy(isLoading = false))
                        TimeRange.MONTH -> copy(monthData = monthData.copy(isLoading = false))
                    }
                }
                setEffect { ReportContract.SideEffect.ShowError("加载${timeRange}数据失败: ${e.message}") }
            }
        }
    }
    
    /**
     * 需求3.3：按小时查询(0-24时累计数据)
     * 横轴为小时(0-23)，显示每小时的累计数据
     */
    private suspend fun loadDayData(): List<ChartPoint> {
        val dateStr = dayCurrentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        val hourlySteps = stepRepository.getHourlyData(dateStr)
        val userSettings = settingsRepository.getUserSettings().first()
        
        return hourlySteps.mapIndexed { hour, steps ->
            val dataValue = when (state.value.selectedDataType) {
                DataType.STEPS -> steps.toFloat()
                DataType.DISTANCE -> {
                    val distance = calculateStatsUseCase.calculateDistance(
                        steps, 
                        userSettings.stepLength, 
                        userSettings.unitSystem
                    )
                    distance.toFloat()
                }
                DataType.CALORIES -> {
                    val weightKg = if (userSettings.unitSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL) {
                        userSettings.weight * 0.45359237 // lbs转kg
                    } else {
                        userSettings.weight
                    }
                    val calories = calculateStatsUseCase.calculateCalories(steps, weightKg)
                    calories.toFloat()
                }
                DataType.TIME -> {
                    val timeSeconds = calculateStatsUseCase.calculateWalkingTimeSeconds(steps)
                    (timeSeconds / 60.0).toFloat() // 转换为分钟
                }
            }
            
            ChartPoint(
                x = hour.toFloat(),
                y = dataValue,
                label = "${hour}:00"
            )
        }
    }
    
    /**
     * 需求3.4：按周查询(周日到周一数据)
     * 需求3.11：周期从周日开始
     * 横轴为星期，显示周日到周一的当日数据
     */
    private suspend fun loadWeekData(): List<ChartPoint> {
        // 计算周的开始日期（周日）
        val startOfWeek = weekCurrentDate.minusDays(weekCurrentDate.dayOfWeek.value % 7L)
        val weekData = mutableListOf<ChartPoint>()
        val userSettings = settingsRepository.getUserSettings().first()
        
        for (i in 0..6) {
            val date = startOfWeek.plusDays(i.toLong())
            val dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            val stepRecord = stepRepository.getStepRecord(dateStr)
            val steps = stepRecord?.steps ?: 0
            
            val dataValue = when (state.value.selectedDataType) {
                DataType.STEPS -> steps.toFloat()
                DataType.DISTANCE -> {
                    if (stepRecord != null && stepRecord.distance > 0) {
                        // 使用已保存的距离数据
                        stepRecord.distance.toFloat()
                    } else {
                        // 重新计算距离
                        val distance = calculateStatsUseCase.calculateDistance(
                            steps, 
                            userSettings.stepLength, 
                            userSettings.unitSystem
                        )
                        distance.toFloat()
                    }
                }
                DataType.CALORIES -> {
                    if (stepRecord != null && stepRecord.calories > 0) {
                        // 使用已保存的卡路里数据
                        stepRecord.calories.toFloat()
                    } else {
                        // 重新计算卡路里
                        val weightKg = if (userSettings.unitSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL) {
                            userSettings.weight * 0.45359237 // lbs转kg
                        } else {
                            userSettings.weight
                        }
                        val calories = calculateStatsUseCase.calculateCalories(steps, weightKg)
                        calories.toFloat()
                    }
                }
                DataType.TIME -> {
                    if (stepRecord != null && stepRecord.walkingTime > 0) {
                        // 使用已保存的时间数据
                        (stepRecord.walkingTime / 60.0).toFloat() // 转换为分钟
                    } else {
                        // 重新计算时间
                        val timeSeconds = calculateStatsUseCase.calculateWalkingTimeSeconds(steps)
                        (timeSeconds / 60.0).toFloat() // 转换为分钟
                    }
                }
            }
            
            val dayNames = listOf("周日", "周一", "周二", "周三", "周四", "周五", "周六")
            weekData.add(
                ChartPoint(
                    x = i.toFloat(),
                    y = dataValue,
                    label = dayNames[i]
                )
            )
        }
        
        return weekData
    }
    
    /**
     * 需求3.5：按月查询(1-31号数据)
     * 横轴为日期，显示1号-31号的当日数据
     */
    private suspend fun loadMonthData(): List<ChartPoint> {
        val year = monthCurrentDate.year
        val month = monthCurrentDate.monthValue
        val daysInMonth = monthCurrentDate.lengthOfMonth()
        val monthData = mutableListOf<ChartPoint>()
        val userSettings = settingsRepository.getUserSettings().first()
        
        for (day in 1..daysInMonth) {
            val date = LocalDate.of(year, month, day)
            val dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
            val stepRecord = stepRepository.getStepRecord(dateStr)
            val steps = stepRecord?.steps ?: 0
            
            val dataValue = when (state.value.selectedDataType) {
                DataType.STEPS -> steps.toFloat()
                DataType.DISTANCE -> {
                    if (stepRecord != null && stepRecord.distance > 0) {
                        // 使用已保存的距离数据
                        stepRecord.distance.toFloat()
                    } else {
                        // 重新计算距离
                        val distance = calculateStatsUseCase.calculateDistance(
                            steps, 
                            userSettings.stepLength, 
                            userSettings.unitSystem
                        )
                        distance.toFloat()
                    }
                }
                DataType.CALORIES -> {
                    if (stepRecord != null && stepRecord.calories > 0) {
                        // 使用已保存的卡路里数据
                        stepRecord.calories.toFloat()
                    } else {
                        // 重新计算卡路里
                        val weightKg = if (userSettings.unitSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL) {
                            userSettings.weight * 0.45359237 // lbs转kg
                        } else {
                            userSettings.weight
                        }
                        val calories = calculateStatsUseCase.calculateCalories(steps, weightKg)
                        calories.toFloat()
                    }
                }
                DataType.TIME -> {
                    if (stepRecord != null && stepRecord.walkingTime > 0) {
                        // 使用已保存的时间数据
                        (stepRecord.walkingTime / 60.0).toFloat() // 转换为分钟
                    } else {
                        // 重新计算时间
                        val timeSeconds = calculateStatsUseCase.calculateWalkingTimeSeconds(steps)
                        (timeSeconds / 60.0).toFloat() // 转换为分钟
                    }
                }
            }
            
            monthData.add(
                ChartPoint(
                    x = day.toFloat(),
                    y = dataValue,
                    label = "${day}日"
                )
            )
        }
        
        return monthData
    }
    
    /**
     * 需求3.7：显示总值和平均值统计
     * 在Week或Month维度时，右侧显示总值和平均值
     * 周度总值/7，月度总值/30或31
     */
    private fun calculateStatistics(chartData: List<ChartPoint>, timeRange: TimeRange): Pair<Double, Double> {
        if (chartData.isEmpty()) return Pair(0.0, 0.0)
        
        val total = chartData.sumOf { it.y.toDouble() }
        val average = when (timeRange) {
            TimeRange.DAY -> {
                // 日度：总值/24小时
                total / 24.0
            }
            TimeRange.WEEK -> {
                // 需求3.7：周度总值/7
                total / 7.0
            }
            TimeRange.MONTH -> {
                // 需求3.7：月度总值/天数（30或31）
                total / monthCurrentDate.lengthOfMonth()
            }
        }
        
        return Pair(total, average)
    }
    
    /**
     * 需求3.8：左右滑动查看更多时间范围的数据
     */
    private fun navigateData(timeRange: TimeRange, direction: Int) {
        when (timeRange) {
            TimeRange.DAY -> {
                dayCurrentDate = dayCurrentDate.plusDays(direction.toLong())
                loadTimeRangeData(TimeRange.DAY)
            }
            TimeRange.WEEK -> {
                weekCurrentDate = weekCurrentDate.plusWeeks(direction.toLong())
                loadTimeRangeData(TimeRange.WEEK)
            }
            TimeRange.MONTH -> {
                monthCurrentDate = monthCurrentDate.plusMonths(direction.toLong())
                loadTimeRangeData(TimeRange.MONTH)
            }
        }
    }
    
    /**
     * 获取当前日期字符串用于显示
     */
    private fun getCurrentDateString(timeRange: TimeRange): String {
        return when (timeRange) {
            TimeRange.DAY -> {
                // Day: 显示具体日期，如 "Oct 02"
                dayCurrentDate.format(DateTimeFormatter.ofPattern("MMM dd", java.util.Locale.ENGLISH))
            }
            TimeRange.WEEK -> {
                // Week: 显示日期范围，如 "7月20-26"
                val startOfWeek = weekCurrentDate.minusDays(weekCurrentDate.dayOfWeek.value % 7L)
                val endOfWeek = startOfWeek.plusDays(6)
                
                if (startOfWeek.month == endOfWeek.month) {
                    // 同一个月内，如 "7月20-26"
                    "${startOfWeek.monthValue}月${startOfWeek.dayOfMonth}-${endOfWeek.dayOfMonth}"
                } else {
                    // 跨月，如 "7月30-8月5"
                    "${startOfWeek.monthValue}月${startOfWeek.dayOfMonth}-${endOfWeek.monthValue}月${endOfWeek.dayOfMonth}"
                }
            }
            TimeRange.MONTH -> {
                // Month: 只显示年月，如 "2024年7月"
                "${monthCurrentDate.year}年${monthCurrentDate.monthValue}月"
            }
        }
    }
}