package com.example.step0724.ui.games

import androidx.compose.animation.core.*
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.step0724.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * Egg Smash游戏页面
 * 敲金蛋游戏实现
 * 
 * Requirements: 9.5 - 添加Egg Smash（敲金蛋）游戏页面的导航支持
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EggSmashScreen(
    onNavigateBack: () -> Unit = {}
) {
    val context = LocalContext.current
    val viewModel: EggSmashViewModel = viewModel()
    
    // 游戏状态
    var eggStates by remember { mutableStateOf(List(6) { EggState.INTACT }) }
    var hammerAnimations by remember { mutableStateOf(List(6) { HammerAnimation() }) }
    var showResult by remember { mutableStateOf(false) }
    var resultMessage by remember { mutableStateOf("") }
    var showNoChancesDialog by remember { mutableStateOf(false) }
    var currentEggIndex by remember { mutableStateOf(-1) } // 记录当前被砸的金蛋索引
    
    // 观察每日次数状态
    val dailyChances by viewModel.dailyChances.collectAsState()
    val canPlay by viewModel.canPlay.collectAsState()
    
    // 页面显示时刷新次数
    LaunchedEffect(Unit) {
        viewModel.refreshChances()
    }
    
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()
    
    // 敲金蛋函数
    fun smashEgg(index: Int) {
        // 检查金蛋状态
        if (eggStates[index] != EggState.INTACT) return

        // 检查是否可以游戏
        if (!canPlay) {
            showNoChancesDialog = true
            return
        }

        // 消耗游戏次数
        if (!viewModel.startGame()) {
            showNoChancesDialog = true
            return
        }
        
        // 开始锤子动画
        hammerAnimations = hammerAnimations.toMutableList().apply {
            this[index] = this[index].copy(isAnimating = true)
        }
        
        // 延迟改变金蛋状态和显示奖励
        coroutineScope.launch {
            delay(800) // 等待锤子动画完成
            
            // 改变金蛋状态
            eggStates = eggStates.toMutableList().apply {
                this[index] = EggState.BROKEN
            }
            
            // 停止锤子动画
            hammerAnimations = hammerAnimations.toMutableList().apply {
                this[index] = this[index].copy(isAnimating = false)
            }
            
            // 生成随机奖励
            val reward = generateReward()
            resultMessage = "恭喜获得 $reward 金币！"
            currentEggIndex = index // 记录当前金蛋索引
            showResult = true
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // 背景图片
        Image(
            painter = painterResource(id = R.drawable.bg_egg),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Image(
                painter = painterResource(id = R.drawable.title_egg),
                contentDescription = "Egg Smash",
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth()
            )
            
            // 金蛋游戏区域
            Box(
                modifier = Modifier
                    .offset(y = (-20).dp)
                    .paint(painterResource(id = R.mipmap.egg_bg_ku), contentScale = ContentScale.Crop)
            ) {
                // 两排金蛋布局
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                        .offset(y = (20).dp)
                        .padding(horizontal = 50.dp)
                ) {
                    repeat(2) { row ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            repeat(3) { col ->
                                val index = row * 3 + col
                                
                                Box(
                                    modifier = Modifier
                                        .weight(1f)
                                        .aspectRatio(1f),
                                    contentAlignment = Alignment.Center
                                ) {
                                    EggItem(
                                        eggState = eggStates[index],
                                        hammerAnimation = hammerAnimations[index],
                                        onClick = { smashEgg(index) }
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(45.dp))
                    }
                }

                // 每日免费次数
                Row(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(y = (-52).dp)
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Daily free chances: $dailyChances",
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
        
        // 结果弹窗
        if (showResult) {
            ResultDialog(
                message = resultMessage,
                onDismiss = { 
                    showResult = false
                    // 重置对应的金蛋
                    if (currentEggIndex >= 0) {
                        eggStates = eggStates.toMutableList().apply {
                            this[currentEggIndex] = EggState.INTACT
                        }
                        currentEggIndex = -1
                    }
                }
            )
        }
        
        // 没有次数的提示弹窗
        if (showNoChancesDialog) {
            NoChancesDialog(
                onDismiss = { showNoChancesDialog = false }
            )
        }
    }
}

@Composable
private fun EggItem(
    eggState: EggState,
    hammerAnimation: HammerAnimation,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clickable(enabled = eggState == EggState.INTACT) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // 金蛋图片
        Image(
            painter = painterResource(
                id = when (eggState) {
                    EggState.INTACT -> R.mipmap.egg_status_c
                    EggState.BROKEN -> R.mipmap.egg_status_b
                }
            ),
            contentDescription = "金蛋",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        // 锤子动画
        if (hammerAnimation.isAnimating) {
            HammerAnimationComponent()
        }
    }
}

@Composable
private fun HammerAnimationComponent() {
    // 动画状态：0=初始, 1=蓄力完成, 2=砸击完成
    var animationState by remember { mutableStateOf(0) }
    
    // 旋转角度动画：先顺时针蓄力(正角度)，然后逆时针下砸(负角度)
    val rotation by animateFloatAsState(
        targetValue = when (animationState) {
            0 -> 0f          // 初始位置
            1 -> 45f         // 顺时针蓄力
            else -> -30f     // 逆时针下砸
        },
        animationSpec = when (animationState) {
            1 -> tween(300, easing = EaseInCubic)      // 蓄力阶段
            2 -> tween(200, easing = EaseOutBounce)    // 砸击阶段
            else -> tween(0)
        },
        label = "rotation"
    )
    
    // Y轴位移：锤子在金蛋头顶，砸击时向下移动
    val offsetY by animateFloatAsState(
        targetValue = when (animationState) {
            0 -> -40f        // 初始位置（更高）
            1 -> -40f        // 蓄力时保持高度
            else -> -25f     // 砸击时下移
        },
        animationSpec = when (animationState) {
            2 -> tween(200, easing = EaseOutBounce)    // 砸击时的位移
            else -> tween(0)
        },
        label = "offsetY"
    )
    
    // 动画序列控制
    LaunchedEffect(Unit) {
        delay(100)           // 短暂延迟
        animationState = 1   // 开始蓄力
        delay(300)           // 蓄力持续时间
        animationState = 2   // 开始砸击
    }
    
    Image(
        painter = painterResource(id = R.mipmap.egg_hammer),
        contentDescription = "锤子",
        modifier = Modifier
            .size(120.dp)
            .offset(x = 25.dp, y = offsetY.dp - 10.dp)
            .rotate(rotation)
            .graphicsLayer {
                transformOrigin = androidx.compose.ui.graphics.TransformOrigin(0.5f, 0.9f)
            }
    )
}

@Composable
private fun ResultDialog(
    message: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "恭喜！",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        text = {
            Text(
                text = message,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

@Composable
private fun NoChancesDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "提示",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        text = {
            Text(
                text = "今日免费次数已用完，请明天再来！",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

// 数据类和枚举
enum class EggState {
    INTACT,  // 完整的金蛋
    BROKEN   // 打开的金蛋
}

data class HammerAnimation(
    val isAnimating: Boolean = false
)

// 生成随机奖励
private fun generateReward(): Int {
    val random = Random.nextFloat()
    return when {
        random < 0.01f -> 500  // 1% 概率获得500金币
        random < 0.05f -> 100  // 4% 概率获得100金币
        random < 0.15f -> 50   // 10% 概率获得50金币
        random < 0.35f -> 20   // 20% 概率获得20金币
        else -> 10             // 65% 概率获得10金币
    }
}
