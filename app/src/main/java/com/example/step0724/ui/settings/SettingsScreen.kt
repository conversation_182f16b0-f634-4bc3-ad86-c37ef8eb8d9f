package com.example.step0724.ui.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.step0724.R
import com.example.step0724.ui.theme.Gray100
import com.example.step0724.ui.theme.PrimaryBlue

@Composable
fun SettingsScreen(
    onNavigateToPermissions: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is SettingsEffect.NavigateToPermissions -> {
                    onNavigateToPermissions()
                }
            }
        }
    }

    // 主要内容区域
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
            // 权限管理
            SettingsItem(
                icon = R.mipmap.set_manage,
                title = "Manage Permissions",
                onClick = { viewModel.sendIntent(SettingsIntent.NavigateToPermissions) }
            )

            // 联系我们
            SettingsItem(
                icon = R.mipmap.set_contact,
                title = "Contact us",
                onClick = { viewModel.sendIntent(SettingsIntent.ContactUs) }
            )

            // 隐私政策
            SettingsItem(
                icon = R.mipmap.set_privacy,
                title = "Privacy Policy",
                onClick = { viewModel.sendIntent(SettingsIntent.OpenPrivacyPolicy) }
            )

            // 用户协议
            SettingsItem(
                icon = R.mipmap.set_user,
                title = "User Agreement",
                onClick = { viewModel.sendIntent(SettingsIntent.OpenUserAgreement) }
            )
        }


}



@Composable
private fun SettingsItem(
    icon: Int,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 80.dp)
            .background(color = Color.White, shape = RoundedCornerShape(16.dp))
            .padding(16.dp)
            .clickable { onClick() },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = icon),
            contentDescription = title,
            modifier = Modifier.size(32.dp)
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )
        }

        Image(
            painter = painterResource(id = R.mipmap.nav_next),
            contentDescription = "Navigate",
            modifier = Modifier.size(20.dp)
        )
    }
}