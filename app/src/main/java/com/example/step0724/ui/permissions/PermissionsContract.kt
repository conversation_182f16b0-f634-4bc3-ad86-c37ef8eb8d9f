package com.example.step0724.ui.permissions

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState

sealed class PermissionsIntent : UiIntent {
    object LoadPermissions : PermissionsIntent()
    object RequestActivityRecognition : PermissionsIntent()
    object RequestBatteryOptimization : PermissionsIntent()
    object RequestNotificationPermission : PermissionsIntent()
    data class ToggleNotificationPermission(val enabled: Boolean) : PermissionsIntent()
    object DismissPermissionExplanationDialog : PermissionsIntent()
    object ConfirmPermissionExplanation : PermissionsIntent()
}

data class PermissionsUiState(
    val isLoading: Boolean = false,
    val hasActivityRecognition: Boolean = false,
    val hasBatteryOptimization: Boolean = false,
    val hasNotificationPermission: <PERSON>olean = false,
    val isNotificationEnabled: Boolean = false,
    val canToggleNotification: Boolean = true,
    val showPermissionExplanationDialog: Boolean = false,
    val currentExplanationPermission: PermissionExplanationType? = null
) : UiState

enum class PermissionExplanationType {
    ACTIVITY_RECOGNITION,
    BATTERY_OPTIMIZATION,
    NOTIFICATION
}

sealed class PermissionsEffect : UiEffect {
    object RequestActivityRecognitionPermission : PermissionsEffect()
    object RequestBatteryOptimizationPermission : PermissionsEffect()
    object RequestNotificationPermission : PermissionsEffect()
    data class ShowError(val message: String) : PermissionsEffect()
    data class ShowPermissionExplanation(val permissionType: String) : PermissionsEffect()
}