package com.example.step0724.ui.earn

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.model.TaskType
import com.example.step0724.data.repository.EarnRepository
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.storage.TimeManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * EarnViewModel - Earn功能状态管理
 * 
 * 继承BaseViewModel，实现MVI架构的状态管理，包括：
 * - Intent处理逻辑分发用户操作
 * - 数据加载和刷新功能
 * - 任务领取的业务逻辑处理
 * - 导航Effect的触发逻辑
 * - 倒计时更新的协程逻辑
 * 
 * Requirements: 2.1, 2.2, 2.3, 2.4, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 
 *              5.1, 5.2, 5.3, 5.4, 5.5, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 
 *              7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 8.1, 8.2, 8.3, 8.4, 8.5
 */
@Hilt<PERSON>iew<PERSON>odel
class <PERSON>arnViewModel @Inject constructor(
    private val earnRepository: EarnRepository,
    private val stepRepository: StepRepository,
    private val timeManager: TimeManager
) : MviViewModel<EarnContract.Intent, EarnContract.UiState, EarnContract.Effect>() {

    private var countdownJob: Job? = null
    private var stepMonitoringJob: Job? = null
    private var lastUpdateDate: String = ""

    override fun setInitialState(): EarnContract.UiState = EarnContract.UiState()

    init {
        // 初始化时加载数据
        sendIntent(EarnContract.Intent.LoadData)
        // 启动倒计时更新
        startCountdownTimer()
        // 启动步数实时监控
        startStepMonitoring()
    }
    
    /**
     * 页面进入时的倒计时初始化
     * 
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     */
    fun onPageEntered() {
        viewModelScope.launch {
            // 立即更新倒计时显示，确保显示准确的剩余时间
            updateCountdown()
            
            // 重启倒计时定时器，确保定时器与当前时间同步
            startCountdownTimer()
        }
    }
    
    /**
     * 页面离开时的资源清理
     * 
     * 当用户离开Earn页面时调用，用于节省系统资源
     */
    fun onPageLeft() {
        // 停止倒计时定时器以节省资源
        stopCountdownTimer()
    }
    
    /**
     * 手动触发倒计时更新
     * 
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     */
    fun manualUpdateCountdown() {
        viewModelScope.launch {
            updateCountdown()
        }
    }

    /**
     * Intent处理逻辑分发用户操作
     * 
     * Requirements: 2.1, 2.2, 2.3, 2.4 - 处理用户在Earn页面的各种操作
     */
    override suspend fun handleIntent(intent: EarnContract.Intent) {
        when (intent) {
            is EarnContract.Intent.LoadData -> {
                loadEarnData()
            }
            
            is EarnContract.Intent.RefreshData -> {
                refreshEarnData()
            }
            
            is EarnContract.Intent.ClaimTask -> {
                claimTaskReward(intent.taskType)
            }
            
            is EarnContract.Intent.NavigateToTask -> {
                handleTaskNavigation(intent.taskType)
            }
            
            is EarnContract.Intent.NavigateToLuckyWheel -> {
                setEffect { EarnContract.Effect.NavigateToLuckyWheel }
            }
            
            is EarnContract.Intent.NavigateToEggSmash -> {
                setEffect { EarnContract.Effect.NavigateToEggSmash }
            }
            
            is EarnContract.Intent.NavigateToLuckyScratch -> {
                setEffect { EarnContract.Effect.NavigateToLuckyScratch }
            }
            
            is EarnContract.Intent.NavigateToGame -> {
                setEffect { EarnContract.Effect.NavigateToGame(intent.gameType) }
            }
            
            is EarnContract.Intent.NavigateToSteps -> {
                setEffect { EarnContract.Effect.NavigateToSteps }
            }
            
            is EarnContract.Intent.UpdateCountdown -> {
                updateCountdown()
            }
        }
    }

    /**
     * 数据加载功能
     * 
     * Requirements: 4.1 - 当用户进入Earn页面时，系统应在页面顶部显示当前金币余额
     * Requirements: 4.2 - 当用户查看Earn页面时，系统应显示"Daily tasks"区域
     * Requirements: 7.1 - 当用户查看Earn页面时，系统应显示"Other tasks"区域
     */
    private suspend fun loadEarnData() {
        setState { copy(isLoading = true, errorMessage = null) }
        
        try {
            val earnData = earnRepository.getEarnData()
            lastUpdateDate = timeManager.getCurrentDateString()
            
            val currentSteps = earnRepository.getCurrentSteps()
            
            setState {
                copy(
                    coins = earnData.coins,
                    dailyTasks = earnData.dailyTasks,
                    otherTasks = earnData.otherTasks,
                    timeUntilReset = earnData.timeUntilReset,
                    consecutiveCheckIns = earnData.consecutiveCheckIns,
                    currentSteps = currentSteps,
                    isLoading = false,
                    errorMessage = null
                )
            }
        } catch (e: Exception) {
            setState { 
                copy(
                    isLoading = false, 
                    errorMessage = "Failed to load data: ${e.message}"
                ) 
            }
            setEffect { EarnContract.Effect.ShowError("Failed to load earn data") }
        }
    }

    /**
     * 数据刷新功能
     * 
     * Requirements: 2.2 - 当用户的金币数量发生变化时，系统应实时更新余额显示
     * Requirements: 6.1, 6.2 - 步数任务状态的实时更新
     */
    private suspend fun refreshEarnData() {
        try {
            val earnData = earnRepository.getEarnData()
            val currentSteps = earnRepository.getCurrentSteps()
            
            setState {
                copy(
                    coins = earnData.coins,
                    dailyTasks = earnData.dailyTasks,
                    otherTasks = earnData.otherTasks,
                    timeUntilReset = earnData.timeUntilReset,
                    consecutiveCheckIns = earnData.consecutiveCheckIns,
                    currentSteps = currentSteps,
                    errorMessage = null
                )
            }
        } catch (e: Exception) {
            setEffect { EarnContract.Effect.ShowError("Failed to refresh data") }
        }
    }

    /**
     * 任务领取的业务逻辑处理
     * 
     * Requirements: 4.5 - 当用户完成任务但未领取奖励时，系统应显示"Claim"按钮
     * Requirements: 5.2 - 当用户点击签到任务的"Claim"按钮时，系统应给用户增加签到奖励金币
     * Requirements: 5.3 - 当用户点击签到任务的"Claim"按钮时，系统应将按钮状态改为勾选标记
     * Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
     * Requirements: 6.4 - 当用户点击步数任务的"Claim"按钮时，系统应给用户增加步数任务奖励金币
     * Requirements: 7.6 - 当用户点击连续签到任务的"Claim"按钮时，系统应给用户增加连续签到奖励金币
     */
    private suspend fun claimTaskReward(taskType: TaskType) {
        try {
            val result = earnRepository.claimTask(taskType)
            
            result.onSuccess { rewardAmount ->
                // 显示奖励动画
                setEffect { EarnContract.Effect.ShowRewardAnimation(rewardAmount) }
                
                // 根据任务类型显示不同的成功消息
                val successMessage = when (taskType) {
                    TaskType.DAILY_CHECK_IN -> {
                        "Daily check-in completed! Earned $rewardAmount coins!"
                    }
                    TaskType.STEPS_1000 -> {
                        "Congratulations! 1000 steps completed! Earned $rewardAmount coins!"
                    }
                    TaskType.STEPS_3000 -> {
                        "Amazing! 3000 steps completed! Earned $rewardAmount coins!"
                    }
                    TaskType.LUCKY_WHEEL -> {
                        "Lucky Wheel task completed! Earned $rewardAmount coins!"
                    }
                    TaskType.CHECK_IN_2_DAYS -> {
                        "2-day check-in streak completed! Earned $rewardAmount coins!"
                    }
                    TaskType.CHECK_IN_3_DAYS -> {
                        "3-day check-in streak completed! Earned $rewardAmount coins!"
                    }
                }
                
                setEffect { EarnContract.Effect.ShowSuccess(successMessage) }
                
                // 刷新数据以更新UI状态
                refreshEarnData()
                
            }.onFailure { exception ->
                val errorMessage = when (taskType) {
                    TaskType.DAILY_CHECK_IN -> {
                        if (exception.message?.contains("Already checked in") == true) {
                            "You have already checked in today!"
                        } else {
                            "Failed to check in: ${exception.message}"
                        }
                    }
                    TaskType.STEPS_1000 -> {
                        if (exception.message?.contains("not ready to claim") == true) {
                            "You need to reach 1000 steps first!"
                        } else {
                            "Failed to claim 1000 steps reward: ${exception.message}"
                        }
                    }
                    TaskType.STEPS_3000 -> {
                        if (exception.message?.contains("not ready to claim") == true) {
                            "You need to reach 3000 steps first!"
                        } else {
                            "Failed to claim 3000 steps reward: ${exception.message}"
                        }
                    }
                    else -> {
                        "Failed to claim reward: ${exception.message}"
                    }
                }
                
                setEffect { EarnContract.Effect.ShowError(errorMessage) }
            }
        } catch (e: Exception) {
            setEffect { EarnContract.Effect.ShowError("Error claiming task: ${e.message}") }
        }
    }

    /**
     * 处理任务导航逻辑
     * 
     * Requirements: 4.7 - 当用户点击步数任务的"Go"按钮时，系统应跳转到Steps页面
     * Requirements: 6.5 - 当用户点击步数任务的"Go"按钮时，系统应跳转到Steps页面
     * Requirements: 7.7 - 当用户点击连续签到任务的"Go"按钮时，系统应跳转到签到任务或显示提示信息
     */
    private suspend fun handleTaskNavigation(taskType: TaskType) {
        when (taskType) {
            TaskType.STEPS_1000, TaskType.STEPS_3000 -> {
                // 导航到Steps页面，让用户查看步数进度并继续运动
                setEffect { EarnContract.Effect.NavigateToSteps }
                
                // 显示鼓励信息
                val targetSteps = if (taskType == TaskType.STEPS_1000) 1000 else 3000
                val currentSteps = state.value.currentSteps
                val remaining = targetSteps - currentSteps
                
                if (remaining > 0) {
                    setEffect { 
                        EarnContract.Effect.ShowSuccess("You need $remaining more steps to reach $targetSteps steps. Keep walking!") 
                    }
                }
            }
            TaskType.LUCKY_WHEEL -> {
                setEffect { EarnContract.Effect.NavigateToLuckyWheel }
            }
            TaskType.DAILY_CHECK_IN -> {
                // 签到任务通常直接领取，不需要导航
                setEffect { EarnContract.Effect.ShowError("Please claim your daily check-in reward") }
            }
            TaskType.CHECK_IN_2_DAYS, TaskType.CHECK_IN_3_DAYS -> {
                // 连续签到任务提示用户继续签到
                setEffect { EarnContract.Effect.ShowError("Keep checking in daily to complete this task") }
            }
        }
    }

    /**
     * 倒计时更新的协程逻辑
     * 
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     * Requirements: 8.4 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     */
    private fun startCountdownTimer() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            // 首次立即更新倒计时显示，确保页面进入时显示准确时间
            updateCountdown()
            
            while (true) {
                try {
                    // 计算到下一分钟整点的精确延迟时间
                    val currentTimeMillis = System.currentTimeMillis()
                    val nextMinuteMillis = ((currentTimeMillis / 60_000) + 1) * 60_000
                    val delayMillis = nextMinuteMillis - currentTimeMillis
                    
                    // 确保延迟时间合理（1秒到60秒之间）
                    val safeDelayMillis = delayMillis.coerceIn(1000L, 60_000L)
                    
                    // 延迟到下一分钟整点
                    delay(safeDelayMillis)
                    
                    // 更新倒计时
                    updateCountdown()
                    
                    // 检查是否需要重置任务（午夜时间检查）
                    if (timeManager.isMidnightPassed()) {
                        // 立即触发任务重置
                        val currentDate = timeManager.getCurrentDateString()
                        if (timeManager.isNewDay(lastUpdateDate)) {
                            lastUpdateDate = currentDate
                            loadEarnData() // 重新加载数据以反映重置状态
                        }
                    }
                    
                } catch (e: Exception) {
                    // 忽略取消异常，其他异常记录但不中断循环
                    if (e !is kotlinx.coroutines.CancellationException) {
                        setEffect { EarnContract.Effect.ShowError("倒计时更新失败: ${e.message}") }
                        // 发生错误时等待30秒后重试，避免频繁错误
                        delay(30_000)
                    } else {
                        break
                    }
                }
            }
        }
    }

    /**
     * 更新倒计时显示
     * 
     * Requirements: 8.1 - 系统应使用"HH:MM"格式显示剩余时间
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     * Requirements: 8.3 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     * Requirements: 8.4 - 当倒计时到达00:00时，系统应更新倒计时为下一天的24小时
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     */
    private suspend fun updateCountdown() {
        try {
            val currentDate = timeManager.getCurrentDateString()
            val timeUntilReset = timeManager.getTimeUntilMidnight()
            
            // 检查是否是新的一天
            if (timeManager.isNewDay(lastUpdateDate)) {
                // 新的一天开始，重置每日任务
                val resetResult = earnRepository.resetDailyTasks()
                
                resetResult.onSuccess {
                    lastUpdateDate = currentDate
                    
                    // 显示新一天开始的提示
                    setEffect { 
                        EarnContract.Effect.ShowSuccess("新的一天开始了！每日任务已重置。") 
                    }
                    
                    // 重新加载所有数据以反映重置后的状态
                    loadEarnData()
                }.onFailure { exception ->
                    // 重置失败时记录错误但继续更新倒计时
                    setEffect { 
                        EarnContract.Effect.ShowError("重置每日任务失败: ${exception.message}") 
                    }
                    
                    // 仍然更新倒计时显示
                    setState { copy(timeUntilReset = timeUntilReset) }
                }
            } else {
                // 同一天内，只更新倒计时显示
                setState { copy(timeUntilReset = timeUntilReset) }
                
                // 检查倒计时是否接近00:00（提供不同时间点的提醒）
                when (timeUntilReset) {
                    "00:01" -> {
                        setEffect { 
                            EarnContract.Effect.ShowSuccess("每日任务将在1分钟后重置！") 
                        }
                    }
                    "00:05" -> {
                        setEffect { 
                            EarnContract.Effect.ShowSuccess("每日任务将在5分钟后重置，请及时完成任务！") 
                        }
                    }
                    "00:00" -> {
                        // 倒计时到达00:00，立即触发重置检查
                        if (timeManager.isNewDay(lastUpdateDate)) {
                            val resetResult = earnRepository.resetDailyTasks()
                            resetResult.onSuccess {
                                lastUpdateDate = currentDate
                                setEffect { 
                                    EarnContract.Effect.ShowSuccess("午夜时间到！每日任务已自动重置。") 
                                }
                                loadEarnData()
                            }
                        }
                    }
                }
            }
            
            // 检查是否需要更新步数相关任务状态
            val currentSteps = earnRepository.getCurrentSteps()
            if (currentSteps != state.value.currentSteps) {
                // 步数发生变化，刷新数据以更新任务状态
                refreshEarnData()
            }
            
        } catch (e: Exception) {
            setEffect { EarnContract.Effect.ShowError("更新倒计时失败: ${e.message}") }
        }
    }

    /**
     * 启动步数实时监控
     * 
     * Requirements: 6.1 - 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取
     * Requirements: 6.2 - 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取
     * Requirements: 6.3 - 步数任务状态的实时更新逻辑
     */
    private fun startStepMonitoring() {
        stepMonitoringJob?.cancel()
        stepMonitoringJob = viewModelScope.launch {
            try {
                stepRepository.getTodaySteps()
                    .catch { exception ->
                        // 处理步数获取异常，但不中断监控
                        setEffect { EarnContract.Effect.ShowError("Failed to monitor steps: ${exception.message}") }
                    }
                    .onEach { currentSteps ->
                        // 步数发生变化时更新UI状态和任务状态
                        updateStepTasksStatus(currentSteps)
                    }
                    .launchIn(this)
            } catch (e: Exception) {
                setEffect { EarnContract.Effect.ShowError("Failed to start step monitoring") }
            }
        }
    }
    
    /**
     * 更新步数任务状态
     * 
     * Requirements: 6.1 - 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取
     * Requirements: 6.2 - 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取
     * Requirements: 6.3 - 步数任务状态的实时更新逻辑
     * 
     * @param currentSteps 当前步数
     */
    private suspend fun updateStepTasksStatus(currentSteps: Int) {
        val previousSteps = state.value.currentSteps
        
        // 只有步数发生变化时才更新
        if (currentSteps != previousSteps) {
            // 更新当前步数
            setState { copy(currentSteps = currentSteps) }
            
            // 检查是否达到步数目标
            val reached1000 = previousSteps < 1000 && currentSteps >= 1000
            val reached3000 = previousSteps < 3000 && currentSteps >= 3000
            
            // 如果达到了新的步数目标，刷新任务数据以更新状态
            if (reached1000 || reached3000) {
                refreshEarnData()
                
                // 显示步数目标达成提示
                when {
                    reached3000 -> {
                        setEffect { 
                            EarnContract.Effect.ShowSuccess("Congratulations! You've reached 3000 steps! Claim your reward!") 
                        }
                    }
                    reached1000 -> {
                        setEffect { 
                            EarnContract.Effect.ShowSuccess("Great job! You've reached 1000 steps! Claim your reward!") 
                        }
                    }
                }
            }
        }
    }

    // ==================== 游戏集成相关方法 ====================
    
    /**
     * 处理游戏返回后的数据刷新
     * 
     * Requirements: 9.7 - 当用户从任何游戏页面返回Earn页面时，系统应刷新任务状态和金币余额
     */
    suspend fun refreshAfterGame() {
        try {
            val result = earnRepository.refreshDataAfterGame()
            result.onSuccess { earnData ->
                setState {
                    copy(
                        coins = earnData.coins,
                        dailyTasks = earnData.dailyTasks,
                        otherTasks = earnData.otherTasks,
                        timeUntilReset = earnData.timeUntilReset,
                        consecutiveCheckIns = earnData.consecutiveCheckIns,
                        errorMessage = null
                    )
                }
            }.onFailure { exception ->
                setEffect { EarnContract.Effect.ShowError("Failed to refresh data after game: ${exception.message}") }
            }
        } catch (e: Exception) {
            setEffect { EarnContract.Effect.ShowError("Error refreshing data: ${e.message}") }
        }
    }
    
    /**
     * 处理游戏奖励
     * 
     * Requirements: 9.5, 9.8 - 当用户在任何游戏中获得奖励时，系统应更新用户的金币余额
     * 
     * @param gameType 游戏类型
     * @param rewardAmount 奖励金币数量
     */
    suspend fun addGameReward(gameType: com.example.step0724.data.model.GameType, rewardAmount: Int) {
        try {
            val result = earnRepository.addGameReward(gameType, rewardAmount)
            result.onSuccess { newBalance ->
                // 显示奖励动画
                setEffect { EarnContract.Effect.ShowRewardAnimation(rewardAmount) }
                
                // 显示成功消息
                val gameName = when (gameType) {
                    com.example.step0724.data.model.GameType.LUCKY_WHEEL -> "Lucky Wheel"
                    com.example.step0724.data.model.GameType.EGG_SMASH -> "Egg Smash"
                    com.example.step0724.data.model.GameType.LUCKY_SCRATCH -> "Lucky Scratch"
                }
                setEffect { EarnContract.Effect.ShowSuccess("$gameName reward: $rewardAmount coins earned!") }
                
                // 刷新数据
                refreshEarnData()
                
            }.onFailure { exception ->
                setEffect { EarnContract.Effect.ShowError("Failed to add game reward: ${exception.message}") }
            }
        } catch (e: Exception) {
            setEffect { EarnContract.Effect.ShowError("Error adding game reward: ${e.message}") }
        }
    }
    
    /**
     * 获取游戏次数
     * 
     * Requirements: 9.8 - 当用户参与游戏时，系统应记录游戏次数和获得的奖励
     * 
     * @param gameType 游戏类型
     * @return 当日游戏次数
     */
    suspend fun getGameCount(gameType: com.example.step0724.data.model.GameType): Int {
        return try {
            earnRepository.getGameCount(gameType)
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 重启倒计时定时器
     * 
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     */
    fun restartCountdownTimer() {
        // 先停止现有的定时器
        stopCountdownTimer()
        // 重新启动定时器
        startCountdownTimer()
    }
    
    /**
     * 停止倒计时定时器（当页面不可见时调用以节省资源）
     */
    fun stopCountdownTimer() {
        countdownJob?.cancel()
        countdownJob = null
    }
    
    /**
     * 检查倒计时定时器是否正在运行
     * 
     * @return true表示定时器正在运行，false表示已停止
     */
    fun isCountdownTimerRunning(): Boolean {
        return countdownJob?.isActive == true
    }
    
    /**
     * 获取当前倒计时状态信息（用于调试）
     * 
     * @return 包含倒计时状态信息的字符串
     */
    fun getCountdownStatus(): String {
        val isRunning = isCountdownTimerRunning()
        val currentTime = timeManager.getTimeUntilMidnight()
        val lastUpdate = lastUpdateDate
        
        return "倒计时状态: ${if (isRunning) "运行中" else "已停止"}, " +
                "当前倒计时: $currentTime, " +
                "上次更新日期: $lastUpdate"
    }
    
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
        stepMonitoringJob?.cancel()
    }
}