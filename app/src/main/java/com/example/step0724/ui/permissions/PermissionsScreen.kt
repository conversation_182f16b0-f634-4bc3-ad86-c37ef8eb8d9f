package com.example.step0724.ui.permissions

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.step0724.R
import com.example.step0724.ui.permissions.dialogs.PermissionExplanationDialog
import com.example.step0724.ui.permissions.dialogs.PermissionType
import com.example.step0724.ui.theme.Gray100
import com.example.step0724.ui.theme.PrimaryBlue
import com.example.step0724.ui.theme.TextPrimary
import com.example.step0724.ui.theme.TextSecondary

@Composable
fun PermissionsScreen(
    onNavigateBack: () -> Unit,
    onRequestActivityRecognition: () -> Unit,
    onRequestBatteryOptimization: () -> Unit,
    onRequestNotification: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PermissionsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()
    var showErrorDialog by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    // 页面重新进入时刷新权限状态
    LaunchedEffect(Unit) {
        viewModel.refreshPermissions()
    }
    
    // 监听生命周期，当页面重新获得焦点时刷新权限状态
    // 这对电池优化权限特别重要，因为它是跳转到设置页面的
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 页面重新获得焦点时刷新权限状态
                viewModel.refreshPermissions()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is PermissionsEffect.RequestActivityRecognitionPermission -> {
                    onRequestActivityRecognition()
                }
                is PermissionsEffect.RequestBatteryOptimizationPermission -> {
                    onRequestBatteryOptimization()
                }
                is PermissionsEffect.RequestNotificationPermission -> {
                    onRequestNotification()
                }
                is PermissionsEffect.ShowError -> {
                    errorMessage = effect.message
                    showErrorDialog = true
                }
                else -> {}
            }
        }
    }

    // 主要内容区域
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                color = Gray100,
                shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
            // 运动权限
            PermissionItem(
                icon = R.mipmap.per_phys,
                title = "Physical activity",
                subtitle = "Track your fitness movements",
                isGranted = state.hasActivityRecognition,
                canToggle = true,
                onToggle = { 
                    android.util.Log.d("PermissionsScreen", "Physical activity permission clicked")
                    viewModel.sendIntent(PermissionsIntent.RequestActivityRecognition)
                }
            )

            // 电池优化
            PermissionItem(
                icon = R.mipmap.per_opt,
                title = "Protected App",
                subtitle = "Disable battery optimization",
                isGranted = state.hasBatteryOptimization,
                canToggle = true,
                onToggle = { 
                    viewModel.sendIntent(PermissionsIntent.RequestBatteryOptimization)
                }
            )

            // 通知权限
            PermissionItem(
                icon = R.mipmap.per_notify,
                title = "Notification",
                subtitle = "Notify when your coin amount changes",
                isGranted = if (state.hasNotificationPermission) state.isNotificationEnabled else false,
                canToggle = state.canToggleNotification,
                onToggle = { enabled ->
                    if (state.hasNotificationPermission) {
                        viewModel.sendIntent(PermissionsIntent.ToggleNotificationPermission(enabled))
                    } else {
                        viewModel.sendIntent(PermissionsIntent.RequestNotificationPermission)
                    }
                }
            )
        }

    // 错误对话框
    if (showErrorDialog) {
        AlertDialog(
            onDismissRequest = { showErrorDialog = false },
            title = { Text("Error") },
            text = { Text(errorMessage) },
            confirmButton = {
                TextButton(onClick = { showErrorDialog = false }) {
                    Text("OK")
                }
            }
        )
    }
    
    // 权限说明弹窗
    if (state.showPermissionExplanationDialog && state.currentExplanationPermission != null) {
        PermissionExplanationDialog(
            permissionType = when (state.currentExplanationPermission) {
                PermissionExplanationType.ACTIVITY_RECOGNITION -> PermissionType.PHYSICAL_ACTIVITY
                PermissionExplanationType.BATTERY_OPTIMIZATION -> PermissionType.BATTERY_OPTIMIZATION
                PermissionExplanationType.NOTIFICATION -> PermissionType.NOTIFICATION
                null -> PermissionType.NOTIFICATION
            },
            onAllow = {
                viewModel.sendIntent(PermissionsIntent.ConfirmPermissionExplanation)
            },
            onCancel = {
                viewModel.sendIntent(PermissionsIntent.DismissPermissionExplanationDialog)
            }
        )
    }
}



@Composable
private fun PermissionItem(
    icon: Int,
    title: String,
    subtitle: String,
    isGranted: Boolean,
    canToggle: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 80.dp)
            .background(color = Color.White, shape = RoundedCornerShape(16.dp))
            .padding(16.dp).clickable(
                enabled = if (title == "Physical activity" || title == "Protected App") {
                    !isGranted
                } else {
                    true
                }
            ) {
                onToggle(!isGranted)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = icon),
            contentDescription = title,
            modifier = Modifier.size(32.dp)
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                lineHeight = 17.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )
            Text(
                text = subtitle,
                fontSize = 12.sp,
                lineHeight = 15.sp,
                color = TextSecondary,
                modifier = Modifier.padding(top = 2.dp)
            )
        }

        if (isGranted && (title == "Physical activity" || title == "Protected App")) {
            Image(
                painter = painterResource(id = R.drawable.ic_check),
                contentDescription = "Granted",
                modifier = Modifier.size(24.dp)
            )
        } else {
            Switch(
                checked = isGranted,
                onCheckedChange = onToggle,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = PrimaryBlue,
                    uncheckedThumbColor = Color.White,
                    uncheckedTrackColor = Color(0xFFE0ECFB),
                    uncheckedBorderColor = Color.Transparent,
                )
            )
        }
    }
}