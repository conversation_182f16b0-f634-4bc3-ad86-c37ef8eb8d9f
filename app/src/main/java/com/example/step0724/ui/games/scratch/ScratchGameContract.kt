package com.example.step0724.ui.games.scratch

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.data.model.ScratchCard
import com.example.step0724.data.model.ScratchGameResult

/**
 * 刮刮乐游戏Intent
 */
sealed class ScratchGameIntent : UiIntent {
    object InitGame : ScratchGameIntent()
    object StartNewGame : ScratchGameIntent()
    data class ScratchCard(val cardId: Int, val percentage: Float) : ScratchGameIntent()
    object ClaimReward : ScratchGameIntent()
    object NavigateBack : ScratchGameIntent()
}

/**
 * 刮刮乐游戏状态
 */
data class ScratchGameState(
    val cards: List<ScratchCard> = emptyList(),
    val isGameComplete: Boolean = false,
    val canScratch: Boolean = true,
    val dailyFreeChances: Int = 3,
    val usedChances: Int = 0,
    val gameResult: ScratchGameResult? = null,
    val isLoading: Boolean = false,
    val coins: Int = 0,
    val showRewardDialog: Boolean = false
) : UiState

/**
 * 刮刮乐游戏效果
 */
sealed class ScratchGameEffect : UiEffect {
    object NavigateBack : ScratchGameEffect()
    data class ShowRewardDialog(val result: ScratchGameResult) : ScratchGameEffect()
    data class ShowMessage(val message: String) : ScratchGameEffect()
    data class UpdateCoins(val coins: Int) : ScratchGameEffect()
}
