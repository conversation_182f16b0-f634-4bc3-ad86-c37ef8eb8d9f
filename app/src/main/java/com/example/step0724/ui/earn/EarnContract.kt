package com.example.step0724.ui.earn

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.data.model.Task
import com.example.step0724.data.model.TaskType

/**
 * Earn功能的MVI契约定义
 * 基于需求11.1, 11.2, 11.3定义用户意图、UI状态和副作用
 */
object EarnContract {
    
    /**
     * 用户意图密封类
     * 定义用户在Earn页面可以执行的所有操作
     */
    sealed class Intent : UiIntent {
        /**
         * 加载Earn页面数据
         */
        object LoadData : Intent()
        
        /**
         * 刷新Earn页面数据
         */
        object RefreshData : Intent()
        
        /**
         * 领取任务奖励
         * @param taskType 要领取奖励的任务类型
         */
        data class ClaimTask(val taskType: TaskType) : Intent()
        
        /**
         * 导航到任务相关页面
         * @param taskType 任务类型，用于确定导航目标
         */
        data class NavigateToTask(val taskType: TaskType) : Intent()
        
        /**
         * 导航到Lucky Wheel游戏页面
         */
        object NavigateToLuckyWheel : Intent()
        
        /**
         * 导航到Egg Smash游戏页面
         */
        object NavigateToEggSmash : Intent()
        
        /**
         * 导航到Lucky Scratch游戏页面
         */
        object NavigateToLuckyScratch : Intent()
        
        /**
         * 通用游戏导航
         * @param gameType 游戏类型
         */
        data class NavigateToGame(val gameType: com.example.step0724.data.model.GameType) : Intent()
        
        /**
         * 导航到Steps页面
         */
        object NavigateToSteps : Intent()
        
        /**
         * 更新倒计时显示
         */
        object UpdateCountdown : Intent()
    }
    
    /**
     * UI状态数据类
     * 管理Earn页面的所有UI状态
     */
    data class UiState(
        /**
         * 当前金币余额
         */
        val coins: Int = 0,
        
        /**
         * 每日任务列表
         */
        val dailyTasks: List<Task> = emptyList(),
        
        /**
         * 其他任务列表
         */
        val otherTasks: List<Task> = emptyList(),
        
        /**
         * 距离下次重置的倒计时显示（格式：HH:MM）
         */
        val timeUntilReset: String = "24:00",
        
        /**
         * 连续签到天数
         */
        val consecutiveCheckIns: Int = 0,
        
        /**
         * 是否正在加载数据
         */
        val isLoading: Boolean = false,
        
        /**
         * 当前步数（用于实时更新步数任务状态）
         */
        val currentSteps: Int = 0,
        
        /**
         * 错误信息（如果有）
         */
        val errorMessage: String? = null
    ) : com.example.step0724.core.mvi.UiState
    
    /**
     * 副作用密封类
     * 处理一次性事件和导航操作
     */
    sealed class Effect : UiEffect {
        /**
         * 导航到Steps页面
         */
        object NavigateToSteps : Effect()
        
        /**
         * 导航到Lucky Wheel游戏页面
         */
        object NavigateToLuckyWheel : Effect()
        
        /**
         * 导航到Egg Smash游戏页面
         */
        object NavigateToEggSmash : Effect()
        
        /**
         * 导航到Lucky Scratch游戏页面
         */
        object NavigateToLuckyScratch : Effect()
        
        /**
         * 通用游戏导航效果
         * @param gameType 游戏类型
         */
        data class NavigateToGame(val gameType: com.example.step0724.data.model.GameType) : Effect()
        
        /**
         * 显示奖励动画
         * @param coins 获得的金币数量
         */
        data class ShowRewardAnimation(val coins: Int) : Effect()
        
        /**
         * 显示错误信息
         * @param message 错误消息
         */
        data class ShowError(val message: String) : Effect()
        
        /**
         * 显示成功消息
         * @param message 成功消息
         */
        data class ShowSuccess(val message: String) : Effect()
    }
}