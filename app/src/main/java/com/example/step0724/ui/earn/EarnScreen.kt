package com.example.step0724.ui.earn

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.step0724.R
import com.example.step0724.data.model.GameType
import com.example.step0724.data.model.Task
import com.example.step0724.data.model.TaskStatus
import com.example.step0724.ui.components.TaskItem
import com.example.step0724.ui.theme.*

@Composable
fun EarnScreen(
    viewModel: EarnViewModel = hiltViewModel(),
    onNavigateToSteps: () -> Unit = {},
    onNavigateToLuckyWheel: () -> Unit = {},
    onNavigateToEggSmash: () -> Unit = {},
    onNavigateToLuckyScratch: () -> Unit = {}
) {
    val uiState by viewModel.state.collectAsStateWithLifecycle()
    
    // 页面进入时的初始化
    LaunchedEffect(Unit) {
        viewModel.sendIntent(EarnContract.Intent.LoadData)
        viewModel.onPageEntered()
    }
    
    // 页面离开时的清理
    DisposableEffect(Unit) {
        onDispose {
            viewModel.onPageLeft()
        }
    }
    
    // Handle navigation effects
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is EarnContract.Effect.NavigateToSteps -> {
                    onNavigateToSteps()
                }
                is EarnContract.Effect.NavigateToLuckyWheel -> {
                    onNavigateToLuckyWheel()
                }
                is EarnContract.Effect.NavigateToEggSmash -> {
                    onNavigateToEggSmash()
                }
                is EarnContract.Effect.NavigateToLuckyScratch -> {
                    onNavigateToLuckyScratch()
                }
                is EarnContract.Effect.NavigateToGame -> {
                    when (effect.gameType) {
                        GameType.LUCKY_WHEEL -> onNavigateToLuckyWheel()
                        GameType.EGG_SMASH -> onNavigateToEggSmash()
                        GameType.LUCKY_SCRATCH -> onNavigateToLuckyScratch()
                    }
                }
                is EarnContract.Effect.ShowError -> {
                    // TODO: Show error snackbar or toast
                }
                is EarnContract.Effect.ShowSuccess -> {
                    // TODO: Show success snackbar or toast
                }
                is EarnContract.Effect.ShowRewardAnimation -> {
                    // TODO: Show reward animation
                }
            }
        }
    }
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Spacer(modifier = Modifier.height(12.dp))
        }

        item {
            MoreGamesSection(
                onLuckyWheelClick = {
                    viewModel.sendIntent(EarnContract.Intent.NavigateToLuckyWheel)
                },
                onEggSmashClick = {
                    viewModel.sendIntent(EarnContract.Intent.NavigateToEggSmash)
                },
                onLuckyScratchClick = {
                    viewModel.sendIntent(EarnContract.Intent.NavigateToLuckyScratch)
                }
            )
        }
        
        item {
            DailyTasksSection(
                tasks = uiState.dailyTasks,
                timeUntilReset = uiState.timeUntilReset,
                onTaskClick = { task ->
                    when (task.status) {
                        TaskStatus.NOT_COMPLETED -> {
                            viewModel.sendIntent(EarnContract.Intent.NavigateToTask(task.type))
                        }
                        TaskStatus.NOT_ELIGIBLE -> {
                            // Not eligible, no action
                        }
                        TaskStatus.COMPLETED -> {
                            viewModel.sendIntent(EarnContract.Intent.ClaimTask(task.type))
                        }
                        TaskStatus.CLAIMED -> {
                            // Already claimed, no action
                        }
                    }
                }
            )
        }
        
        item {
            OtherTasksSection(
                tasks = uiState.otherTasks,
                onTaskClick = { task ->
                    when (task.status) {
                        TaskStatus.NOT_COMPLETED -> {
                            viewModel.sendIntent(EarnContract.Intent.NavigateToTask(task.type))
                        }
                        TaskStatus.NOT_ELIGIBLE -> {
                            // Not eligible, no action
                        }
                        TaskStatus.COMPLETED -> {
                            viewModel.sendIntent(EarnContract.Intent.ClaimTask(task.type))
                        }
                        TaskStatus.CLAIMED -> {
                            // Already claimed, no action
                        }
                    }
                }
            )
        }
    }
}


@Composable
fun MoreGamesSection(
    onLuckyWheelClick: () -> Unit,
    onEggSmashClick: () -> Unit,
    onLuckyScratchClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
            .background(color = Color.White, shape = RoundedCornerShape(16.dp))
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        
        Row(
            modifier = Modifier.fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            GameCard(
                iconRes = R.drawable.gm_wheel,
                onClick = onLuckyWheelClick,
                modifier = Modifier.weight(1f)
            )
            
            GameCard(
                iconRes = R.drawable.gm_egg,
                onClick = onEggSmashClick,
                modifier = Modifier.weight(1f)
            )
            
            GameCard(
                iconRes = R.drawable.gm_scratch,
                onClick = onLuckyScratchClick,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
fun GameCard(
    iconRes: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .aspectRatio(1f)
                .paint(painterResource(id = iconRes), contentScale = ContentScale.Crop)
                .clickable { onClick() }
        )
}

@Composable
fun DailyTasksSection(
    tasks: List<Task>,
    timeUntilReset: String,
    onTaskClick: (Task) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Daily tasks",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = TextPrimary
            )
            
            CountdownTimer(timeUntilReset = timeUntilReset)
        }
        
        tasks.forEach { task ->
            TaskItem(
                task = task,
                onClick = { onTaskClick(task) }
            )
        }
    }
}

@Composable
private fun OtherTasksSection(
    tasks: List<Task>,
    onTaskClick: (Task) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "Other tasks",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
        
        tasks.forEach { task ->
            TaskItem(
                task = task,
                onClick = { onTaskClick(task) }
            )
        }
    }
}





@Composable
fun CountdownTimer(
    timeUntilReset: String
) {
    Row(
        modifier = Modifier
            .background(color = Color.White, shape = RoundedCornerShape(10.dp))
            .padding(horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_time),
            contentDescription = "Time",
            modifier = Modifier.size(16.dp)
        )
        Text(
            text = timeUntilReset,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.text_secondary)
        )
    }
}
