package com.example.step0724.ui.profile.dialogs

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import kotlinx.coroutines.launch
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.domain.usecase.UnitConversionUseCase
import com.example.step0724.R

/**
 * 步长设置弹窗 - 基于需求4.8和需求5.3
 * 需求4.8：根据单位制式弹出相应的步长设置弹窗，默认值为71cm（2 ft 4 in）
 * 需求5.3：英制单位模式下显示英制单位的设置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StepLengthDialog(
    currentStepLength: Double,
    unitSystem: UnitSystem,
    unitConversionUseCase: UnitConversionUseCase,
    onConfirm: (Double) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isMetric = unitSystem == UnitSystem.METRIC
    
    var stepLengthValue by remember { mutableDoubleStateOf(currentStepLength) }
    
    // 英制模式下的英尺和英寸
    var feet by remember { 
        mutableIntStateOf(
            if (isMetric) 2 else unitConversionUseCase.cmToFeetInches(currentStepLength).first
        ) 
    }
    var inches by remember { 
        mutableIntStateOf(
            if (isMetric) 4 else unitConversionUseCase.cmToFeetInches(currentStepLength).second
        ) 
    }
    
    // 公制模式下的厘米
    var cmText by remember { 
        mutableStateOf(
            if (isMetric) String.format("%.0f", currentStepLength) else "71"
        ) 
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {         
       // 标题
                Text(
                    text = "Step Length",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                )
                
                if (isMetric) {
                    // 公制模式：厘米数字选择器
                    Row(
                        modifier = Modifier.padding(vertical = 32.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        StepLengthNumberPicker(
                            value = stepLengthValue.toInt(),
                            onValueChange = { newValue: Int ->
                                stepLengthValue = newValue.toDouble()
                                cmText = String.format("%.0f", stepLengthValue)
                            },
                            range = 1..120,
                            unit = "", // 不在内部显示单位
                            modifier = Modifier.width(120.dp)
                                .offset(x = 10.dp)
                        )

                        Text(
                            text = "cm",
                            fontSize = 16.sp,
                            color = Color.Gray
                        )
                    }
                } else {
                    // 英制模式：英尺和英寸双数字选择器
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 32.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 英尺选择器和单位
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            StepLengthNumberPicker(
                                value = feet,
                                onValueChange = { newValue: Int ->
                                    feet = newValue
                                },
                                range = 0..3,
                                unit = "", // 不在内部显示单位
                                modifier = Modifier.width(80.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "ft",
                                fontSize = 16.sp,
                                color = Color.Gray
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(32.dp))
                        
                        // 英寸选择器和单位
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            StepLengthNumberPicker(
                                value = inches,
                                onValueChange = { newValue: Int ->
                                    inches = newValue
                                },
                                range = 0..11,
                                unit = "", // 不在内部显示单位
                                modifier = Modifier.width(80.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "in",
                                fontSize = 16.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
       
                // 按钮区域 - 需求5.4：保存、取消逻辑
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.light_gray)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                    
                    // 保存按钮
                    Button(
                        onClick = {
                            // 转换为厘米存储
                            val stepLengthInCm = if (isMetric) {
                                stepLengthValue
                            } else {
                                unitConversionUseCase.feetInchesToCm(feet, inches)
                            }
                            onConfirm(stepLengthInCm)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.primary_blue)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Save",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun StepLengthNumberPicker(
    value: Int,
    onValueChange: (Int) -> Unit,
    range: IntRange,
    unit: String,
    modifier: Modifier = Modifier
) {
    val coroutineScope = rememberCoroutineScope()
    
    // 创建数值列表
    val values = remember(range) { range.toList() }
    
    // 找到当前选中值的索引
    val initialIndex = remember(values, value) {
        values.indexOfFirst { it == value }.coerceAtLeast(0)
    }
    
    // LazyColumn 状态
    val lazyListState = rememberLazyListState(
        initialFirstVisibleItemIndex = initialIndex
    )
    
    // 动画状态（保持原有的缩放效果）
    val animatedScale = remember { Animatable(1f) }
    var isDragging by remember { mutableStateOf(false) }
    
    // 当前滚动位置的中心项目索引
    var currentCenterIndex by remember { mutableIntStateOf(initialIndex) }
    
    // 项目高度
    val itemHeight = 35
    
    // 实时监听滚动位置，更新当前中心项目
    LaunchedEffect(lazyListState.firstVisibleItemIndex, lazyListState.firstVisibleItemScrollOffset) {
        val visibleItems = lazyListState.layoutInfo.visibleItemsInfo
        if (visibleItems.isNotEmpty()) {
            // 计算视口中心
            val viewportStart = lazyListState.layoutInfo.viewportStartOffset
            val viewportEnd = lazyListState.layoutInfo.viewportEndOffset
            val centerPosition = viewportStart + (viewportEnd - viewportStart) / 2f

            // 找到最接近中心的项目
            val closestItem = visibleItems.minByOrNull {
                kotlin.math.abs((it.offset + it.size / 2f) - centerPosition)
            }

            closestItem?.let { item ->
                currentCenterIndex = item.index
            }
        }
    }
    
    // 监听滚动停止事件
    LaunchedEffect(lazyListState.isScrollInProgress) {
        if (!lazyListState.isScrollInProgress) {
            val visibleItems = lazyListState.layoutInfo.visibleItemsInfo
            if (visibleItems.isNotEmpty()) {
                // 计算视口中心
                val viewportStart = lazyListState.layoutInfo.viewportStartOffset
                val viewportEnd = lazyListState.layoutInfo.viewportEndOffset
                val centerPosition = viewportStart + (viewportEnd - viewportStart) / 2f

                // 找到最接近中心的项目
                val closestItem = visibleItems.minByOrNull {
                    kotlin.math.abs((it.offset + it.size / 2f) - centerPosition)
                }

                closestItem?.let { item ->
                    val centerIndex = item.index
                    val newValue = values[centerIndex]
                    
                    // 更新选中值并对齐到中心
                    if (newValue != value) {
                        onValueChange(newValue)
                    }
                    lazyListState.animateScrollToItem(centerIndex)
                }
            }
        }
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        // 背景指示器（保持原有样式）
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 蓝色上划线 - 根据单位调整宽度
            val lineWidth = if (unit == "cm") 80.dp else  60.dp
            
            Box(
                modifier = Modifier
                    .width(lineWidth)
                    .height(3.dp)
                    .background(colorResource(R.color.primary_blue))
                    .graphicsLayer {
                        scaleX = animatedScale.value
                        alpha = if (isDragging) 1f else 0.8f
                    }
            )
            
            Spacer(modifier = Modifier.height((itemHeight + 1).dp))
            
            // 蓝色下划线
            Box(
                modifier = Modifier
                    .width(lineWidth)
                    .height(3.dp)
                    .background(colorResource(R.color.primary_blue))
                    .graphicsLayer {
                        scaleX = animatedScale.value
                        alpha = if (isDragging) 1f else 0.8f
                    }
            )
        }

        // 滚动列表
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxWidth()
                .height((itemHeight * 3).dp)
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = {
                            isDragging = true
                            coroutineScope.launch {
                                animatedScale.animateTo(1.05f, tween(100))
                            }
                        },
                        onDragEnd = {
                            isDragging = false
                            coroutineScope.launch {
                                animatedScale.animateTo(1f, tween(150))
                            }
                        }
                    ) { _, _ -> }
                },
            horizontalAlignment = Alignment.CenterHorizontally,
            contentPadding = PaddingValues(vertical = itemHeight.dp)
        ) {
            itemsIndexed(values) { index, itemValue ->
                // 基于当前滚动位置的中心索引来决定显示状态
                val isCenterItem = index == currentCenterIndex
                val isAdjacentItem = kotlin.math.abs(index - currentCenterIndex) == 1
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(itemHeight.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    when {
                        isCenterItem -> {
                            // 中心项目（大，蓝色）
                            // 根据单位调整字体大小，如果单位为空则使用18sp
                            val fontSize = if (unit == "cm") 18.sp else 18.sp
                            
                            Text(
                                text = "$itemValue",
                                fontSize = fontSize,
                                fontWeight = FontWeight.Bold,
                                color = colorResource(R.color.primary_blue),
                                modifier = Modifier.graphicsLayer {
                                    scaleX = animatedScale.value
                                    scaleY = animatedScale.value
                                }
                            )
                            
                            // 只有当单位不为空时才显示单位
                            if (unit.isNotEmpty()) {
                                val unitFontSize = if (unit == "cm") 18.sp else 18.sp
                                val spacerWidth = if (unit == "cm") 8.dp else 6.dp
                                val topPadding = if (unit == "cm") 8.dp else 6.dp
                                
                                Spacer(modifier = Modifier.width(spacerWidth))
                                
                                Text(
                                    text = unit,
                                    fontSize = unitFontSize,
                                    color = Color.Gray,
                                    modifier = Modifier.padding(top = topPadding)
                                )
                            }
                        }
                        isAdjacentItem -> {
                            // 相邻项目（较小，灰色）
                            Text(
                                text = "$itemValue",
                                fontSize = 14.sp,
                                color = Color(0xFFE0ECFB),
                                fontWeight = FontWeight.Normal
                            )
                        }
                        else -> {
                            // 其他项目（透明，但占位）
                            Text(
                                text = "$itemValue",
                                fontSize = 14.sp,
                                color = Color.Transparent,
                                fontWeight = FontWeight.Normal
                            )
                        }
                    }
                }
            }
        }
    }
}
