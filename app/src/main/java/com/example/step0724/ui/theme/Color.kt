package com.example.step0724.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Step Counter App Colors
val Blue500 = Color(0xFF2196F3)
val Blue400 = Color(0xFF42A5F5)
val Blue300 = Color(0xFF64B5F6)
val BlueGradientStart = Color(0xFF64B5F6)
val BlueGradientEnd = Color(0xFF2196F3)

val Orange500 = Color(0xFFFF9800)
val Orange400 = Color(0xFFFFA726)

val Gray100 = Color(0xFFF5F5F5)
val Gray200 = Color(0xFFEEEEEE)
val Gray300 = Color(0xFFE0E0E0)
val Gray600 = Color(0xFF757575)
val Gray800 = Color(0xFF424242)

// 文字颜色
val TextPrimary = Color(0xFF333333)
val TextSecondary = Color(0xFF999999)

// 主题蓝色
val PrimaryBlue = Color(0xFF4893FC)
val LightGray = Color(0xFFEEEEEE)
val PrimaryBackground = Color(0XFFEFF6FF)