package com.example.step0724.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.R
import com.example.step0724.navigation.TabDestination
import com.example.step0724.ui.theme.Blue500
import com.example.step0724.ui.theme.PrimaryBlue

@Composable
fun BottomNavigationBar(
    selectedTab: TabDestination,
    onTabSelected: (TabDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(color = Color.Transparent)
            .fillMaxWidth()
    ) {
        Box(modifier = Modifier
            .fillMaxWidth()
            .height(68.dp)
            .align(Alignment.BottomCenter)
            .background(Color.White)
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            TabItem(
                selected = selectedTab == TabDestination.PROFILE,
                onClick = { onTabSelected(TabDestination.PROFILE) },
                selectedIcon = R.mipmap.tab_profile_s,
                unselectedIcon = R.mipmap.tab_profile_n,
                title = "Profile"
            )

            TabItem(
                selected = selectedTab == TabDestination.STEPS,
                onClick = { onTabSelected(TabDestination.STEPS) },
                selectedIcon = R.mipmap.tab_steps_s,
                unselectedIcon = R.mipmap.tab_steps_n,
                title = "Steps"
            )

            TabItem(
                selected = selectedTab == TabDestination.EARN,
                onClick = { onTabSelected(TabDestination.EARN) },
                selectedIcon = R.mipmap.tab_earn_s,
                unselectedIcon = R.mipmap.tab_earn_n,
                title = "Earn"
            )

            TabItem(
                selected = selectedTab == TabDestination.REPORT,
                onClick = { onTabSelected(TabDestination.REPORT) },
                selectedIcon = R.mipmap.tab_report_s,
                unselectedIcon = R.mipmap.tab_report_n,
                title = "Report"
            )
        }
    }
}

@Composable
private fun TabItem(
    selected: Boolean,
    onClick: () -> Unit,
    selectedIcon: Int,
    unselectedIcon: Int,
    title: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            onClick = onClick
            )
            .padding(bottom = 8.dp)
    ) {
        Image(
            painter = painterResource(id = if (selected) selectedIcon else unselectedIcon),
            contentDescription = title,
        )

        Text(
            text = title,
            color = if (selected) PrimaryBlue else Color(0xFFDDDDDD),
            fontSize = 12.sp,
            fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal
        )
    }
}
