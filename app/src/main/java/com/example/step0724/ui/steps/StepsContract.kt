package com.example.step0724.ui.steps

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.data.model.UnitSystem

sealed class StepsIntent : UiIntent {
    object LoadSteps : StepsIntent()
    object RefreshData : StepsIntent()
    object PermissionGranted : StepsIntent()
    object ShowStepGoalDialog : StepsIntent()
    object DismissStepGoalDialog : StepsIntent()
    data class UpdateStepGoal(val goal: Int) : StepsIntent()
}

data class StepsUiState(
    val currentSteps: Int = 0,
    val stepGoal: Int = 6000,
    val distance: Double = 0.0,
    val calories: Double = 0.0,
    val walkingTimeHours: Double = 0.0,
    val walkingTimeMinutes: Int = 0,
    val coins: Int = 0,
    val unitSystem: UnitSystem = UnitSystem.METRIC,
    val isLoading: Boolean = false,
    val showStepGoalDialog: Boolean = false
) : UiState

sealed class StepsEffect : UiEffect {
    object ShowPermissionDialog : StepsEffect()
    data class ShowError(val message: String) : StepsEffect()
}