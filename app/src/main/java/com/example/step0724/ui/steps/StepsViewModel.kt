package com.example.step0724.ui.steps

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.permission.PermissionManager
import com.example.step0724.data.repository.SettingsRepository
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.service.StepCountingService
import com.example.step0724.domain.usecase.CalculateStatsUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class StepsViewModel @Inject constructor(
    private val stepCountingService: StepCountingService,
    private val stepRepository: StepRepository,
    private val settingsRepository: SettingsRepository,
    private val calculateStatsUseCase: CalculateStatsUseCase,
    private val permissionManager: PermissionManager
) : MviViewModel<StepsIntent, StepsUiState, StepsEffect>() {

    override fun setInitialState(): StepsUiState = StepsUiState()

    init {
        sendIntent(StepsIntent.LoadSteps)
        observeDataChanges()
    }

    private fun observeDataChanges() {
        viewModelScope.launch {
            // 需求9：连接传感器数据流到UI，实现实时步数、距离、卡路里、时间显示
            combine(
                stepRepository.getTodaySteps(), // 从Repository获取实时步数
                settingsRepository.getUserSettings()
            ) { steps, settings ->
                // 需求8.3：当灵敏度设置改变时，更新传感器检测器
                stepCountingService.updateSensitivity(settings.sensitivity)
                
                // 需求2.3：根据公式计算相关统计数据
                val distance = calculateStatsUseCase.calculateDistance(
                    steps, settings.stepLength, settings.unitSystem
                )
                val calories = calculateStatsUseCase.calculateCalories(steps, settings.weight)
                val walkingTime = calculateStatsUseCase.formatWalkingTime(steps)
                val walkingTimeHours = walkingTime.first.toDouble()
                val walkingTimeMinutes = walkingTime.second
                val coins = steps / 100 * 100 // 100步 = 100金币，按100步的倍数计算

                // 需求2.5：处理单位制式切换的UI更新
                setState {
                    copy(
                        currentSteps = steps,
                        stepGoal = settings.stepGoal,
                        distance = distance,
                        calories = calories,
                        walkingTimeHours = walkingTimeHours,
                        walkingTimeMinutes = walkingTimeMinutes,
                        coins = coins,
                        unitSystem = settings.unitSystem,
                        isLoading = false
                    )
                }
            }.launchIn(viewModelScope)
        }
    }

    override suspend fun handleIntent(intent: StepsIntent) {
        when (intent) {
            is StepsIntent.LoadSteps -> {
                setState { copy(isLoading = true) }
                
                // 检查权限
                if (!permissionManager.hasActivityRecognitionPermission()) {
                    setEffect { StepsEffect.ShowPermissionDialog }
                    setState { copy(isLoading = false) }
                    return
                }
                
                // 启动计步服务
                if (!stepCountingService.isRunning()) {
                    stepCountingService.startCounting()
                }
            }
            
            is StepsIntent.PermissionGranted -> {
                // 权限授予后立即启动计步服务
                if (!stepCountingService.isRunning()) {
                    stepCountingService.startCounting()
                }
                // 重新加载数据
                sendIntent(StepsIntent.LoadSteps)
            }
            
            is StepsIntent.RefreshData -> {
                sendIntent(StepsIntent.LoadSteps)
            }
            

            
            is StepsIntent.ShowStepGoalDialog -> {
                setState { copy(showStepGoalDialog = true) }
            }
            
            is StepsIntent.DismissStepGoalDialog -> {
                setState { copy(showStepGoalDialog = false) }
            }
            
            is StepsIntent.UpdateStepGoal -> {
                updateStepGoal(intent.goal)
            }
        }
    }

    private fun updateStepGoal(goal: Int) {
        viewModelScope.launch {
            try {
                settingsRepository.updateStepGoal(goal)
                setState { copy(showStepGoalDialog = false) }
            } catch (e: Exception) {
                setEffect { StepsEffect.ShowError("Failed to update step goal: ${e.message}") }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 不要在这里停止计步服务，因为需要后台运行
    }
}