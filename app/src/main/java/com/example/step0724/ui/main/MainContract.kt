package com.example.step0724.ui.main

import com.example.step0724.core.mvi.UiEffect
import com.example.step0724.core.mvi.UiIntent
import com.example.step0724.core.mvi.UiState
import com.example.step0724.navigation.TabDestination

sealed class MainIntent : UiIntent {
    data class SelectTab(val tab: TabDestination) : MainIntent()
    data class SyncTabWithNavigation(val tab: TabDestination) : MainIntent()
    object ToggleMenu : MainIntent()
    object CloseMenu : MainIntent()
    object PermissionGranted : MainIntent()
}

data class MainUiState(
    val selectedTab: TabDestination = TabDestination.STEPS,
    val isMenuVisible: Boolean = false
) : UiState

sealed class MainEffect : UiEffect {
    data class NavigateToTab(val destination: TabDestination) : MainEffect()
}