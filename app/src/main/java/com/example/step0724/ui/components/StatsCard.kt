package com.example.step0724.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.R
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.ui.theme.Blue500
import com.example.step0724.ui.theme.TextPrimary
import com.example.step0724.ui.theme.TextSecondary

@Composable
fun StatsCard(
    distance: Double,
    calories: Double,
    walkingTimeHours: Double,
    walkingTimeMinutes: Int,
    unitSystem: UnitSystem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color.White,
                shape = RoundedCornerShape(20.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        StatItem(
            icon = R.mipmap.icon_kal,
            value = String.format("%.1f", calories),
            unit = "kcal",
            iconTint = Blue500
        )
        
        StatItem(
            icon = R.mipmap.icon_des,
            value = String.format("%.2f", distance),
            unit = if (unitSystem == UnitSystem.METRIC) "km" else "mile",
            iconTint = Blue500
        )
        
        StatItem(
            icon = R.mipmap.icon_time,
            value = String.format("%d:%02d", walkingTimeHours.toInt(), walkingTimeMinutes),
            unit = "h",
            iconTint = Blue500
        )
    }
}

@Composable
private fun StatItem(
    icon: Int,
    value: String,
    unit: String,
    iconTint: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            contentScale = ContentScale.Fit
        )
        
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary,
            modifier = Modifier.padding(start = 4.dp)
        )
        
        Text(
            text = unit,
            fontSize = 11.sp,
            fontWeight = FontWeight.Normal,
            color = TextSecondary,
            modifier = Modifier.padding(start = 2.dp)
        )
    }
}