package com.example.step0724.ui.settings

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.external.ExternalAppManager
import com.example.step0724.data.external.ExternalAppResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val externalAppManager: ExternalAppManager
) : MviViewModel<SettingsIntent, SettingsUiState, SettingsEffect>() {

    override fun setInitialState(): SettingsUiState = SettingsUiState()

    override suspend fun handleIntent(intent: SettingsIntent) {
        when (intent) {
            is SettingsIntent.NavigateToPermissions -> {
                setEffect { SettingsEffect.NavigateToPermissions }
            }
            
            is SettingsIntent.ContactUs -> {
                handleContactUs()
            }
            
            is SettingsIntent.OpenPrivacyPolicy -> {
                handleOpenPrivacyPolicy()
            }
            
            is SettingsIntent.OpenUserAgreement -> {
                handleOpenUserAgreement()
            }
        }
    }

    private fun handleContactUs() {
        viewModelScope.launch {
            // 直接跳转，不判断可用性
            externalAppManager.openEmailApp(
                email = "<EMAIL>",
                subject = "Contact us",
                body = "Please share your thoughts with us."
            )
        }
    }

    private fun handleOpenPrivacyPolicy() {
        viewModelScope.launch {
            // 直接跳转，不判断可用性
            externalAppManager.openBrowser("https://example.com/privacy-policy")
        }
    }

    private fun handleOpenUserAgreement() {
        viewModelScope.launch {
            // 直接跳转，不判断可用性
            externalAppManager.openBrowser("https://example.com/user-agreement")
        }
    }
}