package com.example.step0724.ui.games

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.step0724.ui.theme.Step0724Theme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GamesActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 获取Intent传递的游戏类型
        val gameType = intent.getStringExtra("game_type") ?: "lucky_wheel"
        
        setContent {
            Step0724Theme {
                GamesScreen(
                    onNavigateBack = { finish() },
                    gameType = gameType
                )
            }
        }
    }
}

@Composable
fun GamesScreen(
    onNavigateBack: () -> Unit,
    gameType: String = "lucky_wheel"
) {
    val navController = rememberNavController()
    
    // 根据传入的游戏类型确定起始页面
    val startDestination = when (gameType) {
        "egg_smash" -> "egg_smash"
        "lucky_scratch" -> "lucky_scratch"
        else -> "lucky_wheel"
    }
    
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = Modifier.fillMaxSize()
    ) {
        composable("lucky_wheel") {
            LuckyWheelScreen()
        }
        
        composable("egg_smash") {
            EggSmashScreen(
                onNavigateBack = onNavigateBack
            )
        }
        
        composable("lucky_scratch") {
            LuckyScratchScreen(
                onNavigateBack = onNavigateBack
            )
        }
    }
}