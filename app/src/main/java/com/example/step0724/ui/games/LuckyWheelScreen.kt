package com.example.step0724.ui.games

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.step0724.R
import kotlinx.coroutines.delay
import kotlin.random.Random

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LuckyWheelScreen() {
    val context = LocalContext.current
    val viewModel: LuckyWheelViewModel = viewModel()
    
    var isSpinning by remember { mutableStateOf(false) }
    var selectedPosition by remember { mutableStateOf(-1) }
    var showResult by remember { mutableStateOf(false) }
    var resultMessage by remember { mutableStateOf("") }
    var showNoChancesDialog by remember { mutableStateOf(false) }
    
    // 观察每日次数状态
    val dailyChances by viewModel.dailyChances.collectAsState()
    val canPlay by viewModel.canPlay.collectAsState()
    
    // 页面显示时刷新次数
    LaunchedEffect(Unit) {
        viewModel.refreshChances()
    }
    
    // 九宫格位置定义（外圈8个位置，中间是开始按钮）
    val wheelPositions = listOf(
        0, 1, 2,  // 上排
        7, -1, 3, // 中排（-1是中间的开始按钮）
        6, 5, 4   // 下排
    )
    
    // 奖励配置
    val rewards = listOf(
        WheelReward("三等奖", 10, 0.90f, R.mipmap.wheel_coin_1_n),      // 位置0
        WheelReward("二等奖", 50, 0.05f, R.mipmap.wheel_coin_2_n),        // 位置1
        WheelReward("三等奖", 10, 0.90f, R.mipmap.wheel_coin_1_n),      // 位置2
        WheelReward("一等奖", 100, 0.04f, R.mipmap.wheel_coin_3_n),       // 位置3
        WheelReward("三等奖", 10, 0.90f, R.mipmap.wheel_coin_1_n),      // 位置4
        WheelReward("二等奖", 50, 0.05f, R.mipmap.wheel_coin_2_n),        // 位置5
        WheelReward("特等奖", 500, 0.01f, R.mipmap.wheel_coin_4_n),       // 位置6
        WheelReward("一等奖", 100, 0.04f, R.mipmap.wheel_coin_3_n)        // 位置7
    )
    
    // 旋转动画
    LaunchedEffect(isSpinning) {
        if (isSpinning) {
            val spinDuration = Random.nextLong(8000, 10000) // 8-10秒
            val finalPosition = selectRandomPosition(rewards)
            
            // 计算需要多少圈才能到达最终位置
            val currentPos = selectedPosition
            val minRotations = 6 // 至少转6圈
            val totalSteps = minRotations * 8 + (finalPosition - currentPos + 8) % 8
            
            var stepCount = 0
            val fastPhaseDuration = spinDuration - 800 // 前面快速阶段（减速阶段缩短到0.8秒）
            val slowPhaseDuration = 800L // 最后0.8秒减速阶段
            
            var currentTime = 0L
            
            // 快速阶段 - 每50ms切换一次，更快的速度
            while (currentTime < fastPhaseDuration && stepCount < totalSteps - 10) {
                selectedPosition = stepCount % 8
                delay(50L) // 更快的切换速度
                currentTime += 50L
                stepCount++
            }
            
            // 减速阶段 - 使用更平滑的减速曲线
            val remainingSteps = totalSteps - stepCount
            
            // 先进入中速阶段，再转一圈
            val midSpeedSteps = 8 // 一整圈
            var midStepCount = 0
            
            while (midStepCount < midSpeedSteps) {
                selectedPosition = stepCount % 8
                delay(100L) // 中等速度
                stepCount++
                midStepCount++
            }
            
            // 最终减速阶段
            val initialDelay = 100L // 初始延迟
            val maxDelay = 450L // 最大延迟
            
            // 计算最终位置前的额外步数（确保最慢速度时多走几个格子）
            val extraSteps = 3 + Random.nextInt(2) // 额外走3-4步
            val targetPosition = (finalPosition - extraSteps + 8) % 8
            
            // 第一阶段减速：先减速到目标位置前的几个格子
            while (stepCount % 8 != targetPosition) {
                selectedPosition = stepCount % 8
                
                // 计算剩余步数到中间目标位置
                val stepsToTarget = (targetPosition - (stepCount % 8) + 8) % 8
                if (stepsToTarget == 0) break
                
                // 使用三次方函数创建平滑的减速曲线
                val progress = 1f - (stepsToTarget.toFloat() / 8f)
                val easeOutFactor = progress * progress * progress // 三次方缓出效果
                val delayTime = (initialDelay + (maxDelay - initialDelay) * easeOutFactor).toLong()
                
                delay(delayTime)
                stepCount++
            }
            
            // 第二阶段：最慢速度，匀速走完剩余的几步到最终位置
            val slowestDelay = 500L // 最慢速度的延迟
            while (stepCount % 8 != finalPosition) {
                selectedPosition = stepCount % 8
                delay(slowestDelay)
                stepCount++
            }
            
            // 最终确保停在正确位置
            selectedPosition = finalPosition
            val reward = rewards[finalPosition]
            resultMessage = "恭喜获得${reward.name}：${reward.coins}金币！"
            showResult = true
            isSpinning = false
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // 背景图片
        Image(
            painter = painterResource(id = R.drawable.bg_wheel),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Image(
                painter = painterResource(id = R.drawable.title_wheel),
                contentDescription = "Lucky Wheel",
                modifier = Modifier
                    .fillMaxWidth()
            )
            
            // 转盘区域
            Box(
                modifier = Modifier
                    .paint(painterResource(id = R.mipmap.wheel_bg_ku), contentScale = ContentScale.Crop)
            ) {

                // 九宫格布局
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                        .offset(y = (-30).dp)
                        .padding(horizontal = 55.dp)
                ) {
                    repeat(3) { row ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            repeat(3) { col ->
                                val positionIndex = row * 3 + col
                                val wheelPosition = wheelPositions[positionIndex]
                                
                                Box(
                                    modifier = Modifier
                                        .weight(1f)
                                        .aspectRatio(1f)
                                        .padding(4.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (wheelPosition == -1) {
                                        // 中间的开始按钮
                                        StartButton(
                                            isSpinning = isSpinning,
                                            canPlay = canPlay,
                                            onClick = {
                                                if (!isSpinning && canPlay) {
                                                    if (viewModel.startGame()) {
                                                        isSpinning = true
                                                        showResult = false
                                                    } else {
                                                        showNoChancesDialog = true
                                                    }
                                                } else if (!canPlay) {
                                                    showNoChancesDialog = true
                                                }
                                            }
                                        )
                                    } else {
                                        // 奖励位置
                                        WheelItem(
                                            reward = rewards[wheelPosition],
                                            isSelected = selectedPosition == wheelPosition && isSpinning,
                                            isWinner = selectedPosition == wheelPosition && !isSpinning && showResult
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                // 每日免费次数
                Row(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(y = (-34).dp)
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Daily free chances: $dailyChances",
                        color = if (canPlay) Color.White else Color.Red,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
        
        // 结果弹窗
        if (showResult) {
            ResultDialog(
                message = resultMessage,
                onDismiss = { showResult = false }
            )
        }
        
        // 没有次数的提示弹窗
        if (showNoChancesDialog) {
            NoChancesDialog(
                onDismiss = { showNoChancesDialog = false }
            )
        }
    }
}

@Composable
private fun StartButton(
    isSpinning: Boolean,
    canPlay: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clickable(enabled = !isSpinning && canPlay) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.mipmap.wheel_coin_empty),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )
        
        Text(
            text = when {
                isSpinning -> "..."
                else -> "START"
            },
            color = if (canPlay) Color.White else colorResource(R.color.text_secondary),
            fontSize = 18.sp,
            fontFamily = FontFamily.SansSerif,
            style = TextStyle(
                shadow = Shadow(
                    color = colorResource(R.color.primary_blue),
                    offset = Offset(0f, 2f),
                    blurRadius = 1.0f
                )
            ),
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun WheelItem(
    reward: WheelReward,
    isSelected: Boolean,
    isWinner: Boolean
) {
    val imageRes = when {
        isWinner -> getSelectedImage(reward.imageRes)
        isSelected -> getSelectedImage(reward.imageRes)
        else -> reward.imageRes
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = imageRes),
            contentDescription = reward.name,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )
    }
}

@Composable
private fun ResultDialog(
    message: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "恭喜！",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        text = {
            Text(
                text = message,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

@Composable
private fun NoChancesDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "提示",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        text = {
            Text(
                text = "今日免费次数已用完，请明天再来！",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

// 数据类
data class WheelReward(
    val name: String,
    val coins: Int,
    val probability: Float,
    val imageRes: Int
)

// 根据概率选择位置
private fun selectRandomPosition(rewards: List<WheelReward>): Int {
    val random = Random.nextFloat()
    
    // 按概率分组，获取对应等级的所有位置索引
    val specialPrizeIndices = rewards.mapIndexedNotNull { index, reward -> 
        if (reward.probability == 0.01f) index else null 
    } // 特等奖 1%
    val firstPrizeIndices = rewards.mapIndexedNotNull { index, reward -> 
        if (reward.probability == 0.04f) index else null 
    } // 一等奖 4%
    val secondPrizeIndices = rewards.mapIndexedNotNull { index, reward -> 
        if (reward.probability == 0.05f) index else null 
    } // 二等奖 5%
    val thirdPrizeIndices = rewards.mapIndexedNotNull { index, reward -> 
        if (reward.probability == 0.90f) index else null 
    } // 三等奖 90%
    
    return when {
        random < 0.01f && specialPrizeIndices.isNotEmpty() -> {
            specialPrizeIndices.random()
        }
        random < 0.05f && firstPrizeIndices.isNotEmpty() -> {
            firstPrizeIndices.random()
        }
        random < 0.10f && secondPrizeIndices.isNotEmpty() -> {
            secondPrizeIndices.random()
        }
        else -> {
            thirdPrizeIndices.random()
        }
    }
}

// 获取选中状态的图片
private fun getSelectedImage(normalImageRes: Int): Int {
    return when (normalImageRes) {
        R.mipmap.wheel_coin_1_n -> R.mipmap.wheel_coin_1_s
        R.mipmap.wheel_coin_2_n -> R.mipmap.wheel_coin_2_s
        R.mipmap.wheel_coin_3_n -> R.mipmap.wheel_coin_3_s
        R.mipmap.wheel_coin_4_n -> R.mipmap.wheel_coin_4_s
        else -> normalImageRes
    }
}
