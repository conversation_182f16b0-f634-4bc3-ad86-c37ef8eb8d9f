package com.example.step0724.ui.steps

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.step0724.R
import com.example.step0724.data.model.GameType
import com.example.step0724.data.model.TaskStatus
import com.example.step0724.ui.components.CircularStepProgress
import com.example.step0724.ui.components.StatsCard
import com.example.step0724.ui.earn.DailyTasksSection
import com.example.step0724.ui.earn.EarnContract
import com.example.step0724.ui.earn.EarnViewModel
import com.example.step0724.ui.earn.MoreGamesSection
import com.example.step0724.ui.profile.dialogs.StepGoalDialog

@Composable
fun StepsScreen(
    modifier: Modifier = Modifier,
    stepsViewModel: StepsViewModel = hiltViewModel(),
    earnViewModel: EarnViewModel = hiltViewModel(),
    onNavigateToLuckyWheel: () -> Unit = {},
    onNavigateToEggSmash: () -> Unit = {},
    onNavigateToLuckyScratch: () -> Unit = {}
) {
    val stepsState by stepsViewModel.state.collectAsState()
    val earnState by earnViewModel.state.collectAsStateWithLifecycle()
    
    // 页面进入时的初始化
    LaunchedEffect(Unit) {
        earnViewModel.sendIntent(EarnContract.Intent.LoadData)
        earnViewModel.onPageEntered()
    }
    
    // 页面离开时的清理
    DisposableEffect(Unit) {
        onDispose {
            earnViewModel.onPageLeft()
        }
    }

    // 处理Steps副作用
    LaunchedEffect(stepsViewModel) {
        stepsViewModel.effect.collect { effect ->
            when (effect) {
                is StepsEffect.ShowPermissionDialog -> {
                }
                is StepsEffect.ShowError -> {
                }
            }
        }
    }
    
    // 处理Earn副作用
    LaunchedEffect(earnViewModel) {
        earnViewModel.effect.collect { effect ->
            when (effect) {
                is EarnContract.Effect.NavigateToLuckyWheel -> {
                    onNavigateToLuckyWheel()
                }
                is EarnContract.Effect.NavigateToEggSmash -> {
                    onNavigateToEggSmash()
                }
                is EarnContract.Effect.NavigateToLuckyScratch -> {
                    onNavigateToLuckyScratch()
                }
                is EarnContract.Effect.NavigateToGame -> {
                    when (effect.gameType) {
                        GameType.LUCKY_WHEEL -> onNavigateToLuckyWheel()
                        GameType.EGG_SMASH -> onNavigateToEggSmash()
                        GameType.LUCKY_SCRATCH -> onNavigateToLuckyScratch()
                    }
                }
                is EarnContract.Effect.ShowError -> {
                    // TODO: Show error snackbar or toast
                }
                is EarnContract.Effect.ShowSuccess -> {
                    // TODO: Show success snackbar or toast
                }
                is EarnContract.Effect.ShowRewardAnimation -> {
                    // TODO: Show reward animation
                }
                else -> {}
            }
        }
    }

    // 使用LazyColumn来支持滚动
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Spacer(modifier = Modifier.height(12.dp))
        }

        item {
            // 统计卡片
            StatsCard(
                distance = stepsState.distance,
                calories = stepsState.calories,
                walkingTimeHours = stepsState.walkingTimeHours,
                walkingTimeMinutes = stepsState.walkingTimeMinutes,
                unitSystem = stepsState.unitSystem
            )
        }

        item {
            // 圆形进度条
            CircularStepProgress(
                currentSteps = stepsState.currentSteps,
                goalSteps = stepsState.stepGoal,
                onGoalClick = { stepsViewModel.sendIntent(StepsIntent.ShowStepGoalDialog) }
            )
        }

        item {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 金币信息
                Text(
                    text = "100 steps = 100 coins",
                    fontSize = 14.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 兑换金币按钮
                Button(
                    onClick = { /* TODO: 实现金币兑换 */ },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(R.color.primary_origin)
                    ),
                    shape = RoundedCornerShape(25.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_play),
                        contentDescription = "Exchange",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = "Exchange coins",
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        item {
            MoreGamesSection(
                onLuckyWheelClick = {
                    earnViewModel.sendIntent(EarnContract.Intent.NavigateToLuckyWheel)
                },
                onEggSmashClick = {
                    earnViewModel.sendIntent(EarnContract.Intent.NavigateToEggSmash)
                },
                onLuckyScratchClick = {
                    earnViewModel.sendIntent(EarnContract.Intent.NavigateToLuckyScratch)
                }
            )
        }

        item {
            DailyTasksSection(
                tasks = earnState.dailyTasks,
                timeUntilReset = earnState.timeUntilReset,
                onTaskClick = { task ->
                    when (task.status) {
                        TaskStatus.NOT_COMPLETED -> {
                            earnViewModel.sendIntent(EarnContract.Intent.NavigateToTask(task.type))
                        }
                        TaskStatus.NOT_ELIGIBLE -> {
                            // Not eligible, no action
                        }
                        TaskStatus.COMPLETED -> {
                            earnViewModel.sendIntent(EarnContract.Intent.ClaimTask(task.type))
                        }
                        TaskStatus.CLAIMED -> {
                            // Already claimed, no action
                        }
                    }
                }
            )
        }
    }
    
    // Step Goal Dialog - 需求4.2：步数目标设置弹窗
    if (stepsState.showStepGoalDialog) {
        StepGoalDialog(
            currentGoal = stepsState.stepGoal,
            onConfirm = { newGoal ->
                stepsViewModel.sendIntent(StepsIntent.UpdateStepGoal(newGoal))
            },
            onDismiss = {
                stepsViewModel.sendIntent(StepsIntent.DismissStepGoalDialog)
            }
        )
    }
}

