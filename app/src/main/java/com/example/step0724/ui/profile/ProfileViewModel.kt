package com.example.step0724.ui.profile

import androidx.lifecycle.viewModelScope
import com.example.step0724.core.mvi.MviViewModel
import com.example.step0724.data.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Profile页面ViewModel - 基于需求4的Profile页面功能
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository,
    val unitConversionUseCase: com.example.step0724.domain.usecase.UnitConversionUseCase,
    private val calculateDefaultStepLengthUseCase: com.example.step0724.domain.usecase.CalculateDefaultStepLengthUseCase
) : MviViewModel<ProfileContract.Intent, ProfileContract.State, ProfileContract.Effect>() {
    
    override fun setInitialState(): ProfileContract.State {
        return ProfileContract.State()
    }
    
    override suspend fun handleIntent(intent: ProfileContract.Intent) {
        when (intent) {
            // 需求4.1：设置项点击处理逻辑
            is ProfileContract.Intent.OnStepGoalClick -> {
                setState { copy(showStepGoalDialog = true) }
            }
            is ProfileContract.Intent.OnSensitivityClick -> {
                setState { copy(showSensitivityDialog = true) }
            }
            is ProfileContract.Intent.OnGenderClick -> {
                setState { copy(showGenderDialog = true) }
            }
            is ProfileContract.Intent.OnWeightClick -> {
                setState { copy(showWeightDialog = true) }
            }
            is ProfileContract.Intent.OnHeightClick -> {
                setState { copy(showHeightDialog = true) }
            }
            is ProfileContract.Intent.OnStepLengthClick -> {
                setState { copy(showStepLengthDialog = true) }
            }
            is ProfileContract.Intent.OnUnitSystemClick -> {
                setState { copy(showUnitSystemDialog = true) }
            }
            is ProfileContract.Intent.OnDismissDialog -> {
                setState { 
                    copy(
                        showStepGoalDialog = false,
                        showSensitivityDialog = false,
                        showGenderDialog = false,
                        showWeightDialog = false,
                        showHeightDialog = false,
                        showStepLengthDialog = false,
                        showUnitSystemDialog = false
                    )
                }
            }
            // 设置更新事件 - 任务16：实现设置数据的保存和同步
            is ProfileContract.Intent.OnUpdateStepGoal -> {
                updateStepGoal(intent.goal)
            }
            is ProfileContract.Intent.OnUpdateSensitivity -> {
                updateSensitivity(intent.level)
            }
            is ProfileContract.Intent.OnUpdateGender -> {
                updateGender(intent.gender)
            }
            is ProfileContract.Intent.OnUpdateWeight -> {
                updateWeight(intent.weight)
            }
            is ProfileContract.Intent.OnUpdateHeight -> {
                updateHeight(intent.height)
            }
            is ProfileContract.Intent.OnUpdateStepLength -> {
                updateStepLength(intent.length)
            }
            is ProfileContract.Intent.OnUpdateUnitSystem -> {
                updateUnitSystem(intent.system)
            }
        }
    }
    
    init {
        loadUserSettings()
    }
    
    /**
     * 加载用户设置 - 需求4.1：显示个人设置选项列表
     */
    private fun loadUserSettings() {
        viewModelScope.launch {
            setState { copy(isLoading = true) }
            
            settingsRepository.getUserSettings()
                .catch { throwable ->
                    setState { copy(isLoading = false) }
                    setEffect { ProfileContract.Effect.ShowError("Failed to load settings: ${throwable.message}") }
                }
                .collect { settings ->
                    setState { 
                        copy(
                            userSettings = settings,
                            isLoading = false
                        )
                    }
                }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.2：步数目标设置，默认值为6000步
     */
    private fun updateStepGoal(goal: Int) {
        viewModelScope.launch {
            try {
                settingsRepository.updateStepGoal(goal)
                setState { copy(showStepGoalDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update step goal: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.4：灵敏度设置，默认为低
     */
    private fun updateSensitivity(level: com.example.step0724.data.model.SensitivityLevel) {
        viewModelScope.launch {
            try {
                settingsRepository.updateSensitivity(level)
                setState { copy(showSensitivityDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update sensitivity: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.5：性别设置，默认为Others
     */
    private fun updateGender(gender: com.example.step0724.data.model.Gender) {
        viewModelScope.launch {
            try {
                settingsRepository.updateGender(gender)
                setState { copy(showGenderDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update gender: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.6：体重设置，默认值为57.0kg（125.7 lbs）
     * 需求9.3：处理单位切换时的数据转换
     */
    private fun updateWeight(weight: Double) {
        viewModelScope.launch {
            try {
                settingsRepository.updateWeight(weight)
                setState { copy(showWeightDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update weight: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.7：身高设置，默认值为172cm（5 ft 8 in）
     * 需求9.3：处理单位切换时的数据转换
     */
    private fun updateHeight(height: Double) {
        viewModelScope.launch {
            try {
                settingsRepository.updateHeight(height)
                // 需求4.9：当身高改变时，自动更新默认步长
                val newStepLength = calculateDefaultStepLengthUseCase.calculate(height)
                settingsRepository.updateStepLength(newStepLength)
                setState { copy(showHeightDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update height: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.8：步长设置，默认值为71cm（2 ft 4 in）
     * 需求9.3：处理单位切换时的数据转换
     */
    private fun updateStepLength(length: Double) {
        viewModelScope.launch {
            try {
                settingsRepository.updateStepLength(length)
                setState { copy(showStepLengthDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update step length: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：实现设置数据的保存和同步
     * 需求4.10：单位制式设置，默认为公制单位
     * 需求9.3：处理单位切换时的数据转换
     */
    private fun updateUnitSystem(system: com.example.step0724.data.model.UnitSystem) {
        viewModelScope.launch {
            try {
                // 需求9.3：单位切换时需要转换现有数据
                val currentSettings = state.value.userSettings
                if (currentSettings.unitSystem != system) {
                    // 转换体重、身高、步长数据
                    val convertedWeight = convertWeightForUnitSystem(currentSettings.weight, currentSettings.unitSystem, system)
                    val convertedHeight = convertHeightForUnitSystem(currentSettings.height, currentSettings.unitSystem, system)
                    val convertedStepLength = convertStepLengthForUnitSystem(currentSettings.stepLength, currentSettings.unitSystem, system)
                    
                    // 批量更新所有相关设置
                    settingsRepository.updateUnitSystem(system)
                    settingsRepository.updateWeight(convertedWeight)
                    settingsRepository.updateHeight(convertedHeight)
                    settingsRepository.updateStepLength(convertedStepLength)
                }
                setState { copy(showUnitSystemDialog = false) }
                setEffect { ProfileContract.Effect.SettingsSaved }
            } catch (e: Exception) {
                setEffect { ProfileContract.Effect.ShowError("Failed to update unit system: ${e.message}") }
            }
        }
    }
    
    /**
     * 任务16：需求9.3 - 处理单位切换时的体重数据转换
     */
    private fun convertWeightForUnitSystem(
        weight: Double, 
        fromSystem: com.example.step0724.data.model.UnitSystem, 
        toSystem: com.example.step0724.data.model.UnitSystem
    ): Double {
        return when {
            fromSystem == toSystem -> weight
            fromSystem == com.example.step0724.data.model.UnitSystem.METRIC && toSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL -> 
                unitConversionUseCase.kgToLbs(weight)
            fromSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL && toSystem == com.example.step0724.data.model.UnitSystem.METRIC -> 
                unitConversionUseCase.lbsToKg(weight)
            else -> weight
        }
    }
    
    /**
     * 任务16：需求9.3 - 处理单位切换时的身高数据转换
     */
    private fun convertHeightForUnitSystem(
        height: Double, 
        fromSystem: com.example.step0724.data.model.UnitSystem, 
        toSystem: com.example.step0724.data.model.UnitSystem
    ): Double {
        return when {
            fromSystem == toSystem -> height
            fromSystem == com.example.step0724.data.model.UnitSystem.METRIC && toSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL -> {
                val (feet, inches) = unitConversionUseCase.cmToFeetInches(height)
                unitConversionUseCase.feetInchesToCm(feet, inches) // 保持cm存储，只是显示转换
            }
            fromSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL && toSystem == com.example.step0724.data.model.UnitSystem.METRIC -> {
                height // 已经是cm存储
            }
            else -> height
        }
    }
    
    /**
     * 任务16：需求9.3 - 处理单位切换时的步长数据转换
     */
    private fun convertStepLengthForUnitSystem(
        stepLength: Double, 
        fromSystem: com.example.step0724.data.model.UnitSystem, 
        toSystem: com.example.step0724.data.model.UnitSystem
    ): Double {
        return when {
            fromSystem == toSystem -> stepLength
            fromSystem == com.example.step0724.data.model.UnitSystem.METRIC && toSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL -> {
                val (feet, inches) = unitConversionUseCase.cmToFeetInches(stepLength)
                unitConversionUseCase.feetInchesToCm(feet, inches) // 保持cm存储，只是显示转换
            }
            fromSystem == com.example.step0724.data.model.UnitSystem.IMPERIAL && toSystem == com.example.step0724.data.model.UnitSystem.METRIC -> {
                stepLength // 已经是cm存储
            }
            else -> stepLength
        }
    }
}