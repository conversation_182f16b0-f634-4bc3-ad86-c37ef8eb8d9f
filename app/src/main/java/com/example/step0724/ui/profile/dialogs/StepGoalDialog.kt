package com.example.step0724.ui.profile.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.step0724.R

/**
 * 步数目标设置弹窗 - 基于需求4.2和需求4.3
 * 需求4.2：弹出步数目标设置弹窗，默认值为6000步
 * 需求4.3：支持手动输入或点击+/-按钮调整，每次增减100步
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StepGoalDialog(
    currentGoal: Int,
    onConfirm: (Int) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var goalValue by remember { mutableIntStateOf(currentGoal) }
    var goalText by remember { mutableStateOf(currentGoal.toString()) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "Step Goal",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 24.dp)
                )
                
                // 步数调整区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 减少按钮 - 需求4.3：每次增减100步
                    IconButton(
                        onClick = {
                            val newGoal = maxOf(100, goalValue - 100) // 最小值100步
                            goalValue = newGoal
                            goalText = newGoal.toString()
                        },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF4FC3F7))
                    ) {
                        Text(
                            text = "-",
                            color = Color.White,
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    // 步数输入框 - 需求4.3：支持手动输入
                    OutlinedTextField(
                        value = goalText,
                        onValueChange = { newText ->
                            goalText = newText
                            // 验证输入并更新goalValue
                            newText.toIntOrNull()?.let { newGoal ->
                                if (newGoal >= 100 && newGoal <= 50000) { // 合理范围
                                    goalValue = newGoal
                                }
                            }
                        },
                        modifier = Modifier.width(120.dp),
                        textStyle = LocalTextStyle.current.copy(
                            textAlign = TextAlign.Center,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        ),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF4FC3F7),
                            unfocusedBorderColor = Color.Gray
                        )
                    )
                    
                    // 增加按钮 - 需求4.3：每次增减100步
                    IconButton(
                        onClick = {
                            val newGoal = minOf(50000, goalValue + 100) // 最大值50000步
                            goalValue = newGoal
                            goalText = newGoal.toString()
                        },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF4FC3F7))
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Increase",
                            tint = Color.White
                        )
                    }
                }
                
                // 单位标签
                Text(
                    text = "steps",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 8.dp, bottom = 24.dp)
                )
                
                // 按钮区域 - 需求5.4：保存、取消逻辑
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮 - 需求5.5：取消设置时不保存更改
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.light_gray)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                    
                    // 确认按钮 - 需求5.4：完成设置后保存设置并关闭弹窗
                    Button(
                        onClick = { onConfirm(goalValue) },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(R.color.primary_blue)
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Save",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}