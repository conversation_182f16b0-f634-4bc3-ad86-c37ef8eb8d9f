package com.example.step0724.ui.permissions.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.step0724.R
import com.example.step0724.ui.theme.LightGray
import com.example.step0724.ui.theme.PrimaryBlue
import com.example.step0724.ui.theme.TextPrimary
import com.example.step0724.ui.theme.TextSecondary

enum class PermissionType {
    NOTIFICATION,
    PHYSICAL_ACTIVITY,
    BATTERY_OPTIMIZATION
}

@Composable
fun PermissionExplanationDialog(
    permissionType: PermissionType,
    onAllow: () -> Unit,
    onCancel: () -> Unit
) {
    Dialog(onDismissRequest = {}) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 图标

                Image(
                    painter = painterResource(
                        id = when (permissionType) {
                            PermissionType.NOTIFICATION -> R.mipmap.img_notify
                            PermissionType.PHYSICAL_ACTIVITY -> R.mipmap.img_phys
                            PermissionType.BATTERY_OPTIMIZATION -> R.mipmap.img_opt
                        }
                    ),
                    contentDescription = null,
                    modifier = Modifier.size(86.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 标题
                Text(
                    text = when (permissionType) {
                        PermissionType.NOTIFICATION -> "Notification"
                        PermissionType.PHYSICAL_ACTIVITY -> "Physical Activity"
                        PermissionType.BATTERY_OPTIMIZATION -> "Battery Optimization"
                    },
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 说明文字
                Text(
                    text = when (permissionType) {
                        PermissionType.NOTIFICATION -> "Stay informed about step rewards and time-limited bonuses."
                        PermissionType.PHYSICAL_ACTIVITY -> "Track your steps accurately to get the right rewards."
                        PermissionType.BATTERY_OPTIMIZATION -> "Turn it off to keep step tracking uninterrupted."
                    },
                    fontSize = 14.sp,
                    color = TextPrimary,
                    textAlign = TextAlign.Center,
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Cancel按钮
                    Button(
                        onClick = onCancel,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = LightGray
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Cancel",
                            color = TextSecondary,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Allow按钮
                    Button(
                        onClick = onAllow,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = PrimaryBlue
                        ),
                        shape = RoundedCornerShape(22.dp)
                    ) {
                        Text(
                            text = "Allow",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}