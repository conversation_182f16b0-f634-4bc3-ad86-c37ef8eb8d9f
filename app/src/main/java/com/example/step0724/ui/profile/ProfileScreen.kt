package com.example.step0724.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.step0724.R
import com.example.step0724.data.model.*

import com.example.step0724.ui.profile.dialogs.StepGoalDialog
import com.example.step0724.ui.profile.dialogs.SensitivityDialog
import com.example.step0724.ui.profile.dialogs.GenderDialog
import com.example.step0724.ui.profile.dialogs.WeightDialog
import com.example.step0724.ui.profile.dialogs.HeightDialog
import com.example.step0724.ui.profile.dialogs.StepLengthDialog
import com.example.step0724.ui.profile.dialogs.UnitSystemDialog

/**
 * Profile页面 - 基于需求4.1的个人设置选项列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    modifier: Modifier = Modifier,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.state.collectAsState()
    
    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is ProfileContract.Effect.ShowError -> {
                    // TODO: 显示错误提示，后续任务会实现
                }
                is ProfileContract.Effect.SettingsSaved -> {
                    // TODO: 显示保存成功提示，后续任务会实现
                }
            }
        }
    }
    
    if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 需求4.1：个人设置选项列表 - 网格布局
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                contentPadding = PaddingValues(16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(getSettingsItems(uiState.userSettings)) { item ->
                    ProfileSettingCard(
                        title = item.title,
                        value = item.value,
                        iconRes = item.iconRes,
                        backgroundColor = item.backgroundColor,
                        onClick = {
                            viewModel.sendIntent(item.intent)
                        }
                    )
                }
            }
        }
    
    // 需求5.1：设置弹窗显示逻辑
    // Step Goal Dialog - 需求4.2：步数目标设置弹窗
    if (uiState.showStepGoalDialog) {
        StepGoalDialog(
            currentGoal = uiState.userSettings.stepGoal,
            onConfirm = { newGoal ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateStepGoal(newGoal))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Sensitivity Dialog - 需求4.4：灵敏度设置弹窗
    if (uiState.showSensitivityDialog) {
        SensitivityDialog(
            currentSensitivity = uiState.userSettings.sensitivity,
            onConfirm = { newSensitivity ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateSensitivity(newSensitivity))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Gender Dialog - 需求4.5：性别选择弹窗
    if (uiState.showGenderDialog) {
        GenderDialog(
            currentGender = uiState.userSettings.gender,
            onConfirm = { newGender ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateGender(newGender))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Weight Dialog - 需求4.6：体重设置弹窗
    if (uiState.showWeightDialog) {
        WeightDialog(
            currentWeight = uiState.userSettings.weight,
            unitSystem = uiState.userSettings.unitSystem,
            unitConversionUseCase = viewModel.unitConversionUseCase,
            onConfirm = { newWeight ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateWeight(newWeight))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Height Dialog - 需求4.7：身高设置弹窗
    if (uiState.showHeightDialog) {
        HeightDialog(
            currentHeight = uiState.userSettings.height,
            unitSystem = uiState.userSettings.unitSystem,
            unitConversionUseCase = viewModel.unitConversionUseCase,
            onConfirm = { newHeight ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateHeight(newHeight))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Step Length Dialog - 需求4.8：步长设置弹窗
    if (uiState.showStepLengthDialog) {
        StepLengthDialog(
            currentStepLength = uiState.userSettings.stepLength,
            unitSystem = uiState.userSettings.unitSystem,
            unitConversionUseCase = viewModel.unitConversionUseCase,
            onConfirm = { newStepLength ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateStepLength(newStepLength))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
    
    // Unit System Dialog - 需求4.10：单位制式选择弹窗
    if (uiState.showUnitSystemDialog) {
        UnitSystemDialog(
            currentUnitSystem = uiState.userSettings.unitSystem,
            onConfirm = { newUnitSystem ->
                viewModel.sendIntent(ProfileContract.Intent.OnUpdateUnitSystem(newUnitSystem))
            },
            onDismiss = {
                viewModel.sendIntent(ProfileContract.Intent.OnDismissDialog)
            }
        )
    }
}

/**
 * 设置项数据类
 */
private data class SettingItemData(
    val title: String,
    val value: String,
    val iconRes: Int,
    val backgroundColor: Color,
    val intent: ProfileContract.Intent
)

/**
 * 获取设置项列表 - 基于需求4的所有设置项，按照UI设计排序
 * 任务16：添加Step Goal设置项
 */
private fun getSettingsItems(settings: UserSettings): List<SettingItemData> {
    return listOf(
        SettingItemData(
            title = "Sensitivity",
            value = getSensitivityDisplayName(settings.sensitivity),
            iconRes = R.mipmap.pro_sens,
            backgroundColor = Color(0xFF81C784), // 绿色
            intent = ProfileContract.Intent.OnSensitivityClick
        ),
        SettingItemData(
            title = "Weight",
            value = getWeightDisplayValue(settings.weight, settings.unitSystem),
            iconRes = R.mipmap.pro_weight,
            backgroundColor = Color(0xFFFF8A65), // 橙色
            intent = ProfileContract.Intent.OnWeightClick
        ),
        SettingItemData(
            title = "Height",
            value = getHeightDisplayValue(settings.height, settings.unitSystem),
            iconRes = R.mipmap.pro_height,
            backgroundColor = Color(0xFF9575CD), // 紫色
            intent = ProfileContract.Intent.OnHeightClick
        ),
        SettingItemData(
            title = "Gender",
            value = getGenderDisplayName(settings.gender),
            iconRes = R.mipmap.pro_gender,
            backgroundColor = Color(0xFF64B5F6), // 蓝色
            intent = ProfileContract.Intent.OnGenderClick
        ),
        SettingItemData(
            title = "Step length",
            value = getStepLengthDisplayValue(settings.stepLength, settings.unitSystem),
            iconRes = R.mipmap.pro_step,
            backgroundColor = Color(0xFFBA68C8), // 紫色
            intent = ProfileContract.Intent.OnStepLengthClick
        ),
        // 第三行：Unit
        SettingItemData(
            title = "Unit",
            value = getUnitSystemDisplayName(settings.unitSystem),
            iconRes = R.mipmap.pro_unit,
            backgroundColor = Color(0xFFFFB74D), // 黄色
            intent = ProfileContract.Intent.OnUnitSystemClick
        )
    )
}

/**
 * Profile设置卡片组件 - 基于需求4.1的设置项点击处理逻辑
 */
@Composable
private fun ProfileSettingCard(
    title: String,
    value: String,
    iconRes: Int,
    backgroundColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 130.dp)
            .clickable { onClick() }
            .background(color = Color.White, shape = RoundedCornerShape(16.dp))
            .padding(vertical = 12.dp, horizontal = 10.dp)
    ) {
        // 标题 + 值：左上角
        Column(
            modifier = Modifier
                .align(Alignment.TopStart)

        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                lineHeight = 17.sp,
                color = colorResource(R.color.text_secondary),
                fontWeight = FontWeight.Normal
            )
            Text(
                text = value,
                fontSize = 14.sp,
                lineHeight = 17.sp,
                fontWeight = FontWeight.Bold,
                color = colorResource(R.color.text_primary),
            )
        }

        // 图标：右下角
        Image(
            painter = painterResource(id = iconRes),
            contentDescription = title,
            modifier = Modifier
                .size(40.dp)
                .align(Alignment.BottomEnd)  // 与文字保持相同内边距
        )
    }
}

/**
 * 获取灵敏度显示名称 - 基于需求4.4的四个等级
 */
private fun getSensitivityDisplayName(sensitivity: SensitivityLevel): String {
    return when (sensitivity) {
        SensitivityLevel.LOW -> "Low"
        SensitivityLevel.MEDIUM_LOW -> "Medium Low"
        SensitivityLevel.MEDIUM_HIGH -> "Medium High"
        SensitivityLevel.HIGH -> "High"
    }
}

/**
 * 获取性别显示名称 - 基于需求4.5的三个选项
 */
private fun getGenderDisplayName(gender: Gender): String {
    return when (gender) {
        Gender.MALE -> "Male"
        Gender.FEMALE -> "Female"
        Gender.OTHERS -> "Others"
    }
}

/**
 * 获取体重显示值 - 基于需求4.6和需求9的单位制式
 */
private fun getWeightDisplayValue(weight: Double, unitSystem: UnitSystem): String {
    return when (unitSystem) {
        UnitSystem.METRIC -> "${String.format("%.1f", weight)} kg"
        UnitSystem.IMPERIAL -> {
            val lbs = weight / 0.45359237 // kg转lbs
            "${String.format("%.1f", lbs)} lbs"
        }
    }
}

/**
 * 获取身高显示值 - 基于需求4.7和需求9的单位制式
 */
private fun getHeightDisplayValue(height: Double, unitSystem: UnitSystem): String {
    return when (unitSystem) {
        UnitSystem.METRIC -> "${height.toInt()} cm"
        UnitSystem.IMPERIAL -> {
            val totalInches = height / 2.54
            val feet = (totalInches / 12).toInt()
            val inches = (totalInches % 12).toInt()
            "$feet ft $inches in"
        }
    }
}

/**
 * 获取步长显示值 - 基于需求4.8和需求9的单位制式
 */
private fun getStepLengthDisplayValue(stepLength: Double, unitSystem: UnitSystem): String {
    return when (unitSystem) {
        UnitSystem.METRIC -> "${stepLength.toInt()} cm"
        UnitSystem.IMPERIAL -> {
            val totalInches = stepLength / 2.54
            val feet = (totalInches / 12).toInt()
            val inches = (totalInches % 12).toInt()
            "$feet ft $inches in"
        }
    }
}

/**
 * 获取单位制式显示名称 - 基于需求4.10和需求9
 */
private fun getUnitSystemDisplayName(unitSystem: UnitSystem): String {
    return when (unitSystem) {
        UnitSystem.METRIC -> "kg / cm"
        UnitSystem.IMPERIAL -> "lbs / ft"
    }
}