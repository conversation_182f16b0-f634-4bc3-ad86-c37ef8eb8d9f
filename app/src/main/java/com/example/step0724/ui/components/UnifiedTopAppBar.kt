package com.example.step0724.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.step0724.R
import com.example.step0724.data.model.DataType
import com.example.step0724.ui.theme.Orange500

/**
 * 获取数据类型的显示名称
 */
private fun getDataTypeDisplayName(dataType: DataType): String {
    return when (dataType) {
        DataType.STEPS -> "Steps"
        DataType.DISTANCE -> "Distance"
        DataType.CALORIES -> "Calories"
        DataType.TIME -> "Time"
    }
}

@Composable
fun UnifiedTopAppBar(
    title: String? = null,
    showBackButton: Boolean = false,
    showSettingsButton: Boolean = false,
    showCoins: Boolean = false,
    coins: Int = 0,
    showEarnLayout: Boolean = false,
    earnCoins: Int = 0,
    showDataTypeSelector: Boolean = false,
    selectedDataType: DataType? = null,
    isDataTypeSelectorExpanded: Boolean = false,
    onBackClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    onWithdrawClick: () -> Unit = {},
    onDataTypeSelectorToggle: () -> Unit = {},
    onDataTypeSelected: (DataType) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(if (showEarnLayout) 150.dp else 90.dp)
            .clip(RoundedCornerShape(bottomStart = 24.dp, bottomEnd = 24.dp))
            .paint(
                painter = painterResource(id = if (showEarnLayout) R.drawable.top_bg_l else R.drawable.top_bg_s),
                contentScale = ContentScale.Crop,
            )
            .statusBarsPadding()
            .padding(horizontal = 16.dp)
    ) {
        if (showEarnLayout) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.Center),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = title ?: "Earn",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(R.color.text_primary)
                    )
                    
                    Image(
                        painter = painterResource(id = R.mipmap.icon_set),
                        contentDescription = "Settings",
                        modifier = Modifier
                            .size(28.dp)
                            .clickable(onClick = onSettingsClick)
                    )
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    Color(0xFFFFF5E6),
                                    Color(0xFFFFF8E0)
                                )
                            ),
                            shape = RoundedCornerShape(24.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(id = R.mipmap.icon_coin),
                            contentDescription = "Coins",
                            modifier = Modifier.size(24.dp)
                        )
                        Text(
                            text = "$earnCoins",
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold,
                            color = colorResource(R.color.primary_origin),
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                        Text(
                            text = "= $ NA",
                            fontSize = 14.sp,
                            color = colorResource(R.color.text_primary)
                        )
                    }
                    
                    Box(
                        modifier = Modifier
                            .background(
                                color = colorResource(R.color.primary_origin),
                                shape = RoundedCornerShape(20.dp)
                            )
                            .clickable(onClick = onWithdrawClick)
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                    ) {
                        Text(
                            text = "Withdraw",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                }
            }
        } else {
            Row(
                modifier = Modifier.fillMaxWidth()
                    .align(Alignment.Center),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
            if (showCoins) {
                Row(
                    modifier = Modifier
                        .widthIn(min = 80.dp)
                        .background(brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFFFFF5E6),
                                Color(0xFFFFF8E0)
                            )
                        ),
                            shape = RoundedCornerShape(18.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.icon_coin),
                        contentDescription = "Coins",
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = coins.toString(),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(R.color.primary_origin),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            } else if (title != null) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (showBackButton) {
                        Icon(
                            painter = painterResource(id = R.mipmap.nav_back),
                            contentDescription = "Back",
                            tint = colorResource(R.color.text_primary),
                            modifier = Modifier.size(30.dp)
                                .clickable(onClick = onBackClick)
                                .padding(end = 4.dp)
                        )
                    }

                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(R.color.text_primary)
                    )
                }
            }


            // 右侧内容
            if (showSettingsButton) {
                Image(
                    painter = painterResource(id = R.mipmap.icon_set),
                    contentDescription = "Settings",
                    modifier = Modifier
                        .size(28.dp)
                        .clickable(onClick = onSettingsClick)
                )
            } else if (showDataTypeSelector && selectedDataType != null) {
            Box {
                Row(
                    modifier = Modifier
                        .widthIn(min = 100.dp)
                        .background(color = Color.White, shape = RoundedCornerShape(22.dp))
                        .padding(vertical = 8.dp, horizontal = 16.dp)
                        .clickable { onDataTypeSelectorToggle() },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = getDataTypeDisplayName(selectedDataType),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = colorResource(R.color.text_primary)
                    )
                    Image(
                        painter = painterResource(id =if (isDataTypeSelectorExpanded) R.mipmap.arrow_up else R.mipmap.arrow_down),
                        contentDescription = "Expand",
                        modifier = Modifier.size(20.dp)
                            .padding(start = 4.dp)
                    )
                }

                DropdownMenu(
                    expanded = isDataTypeSelectorExpanded,
                    onDismissRequest = onDataTypeSelectorToggle,
                    modifier = Modifier.background(Color.White)
                ) {
                    DataType.values().forEach { dataType ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = getDataTypeDisplayName(dataType),
                                    color = if (dataType == selectedDataType) colorResource(R.color.primary_blue) else colorResource(R.color.text_primary)
                                )
                            },
                            onClick = {
                                onDataTypeSelected(dataType)
                            }
                        )
                }
            }
            }
        }
        }
    }
}
}