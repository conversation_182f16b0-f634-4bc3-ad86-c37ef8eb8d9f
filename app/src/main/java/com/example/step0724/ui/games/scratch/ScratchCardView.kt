package com.example.step0724.ui.games.scratch

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.example.step0724.R
import com.example.step0724.data.model.ScratchCard
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * 刮刮卡组件 - 使用基于dp的精确面积计算
 */
@Composable
fun ScratchCardView(
    card: ScratchCard,
    onScratch: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    var scratchPoints by remember { mutableStateOf(listOf<Offset>()) }
    var scratchedArea by remember { mutableStateOf(0f) }
    var isRevealed by remember { mutableStateOf(false) }
    
    val density = LocalDensity.current
    val cardSizeDp = 100.dp
    val cardSizePx = with(density) { cardSizeDp.toPx() }
    val scratchRadiusDp = 12.dp // 使用dp定义刮开半径
    val scratchRadiusPx = with(density) { scratchRadiusDp.toPx() }
    
    // 当刮开面积超过60%时，自动揭开整张卡片
    LaunchedEffect(scratchedArea) {
        if (scratchedArea >= 0.6f && !isRevealed) {
            isRevealed = true
            onScratch(1f) // 通知完全刮开
        }
    }
    
    // 计算刮开面积的函数 - 基于dp的精确计算
    fun calculateScratchedArea(): Float {
        if (scratchPoints.isEmpty()) return 0f
        
        // 使用基于dp的网格采样，确保不同分辨率下的一致性
        val gridSizeDp = 2 // 每2dp一个采样点
        val gridCountPerSide = (100 / gridSizeDp) // 100dp / 2dp = 50个网格点每边
        val totalGridPoints = gridCountPerSide * gridCountPerSide // 50 * 50 = 2500个采样点
        
        var scratchedGridPoints = 0
        val gridStepPx = cardSizePx / gridCountPerSide
        
        // 检查每个网格点是否被刮开
        for (i in 0 until gridCountPerSide) {
            for (j in 0 until gridCountPerSide) {
                val gridX = gridStepPx * (i + 0.5f)
                val gridY = gridStepPx * (j + 0.5f)
                
                // 检查这个网格点是否在任何刮开区域内
                val isScratched = scratchPoints.any { point ->
                    val distance = sqrt((gridX - point.x).pow(2) + (gridY - point.y).pow(2))
                    distance <= scratchRadiusPx
                }
                
                if (isScratched) {
                    scratchedGridPoints++
                }
            }
        }
        
        return scratchedGridPoints.toFloat() / totalGridPoints.toFloat()
    }
    
    Box(
        modifier = modifier
            .size(cardSizeDp)
            .pointerInput(card.id) {
                if (!card.isScratched && !isRevealed) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            scratchPoints = scratchPoints + offset
                            scratchedArea = calculateScratchedArea()
                            onScratch(scratchedArea)
                        },
                        onDrag = { change, _ ->
                            scratchPoints = scratchPoints + change.position
                            scratchedArea = calculateScratchedArea()
                            onScratch(scratchedArea)
                        }
                    )
                }
            }
    ) {
        // 底层：刮刮卡底层背景 + 水果图案
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(12.dp)),
            contentAlignment = Alignment.Center
        ) {
            // 底层背景
            Image(
                painter = painterResource(id = R.mipmap.sc_card_in),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            
            // 水果图案
            Image(
                painter = painterResource(id = card.fruitType.resourceId),
                contentDescription = null,
                modifier = Modifier.size(60.dp),
                contentScale = ContentScale.Fit
            )
        }
        
        // 顶层：刮刮卡图层 - 只有在未完全刮开时显示
        if (!card.isScratched && !isRevealed) {
            // 获取刮刮卡表面的ImageBitmap
            val scratchBitmap = ImageBitmap.imageResource(R.mipmap.sc_card_b)
            
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(12.dp))
            ) {
                // 绘制完整的刮刮卡表面
                drawImage(
                    image = scratchBitmap,
                    dstSize = IntSize(size.width.toInt(), size.height.toInt())
                )
                
                // 在刮开的位置绘制透明圆圈来"挖洞"
                scratchPoints.forEach { point ->
                    if (point.x >= 0 && point.x <= size.width && 
                        point.y >= 0 && point.y <= size.height) {
                        drawCircle(
                            color = Color.Transparent,
                            radius = scratchRadiusPx,
                            center = point,
                            blendMode = BlendMode.Clear
                        )
                    }
                }
            }
        }
    }
}