package com.example.step0724.ui.report

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.step0724.data.model.DataType
import com.example.step0724.data.model.TimeRange
import com.example.step0724.ui.components.LineChart
import com.example.step0724.R

/**
 * Report页面 - 基于需求3的数据报告页面，按照UI设计图实现
 * 需求3.2：时间维度选择器(Day/Week/Month)
 * 需求3.9：数据类型下拉菜单(steps/distance/calories/time)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportScreen(
    modifier: Modifier = Modifier,
    viewModel: ReportViewModel? = null
) {
    val actualViewModel = viewModel ?: hiltViewModel<ReportViewModel>()
    val uiState by actualViewModel.state.collectAsState()
    
    // 内容区域 - 数据类型选择器现在在TopAppBar中
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
            // Day 卡片
            item {
                TimeRangeCard(
                    title = "Day",
                    currentDate = uiState.dayData.currentDate,
                    totalValue = uiState.dayData.totalValue,
                    avgValue = uiState.dayData.averageValue,
                    chartData = uiState.dayData.chartData,
                    dataType = uiState.selectedDataType,
                    timeRange = TimeRange.DAY,
                    isLoading = uiState.dayData.isLoading,
                    onPreviousClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigatePrevious(TimeRange.DAY)) },
                    onNextClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigateNext(TimeRange.DAY)) }
                )
            }
            
            // Week 卡片
            item {
                TimeRangeCard(
                    title = "Week",
                    currentDate = uiState.weekData.currentDate,
                    totalValue = uiState.weekData.totalValue,
                    avgValue = uiState.weekData.averageValue,
                    chartData = uiState.weekData.chartData,
                    dataType = uiState.selectedDataType,
                    timeRange = TimeRange.WEEK,
                    isLoading = uiState.weekData.isLoading,
                    onPreviousClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigatePrevious(TimeRange.WEEK)) },
                    onNextClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigateNext(TimeRange.WEEK)) }
                )
            }
            
            // Month 卡片
            item {
                TimeRangeCard(
                    title = "Month",
                    currentDate = uiState.monthData.currentDate,
                    totalValue = uiState.monthData.totalValue,
                    avgValue = uiState.monthData.averageValue,
                    chartData = uiState.monthData.chartData,
                    dataType = uiState.selectedDataType,
                    timeRange = TimeRange.MONTH,
                    isLoading = uiState.monthData.isLoading,
                    onPreviousClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigatePrevious(TimeRange.MONTH)) },
                    onNextClick = { actualViewModel.sendIntent(ReportContract.Intent.NavigateNext(TimeRange.MONTH)) }
                )
            }
        }
    }

/**
 * 时间范围卡片组件 - 每个时间维度的独立卡片
 */
@Composable
private fun TimeRangeCard(
    title: String,
    currentDate: String,
    totalValue: Double,
    avgValue: Double,
    chartData: List<com.example.step0724.data.model.ChartPoint>,
    dataType: DataType,
    timeRange: TimeRange,
    isLoading: Boolean,
    onPreviousClick: () -> Unit,
    onNextClick: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxWidth()
            .background(color = Color(0xFFE0ECFB), shape = RoundedCornerShape(16.dp)),
    ) {
        Column{
            // 标题和日期导航
            Row(
                modifier = Modifier.fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.text_primary)
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier
                        .widthIn(min = 110.dp)
                        .background(color = Color.White, shape = RoundedCornerShape(12.dp))
                        .padding(4.dp)
                ) {
                    Image(painter = painterResource(R.mipmap.arrow_left),
                        contentDescription = "Previous",
                        modifier = Modifier.size(20.dp)
                            .clickable { onPreviousClick() }
                    )
                    Text(
                        text = currentDate,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.text_primary),
                    )
                    Image(painter = painterResource(R.mipmap.arrow_right),
                        contentDescription = "Next",
                        modifier = Modifier.size(20.dp)
                            .clickable { onNextClick() }
                    )
                }
            }

            Column(modifier = Modifier.fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(16.dp))
                .padding(12.dp)
            ) {
                // 需求3.7：在Week或Month维度时，右侧显示总值和平均值
                if (timeRange == TimeRange.WEEK || timeRange == TimeRange.MONTH) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Text(
                            text = "Total: ${formatValue(totalValue, dataType)}",
                            fontSize = 10.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                        Text(
                            text = "Avg: ${formatValue(avgValue, dataType)}",
                            fontSize = 10.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                } else {
                    // Day维度显示当日总计
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Text(
                            text = "Total: ${formatValue(totalValue, dataType)}",
                            fontSize = 10.sp,
                            color = colorResource(R.color.text_secondary)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                if (isLoading) {
                    Column {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(150.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(color = Color(0xFF2196F3))
                        }

                        Spacer(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(24.dp)
                        )
                    }
                } else {
                    LineChart(
                        chartData = chartData,
                        dataType = dataType,
                        timeRange = timeRange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(150.dp)
                    )
                }
            }
        }
    }
}





// 辅助函数
private fun getDataTypeDisplayName(dataType: DataType): String {
    return when (dataType) {
        DataType.STEPS -> "步数"
        DataType.DISTANCE -> "距离"
        DataType.CALORIES -> "卡路里"
        DataType.TIME -> "时间"
    }
}

private fun getTimeRangeDisplayName(timeRange: TimeRange): String {
    return when (timeRange) {
        TimeRange.DAY -> "日"
        TimeRange.WEEK -> "周"
        TimeRange.MONTH -> "月"
    }
}

private fun getAverageLabel(timeRange: TimeRange): String {
    return when (timeRange) {
        TimeRange.DAY -> "平均/小时"
        TimeRange.WEEK -> "平均/天"
        TimeRange.MONTH -> "平均/天"
    }
}

private fun formatValue(value: Double, dataType: DataType): String {
    return when (dataType) {
        DataType.STEPS -> "${value.toInt()}"
        DataType.DISTANCE -> String.format("%.2f km", value)
        DataType.CALORIES -> "${value.toInt()} cal"
        DataType.TIME -> "${value.toInt()} min"
    }
}