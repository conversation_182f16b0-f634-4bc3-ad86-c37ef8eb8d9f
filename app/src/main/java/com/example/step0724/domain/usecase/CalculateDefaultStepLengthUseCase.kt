package com.example.step0724.domain.usecase

import javax.inject.Inject
import javax.inject.Singleton

/**
 * 计算默认步长用例 - 基于需求4.9的步长计算
 * 需求4.9：步长(cm) = 身高(cm) × 0.415
 */
@Singleton
class CalculateDefaultStepLengthUseCase @Inject constructor() {
    
    /**
     * 根据身高计算默认步长
     * 需求4.9：步长(cm) = 身高(cm) × 0.415
     * @param heightCm 身高（厘米）
     * @return 步长（厘米）
     */
    fun calculate(heightCm: Double): Double {
        return heightCm * 0.415
    }
}