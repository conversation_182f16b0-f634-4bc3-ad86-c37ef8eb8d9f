package com.example.step0724.domain.usecase

import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UnitConversionUseCase @Inject constructor() {

    // 需求9：1英尺(ft) = 12英寸(in) = 30.48000cm
    fun cmToFeetInches(cm: Double): Pair<Int, Int> {
        val totalInches = cm / 2.54
        val feet = (totalInches / 12).toInt()
        val inches = (totalInches % 12).toInt()
        return Pair(feet, inches)
    }

    fun feetInchesToCm(feet: Int, inches: Int): Double {
        return (feet * 12 + inches) * 2.54
    }

    // 需求9：1英镑(lbs) = 0.45359237kg
    fun kgToLbs(kg: Double): Double = kg / 0.45359237
    fun lbsToKg(lbs: Double): Double = lbs * 0.45359237

    // 需求9：1km = 0.62137119英里(mi)
    fun kmToMiles(km: Double): Double = km * 0.62137119
    fun milesToKm(miles: Double): Double = miles / 0.62137119
}