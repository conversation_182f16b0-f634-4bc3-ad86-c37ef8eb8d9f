package com.example.step0724.domain.usecase

import com.example.step0724.data.model.UnitSystem
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 步数统计计算用例 - 基于需求2.3的计算公式
 * 计算距离、卡路里、时间等统计信息
 */
@Singleton
class CalculateStatsUseCase @Inject constructor(
    private val unitConversionUseCase: UnitConversionUseCase
) {

    /**
     * 计算走路距离
     * 需求2.3：走路距离(km) = 步长(cm) × 步数 ÷ 100 ÷ 1000
     * @param steps 步数
     * @param stepLengthCm 步长（厘米）
     * @param unitSystem 单位制式
     * @return 距离（km或mi，根据单位制式）
     */
    fun calculateDistance(steps: Int, stepLengthCm: Double, unitSystem: UnitSystem): Double {
        val distanceKm = stepLengthCm * steps / 100.0 / 1000.0
        
        return if (unitSystem == UnitSystem.IMPERIAL) {
            unitConversionUseCase.kmToMiles(distanceKm)
        } else {
            distanceKm
        }
    }

    /**
     * 计算消耗卡路里
     * 需求2.3：消耗卡路里 = 走路时间(minutes) × 3.0 × 3.5 × 体重(kg) ÷ 200
     * @param steps 步数
     * @param weightKg 体重（公斤）
     * @return 消耗的卡路里
     */
    fun calculateCalories(steps: Int, weightKg: Double): Double {
        val walkingTimeMinutes = calculateWalkingTimeMinutes(steps)
        return walkingTimeMinutes * 3.0 * 3.5 * weightKg / 200.0
    }

    /**
     * 计算走路时间（秒）
     * 需求2.3：走路时间(seconds) = 步数 × 0.6
     * @param steps 步数
     * @return 走路时间（秒）
     */
    fun calculateWalkingTimeSeconds(steps: Int): Long {
        return (steps * 0.6).toLong()
    }

    /**
     * 计算走路时间（分钟）
     * @param steps 步数
     * @return 走路时间（分钟）
     */
    private fun calculateWalkingTimeMinutes(steps: Int): Double {
        return (steps * 0.6) / 60.0
    }

    /**
     * 格式化走路时间为小时和分钟
     * @param steps 步数
     * @return Pair<小时, 分钟>
     */
    fun formatWalkingTime(steps: Int): Pair<Int, Int> {
        val totalSeconds = calculateWalkingTimeSeconds(steps)
        val hours = (totalSeconds / 3600).toInt()
        val minutes = ((totalSeconds % 3600) / 60).toInt()
        return Pair(hours, minutes)
    }
}