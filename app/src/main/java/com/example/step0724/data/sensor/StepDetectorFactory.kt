package com.example.step0724.data.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import com.example.step0724.data.storage.StepCounterStateManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 步数检测器工厂 - 需求8.1：自动选择可用的传感器
 */
@Singleton
class StepDetectorFactory @Inject constructor(
    @ApplicationContext private val context: Context,
    private val stateManager: StepCounterStateManager
) {
    
    /**
     * 创建步数检测器
     * 需求8.1：优先使用Step Counter传感器，不可用时使用加速度传感器
     */
    fun createDetector(): StepDetector {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        
        return if (hasStepCounterSensor(sensorManager)) {
            StepCounterSensorDetector(context, stateManager)
        } else {
            AccelerometerStepDetector(context)
        }
    }
    
    /**
     * 检查设备是否支持Step Counter传感器
     */
    private fun hasStepCounterSensor(sensorManager: SensorManager): Bo<PERSON>an {
        return sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER) != null
    }
    
    /**
     * 获取当前使用的传感器类型描述
     */
    fun getSensorType(): String {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        return if (hasStepCounterSensor(sensorManager)) {
            "Step Counter Sensor"
        } else {
            "Accelerometer Sensor"
        }
    }
    
    /**
     * 检查设备是否支持加速度传感器
     */
    fun hasAccelerometer(): Boolean {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        return sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER) != null
    }
    
    /**
     * 获取传感器支持信息
     */
    fun getSensorSupportInfo(): SensorSupportInfo {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        return SensorSupportInfo(
            hasStepCounter = hasStepCounterSensor(sensorManager),
            hasAccelerometer = hasAccelerometer(),
            preferredSensorType = getSensorType()
        )
    }
}

/**
 * 传感器支持信息
 */
data class SensorSupportInfo(
    val hasStepCounter: Boolean,
    val hasAccelerometer: Boolean,
    val preferredSensorType: String
)