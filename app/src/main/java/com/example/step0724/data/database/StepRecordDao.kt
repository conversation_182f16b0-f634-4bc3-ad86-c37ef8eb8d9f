package com.example.step0724.data.database

import androidx.room.*
import com.example.step0724.data.model.StepRecord
import kotlinx.coroutines.flow.Flow

/**
 * 步数记录数据访问对象 - 基于需求7的Room数据库存储
 */
@Dao
interface StepRecordDao {
    
    /**
     * 获取指定日期的步数记录
     */
    @Query("SELECT * FROM step_records WHERE date = :date")
    suspend fun getStepRecord(date: String): StepRecord?
    
    /**
     * 获取今日步数的实时流 - 需求2：获取当前日期的实时步数
     */
    @Query("SELECT steps FROM step_records WHERE date = :date")
    fun getTodayStepsFlow(date: String): Flow<Int?>
    
    /**
     * 插入或更新步数记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateStepRecord(record: StepRecord)
    
    /**
     * 获取指定日期范围的步数记录 - 需求3：支持按天、周、月的数据查询
     */
    @Query("SELECT * FROM step_records WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    suspend fun getStepRecordsInRange(startDate: String, endDate: String): List<StepRecord>
    
    /**
     * 获取指定年月的所有记录 - 需求3：按月查询
     */
    @Query("SELECT * FROM step_records WHERE date LIKE :yearMonth || '%' ORDER BY date ASC")
    suspend fun getMonthlyRecords(yearMonth: String): List<StepRecord>
    
    /**
     * 删除指定日期之前的旧记录（数据清理）
     */
    @Query("DELETE FROM step_records WHERE date < :cutoffDate")
    suspend fun deleteOldRecords(cutoffDate: String)
    
    /**
     * 获取所有记录数量
     */
    @Query("SELECT COUNT(*) FROM step_records")
    suspend fun getRecordCount(): Int
}