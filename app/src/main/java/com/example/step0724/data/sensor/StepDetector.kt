package com.example.step0724.data.sensor

import com.example.step0724.data.model.SensitivityLevel
import kotlinx.coroutines.flow.Flow

/**
 * 步数检测器接口 - 基于需求8的计步核心功能
 */
interface StepDetector {
    /**
     * 开始步数检测
     */
    fun startDetection()
    
    /**
     * 停止步数检测
     */
    fun stopDetection()
    
    /**
     * 获取实时步数流
     */
    fun getStepCount(): Flow<Int>
    
    /**
     * 检查传感器是否支持
     */
    fun isSupported(): Boolean
    
    /**
     * 设置灵敏度等级 - 需求8.3：灵敏度影响步数检测
     */
    fun setSensitivity(level: SensitivityLevel)
    
    /**
     * 重置步数计数
     */
    fun resetStepCount()
    
    /**
     * 检查是否正在检测
     */
    fun isDetecting(): Boolean
}