package com.example.step0724.data.storage

import com.example.step0724.data.model.*
import com.tencent.mmkv.MMKV
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MMKV存储管理器 - 基于需求7的用户设置存储
 */
@Singleton
class MMKVManager @Inject constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    /**
     * 获取用户设置 - 需求4：用户设置的获取
     */
    fun getUserSettings(): UserSettings {
        return UserSettings(
            stepGoal = mmkv.decodeInt(SettingsKeys.STEP_GOAL, 6000),
            sensitivity = SensitivityLevel.valueOf(
                mmkv.decodeString(SettingsKeys.SENSITIVITY, SensitivityLevel.LOW.name) ?: SensitivityLevel.LOW.name
            ),
            gender = Gender.valueOf(
                mmkv.decodeString(SettingsKeys.GENDER, Gender.OTHERS.name) ?: Gender.OTHERS.name
            ),
            weight = mmkv.decodeDouble(SettingsKeys.WEIGHT, 57.0),
            height = mmkv.decodeDouble(SettingsKeys.HEIGHT, 172.0),
            stepLength = mmkv.decodeDouble(SettingsKeys.STEP_LENGTH, 71.0),
            unitSystem = UnitSystem.valueOf(
                mmkv.decodeString(SettingsKeys.UNIT_SYSTEM, UnitSystem.METRIC.name) ?: UnitSystem.METRIC.name
            )
        )
    }
    
    /**
     * 更新步数目标 - 需求4：步数目标设置
     */
    fun updateStepGoal(goal: Int) {
        mmkv.encode(SettingsKeys.STEP_GOAL, goal)
    }
    
    /**
     * 更新灵敏度 - 需求4：灵敏度设置
     */
    fun updateSensitivity(level: SensitivityLevel) {
        mmkv.encode(SettingsKeys.SENSITIVITY, level.name)
    }
    
    /**
     * 更新性别 - 需求4：性别设置
     */
    fun updateGender(gender: Gender) {
        mmkv.encode(SettingsKeys.GENDER, gender.name)
    }
    
    /**
     * 更新体重 - 需求4：体重设置
     */
    fun updateWeight(weight: Double) {
        mmkv.encode(SettingsKeys.WEIGHT, weight)
    }
    
    /**
     * 更新身高 - 需求4：身高设置
     */
    fun updateHeight(height: Double) {
        mmkv.encode(SettingsKeys.HEIGHT, height)
    }
    
    /**
     * 更新步长 - 需求4：步长设置
     */
    fun updateStepLength(length: Double) {
        mmkv.encode(SettingsKeys.STEP_LENGTH, length)
    }
    
    /**
     * 更新单位制式 - 需求4：单位制式设置
     */
    fun updateUnitSystem(system: UnitSystem) {
        mmkv.encode(SettingsKeys.UNIT_SYSTEM, system.name)
    }
    
    /**
     * 获取上次传感器值 - 需求7：传感器状态保存
     */
    fun getLastSensorValue(): Long {
        return mmkv.decodeLong(SettingsKeys.LAST_SENSOR_VALUE, 0L)
    }
    
    /**
     * 保存传感器值 - 需求7：传感器状态保存
     */
    fun saveLastSensorValue(value: Long) {
        mmkv.encode(SettingsKeys.LAST_SENSOR_VALUE, value)
    }
}