package com.example.step0724.data.model

import androidx.annotation.DrawableRes

/**
 * 游戏入口数据模型
 * 描述More games区域中每个游戏的显示信息和状态
 * 
 * 基于需求9.2定义游戏入口的数据结构
 */
data class GameEntry(
    /**
     * 游戏类型
     */
    val type: GameType,
    
    /**
     * 游戏显示标题
     */
    val title: String,
    
    /**
     * 游戏图标资源ID
     */
    @DrawableRes
    val iconRes: Int,
    
    /**
     * 游戏是否可用
     * 可用于控制游戏的开启/关闭状态
     */
    val isAvailable: Boolean = true,
    
    /**
     * 游戏描述（可选）
     * 用于显示游戏的简短说明
     */
    val description: String = ""
)