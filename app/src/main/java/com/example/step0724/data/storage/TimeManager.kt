package com.example.step0724.data.storage

import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TimeManager - 时间管理工具类
 * 
 * 负责处理Earn功能中的时间相关逻辑，包括：
 * - 获取当前日期字符串
 * - 计算到午夜的倒计时
 * - 判断日期是否变更
 * 
 * Requirements: 8.1, 8.2, 8.3, 8.4, 8.5
 */
@Singleton
class TimeManager @Inject constructor() {
    
    companion object {
        private const val DATE_FORMAT = "yyyy-MM-dd"
        private const val TIME_FORMAT = "%02d:%02d"
    }
    
    private val dateFormatter = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
    
    /**
     * 获取当前日期字符串
     * 
     * Requirements: 8.1 - 系统应使用"HH:MM"格式显示剩余时间
     * 
     * @return 格式为"yyyy-MM-dd"的当前日期字符串
     */
    fun getCurrentDateString(): String {
        return dateFormatter.format(Date())
    }
    
    /**
     * 计算到午夜的倒计时时间
     * 
     * Requirements: 8.2 - 系统应每分钟更新一次倒计时显示
     * Requirements: 8.4 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     * 
     * @return 格式为"HH:MM"的倒计时字符串
     */
    fun getTimeUntilMidnight(): String {
        val now = Calendar.getInstance()
        val midnight = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        
        val diffInMillis = midnight.timeInMillis - now.timeInMillis
        
        // 确保差值为正数，防止时间异常
        if (diffInMillis <= 0) {
            return "00:00"
        }
        
        val hours = diffInMillis / (1000 * 60 * 60)
        val minutes = (diffInMillis % (1000 * 60 * 60)) / (1000 * 60)
        
        return String.format(TIME_FORMAT, hours, minutes)
    }
    
    /**
     * 判断是否是新的一天
     * 
     * Requirements: 8.3 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     * 
     * @param lastDate 上次记录的日期字符串，格式为"yyyy-MM-dd"
     * @return true表示是新的一天，false表示是同一天
     */
    fun isNewDay(lastDate: String): Boolean {
        val currentDate = getCurrentDateString()
        return currentDate != lastDate
    }
    
    /**
     * 获取到午夜的毫秒数
     * 
     * 内部方法，用于更精确的时间计算
     * 
     * @return 到午夜的毫秒数
     */
    fun getMillisUntilMidnight(): Long {
        val now = Calendar.getInstance()
        val midnight = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        
        val diffInMillis = midnight.timeInMillis - now.timeInMillis
        return if (diffInMillis > 0) diffInMillis else 0L
    }
    
    /**
     * 检查是否已过午夜（用于任务重置检查）
     * 
     * @return true表示已过午夜，需要重置任务
     */
    fun isMidnightPassed(): Boolean {
        return getMillisUntilMidnight() == 0L
    }
}