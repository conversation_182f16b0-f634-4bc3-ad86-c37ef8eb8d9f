package com.example.step0724.data.service

import kotlinx.coroutines.flow.Flow

import com.example.step0724.data.model.SensitivityLevel

/**
 * 步数统计服务接口 - 定义计步服务的核心功能
 */
interface StepCountingService {
    
    /**
     * 开始计步
     */
    fun startCounting()
    
    /**
     * 停止计步
     */
    fun stopCounting()
    
    /**
     * 获取当前步数流
     */
    fun getCurrentSteps(): Flow<Int>
    
    /**
     * 重置每日步数
     */
    fun resetDailySteps()
    
    /**
     * 检查服务是否正在运行
     */
    fun isRunning(): Boolean
    
    /**
     * 更新灵敏度设置 - 需求8.3：灵敏度影响步数检测
     */
    fun updateSensitivity(level: SensitivityLevel)
}