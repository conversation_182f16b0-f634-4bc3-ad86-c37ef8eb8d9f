package com.example.step0724.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 步数记录实体 - 存储每日步数数据
 * 基于需求7的数据持久化要求
 */
@Entity(tableName = "step_records")
data class StepRecord(
    @PrimaryKey val date: String, // yyyy-MM-dd格式
    val steps: Int,
    val distance: Double, // 根据需求2的公式计算：步长×步数÷100÷1000
    val calories: Double, // 根据需求2的公式计算：走路时间×3.0×3.5×体重÷200
    val walkingTime: Long, // 根据需求2的公式计算：步数×0.6秒
    val hourlyData: String // JSON格式存储0-24时累计数据（需求3）
)