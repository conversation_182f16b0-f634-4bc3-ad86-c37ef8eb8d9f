package com.example.step0724.data.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import com.example.step0724.data.model.SensitivityLevel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * 加速度传感器步数检测器 - 需求8.1：备用方案，当Step Counter不可用时使用
 */
@Singleton
class AccelerometerStepDetector @Inject constructor(
    private val context: Context
) : StepDetector, SensorEventListener {

    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    
    private val _stepCount = MutableStateFlow(0)
    private var stepCount = 0
    private var isDetecting = false
    
    // 需求8.3：灵敏度直接影响步数检测的敏感程度
    private var sensitivity = SensitivityLevel.LOW
    private var threshold = 12.0f // 默认检测阈值
    private var minStepInterval = 600L // 默认最小步数间隔
    
    // 步数检测参数
    private var lastAcceleration = 0f
    private var currentAcceleration = SensorManager.GRAVITY_EARTH
    private var lastUpdate = 0L
    private var lastStepTime = 0L
    
    // 需求8.8：防抖动算法相关变量
    private val accelerationHistory = mutableListOf<Float>()
    private val historySize = 5
    private var peakCount = 0
    private var valleyCount = 0

    override fun startDetection() {
        if (accelerometer != null && !isDetecting) {
            sensorManager.registerListener(
                this,
                accelerometer,
                SensorManager.SENSOR_DELAY_FASTEST
            )
            isDetecting = true
        }
    }

    override fun stopDetection() {
        if (isDetecting) {
            sensorManager.unregisterListener(this)
            isDetecting = false
        }
    }

    override fun getStepCount(): Flow<Int> = _stepCount.asStateFlow()

    override fun isSupported(): Boolean = accelerometer != null
    
    override fun isDetecting(): Boolean = isDetecting

    override fun setSensitivity(level: SensitivityLevel) {
        sensitivity = level
        updateSensitivityParameters()
    }

    override fun resetStepCount() {
        stepCount = 0
        _stepCount.value = 0
        accelerationHistory.clear()
        peakCount = 0
        valleyCount = 0
    }

    /**
     * 需求8.3：根据灵敏度等级更新检测参数
     */
    private fun updateSensitivityParameters() {
        when (sensitivity) {
            SensitivityLevel.LOW -> {
                threshold = 15.0f      // 需要较大的移动幅度才计入步数
                minStepInterval = 800L
            }
            SensitivityLevel.MEDIUM_LOW -> {
                threshold = 12.0f      // 适中偏低的移动幅度计入步数
                minStepInterval = 600L
            }
            SensitivityLevel.MEDIUM_HIGH -> {
                threshold = 9.0f       // 适中偏高的移动幅度计入步数
                minStepInterval = 400L
            }
            SensitivityLevel.HIGH -> {
                threshold = 6.0f       // 较小的移动幅度即可计入步数
                minStepInterval = 300L
            }
        }
    }

    override fun onSensorChanged(event: SensorEvent?) {
        event?.let {
            if (it.sensor.type == Sensor.TYPE_ACCELEROMETER) {
                val currentTime = System.currentTimeMillis()
                
                if (currentTime - lastUpdate > 50) { // 每50ms更新一次
                    val x = it.values[0]
                    val y = it.values[1]
                    val z = it.values[2]
                    
                    lastAcceleration = currentAcceleration
                    currentAcceleration = sqrt(x * x + y * y + z * z)
                    
                    // 需求8.8：防抖动算法，避免非步行动作被误计为步数
                    if (isValidStep(currentAcceleration, currentTime)) {
                        stepCount++
                        _stepCount.value = stepCount
                        lastStepTime = currentTime
                    }
                    
                    lastUpdate = currentTime
                }
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 不需要处理精度变化
    }

    /**
     * 需求8.8：防抖动算法，避免非步行动作被误计为步数
     */
    private fun isValidStep(acceleration: Float, currentTime: Long): Boolean {
        // 检查时间间隔
        if (currentTime - lastStepTime < minStepInterval) {
            return false
        }
        
        // 检查加速度变化幅度
        val delta = abs(acceleration - lastAcceleration)
        if (delta < threshold) {
            return false
        }
        
        // 维护加速度历史记录
        accelerationHistory.add(acceleration)
        if (accelerationHistory.size > historySize) {
            accelerationHistory.removeAt(0)
        }
        
        // 需要足够的历史数据才能判断
        if (accelerationHistory.size < historySize) {
            return false
        }
        
        // 检查是否为有效的步行模式（波峰波谷交替）
        return isWalkingPattern(accelerationHistory)
    }

    /**
     * 检查加速度变化是否符合步行模式
     */
    private fun isWalkingPattern(history: List<Float>): Boolean {
        if (history.size < 3) return false
        
        var peaks = 0
        var valleys = 0
        
        for (i in 1 until history.size - 1) {
            val prev = history[i - 1]
            val curr = history[i]
            val next = history[i + 1]
            
            // 检测波峰
            if (curr > prev && curr > next && curr > SensorManager.GRAVITY_EARTH + 2) {
                peaks++
            }
            // 检测波谷
            if (curr < prev && curr < next && curr < SensorManager.GRAVITY_EARTH - 2) {
                valleys++
            }
        }
        
        // 步行模式应该有交替的波峰波谷
        return peaks > 0 && valleys > 0 && abs(peaks - valleys) <= 1
    }
}