package com.example.step0724.data.service

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 步数统计服务管理器 - 管理前台服务的生命周期和权限
 */
@Singleton
class StepCountingServiceManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var isServiceRunning = false

    /**
     * 启动步数统计服务
     * 需求8.4：通过前台服务继续进行步数统计
     */
    fun startService(): Boolean {
        return try {
            if (!isServiceRunning && hasRequiredPermissions()) {
                StepCountingForegroundService.startService(context)
                isServiceRunning = true
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 停止步数统计服务
     */
    fun stopService() {
        if (isServiceRunning) {
            StepCountingForegroundService.stopService(context)
            isServiceRunning = false
        }
    }

    /**
     * 检查服务是否正在运行
     */
    fun isRunning(): Boolean = isServiceRunning

    /**
     * 检查是否有必要的权限
     */
    private fun hasRequiredPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 10以下不需要ACTIVITY_RECOGNITION权限
        }
    }

    /**
     * 获取所需权限列表
     */
    fun getRequiredPermissions(): List<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            listOf(android.Manifest.permission.ACTIVITY_RECOGNITION)
        } else {
            emptyList()
        }
    }

    /**
     * 重启服务（用于设备重启后恢复）
     * 需求8.7：设备重启时能够从上次保存的步数继续累计
     */
    fun restartServiceIfNeeded() {
        if (hasRequiredPermissions()) {
            startService()
        }
    }
}