package com.example.step0724.data.model

/**
 * Earn页面数据模型
 * 聚合Earn页面所需的所有数据
 */
data class EarnData(
    /**
     * 当前金币余额
     */
    val coins: Int,
    
    /**
     * 每日任务列表
     */
    val dailyTasks: List<Task>,
    
    /**
     * 其他任务列表
     */
    val otherTasks: List<Task>,
    
    /**
     * 距离下次重置的倒计时显示（格式：HH:MM）
     */
    val timeUntilReset: String,
    
    /**
     * 连续签到天数
     */
    val consecutiveCheckIns: Int
)