package com.example.step0724.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.example.step0724.data.model.StepRecord

/**
 * 计步器数据库 - 基于需求7的Room数据库存储
 */
@Database(
    entities = [StepRecord::class],
    version = 1,
    exportSchema = false
)
abstract class StepCounterDatabase : RoomDatabase() {
    
    abstract fun stepRecordDao(): StepRecordDao
    
    companion object {
        const val DATABASE_NAME = "step_counter_database"
    }
}