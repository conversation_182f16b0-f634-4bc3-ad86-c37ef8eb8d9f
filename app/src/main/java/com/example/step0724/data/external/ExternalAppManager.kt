package com.example.step0724.data.external

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 管理外部应用集成的工具类
 * 需求6：菜单功能 - 处理外部应用不可用的异常情况
 */
@Singleton
class ExternalAppManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    /**
     * 需求6.4：打开邮件应用
     * - 自动填入开发者邮箱地址
     * - 设置邮件标题为"Contact us"
     * - 设置邮件内容引导文案为"Please share your thoughts with us."
     */
    fun openEmailApp(email: String, subject: String, body: String) {
        try {
            val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:")
                putExtra(Intent.EXTRA_EMAIL, arrayOf(email))
                putExtra(Intent.EXTRA_SUBJECT, subject)
                putExtra(Intent.EXTRA_TEXT, body)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(emailIntent)
        } catch (e: Exception) {
            // 静默处理异常，直接跳转
        }
    }
    
    /**
     * 需求6.5：打开系统默认浏览器
     * - 跳转到隐私协议的网页链接
     */
    fun openBrowser(url: String) {
        try {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url)).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(browserIntent)
        } catch (e: Exception) {
            // 静默处理异常，直接跳转
        }
    }
    
    /**
     * 检查Intent是否有可用的应用处理
     */
    private fun isIntentAvailable(intent: Intent): Boolean {
        val packageManager = context.packageManager
        return packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY).isNotEmpty()
    }
}

/**
 * 外部应用调用结果
 */
sealed class ExternalAppResult {
    object Success : ExternalAppResult()
    data class AppNotAvailable(val message: String) : ExternalAppResult()
    data class Error(val message: String) : ExternalAppResult()
}