package com.example.step0724.data.storage

/**
 * MMKV存储键值常量 - 基于需求7的用户设置存储
 */
object SettingsKeys {
    // 需求4：Profile页面的所有设置项
    const val STEP_GOAL = "step_goal"           // 步数目标，默认6000
    const val SENSITIVITY = "sensitivity"       // 灵敏度，默认低
    const val GENDER = "gender"                 // 性别，默认Others
    const val WEIGHT = "weight"                 // 体重，默认57.0kg
    const val HEIGHT = "height"                 // 身高，默认172cm
    const val STEP_LENGTH = "step_length"       // 步长，默认71cm
    const val UNIT_SYSTEM = "unit_system"       // 单位制式，默认公制
    
    // 需求7：传感器状态保存
    const val LAST_SENSOR_VALUE = "last_sensor_value" // 上次Step Counter传感器值
}