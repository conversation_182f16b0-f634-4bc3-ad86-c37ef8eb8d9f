package com.example.step0724.data.model

/**
 * 用户设置实体 - 基于需求4的Profile页面设置项
 */
data class UserSettings(
    val stepGoal: Int = 6000, // 需求4：默认6000步
    val sensitivity: SensitivityLevel = SensitivityLevel.LOW, // 需求4：默认低灵敏度
    val gender: Gender = Gender.OTHERS, // 需求4：默认Others
    val weight: Double = 57.0, // 需求4：默认57.0kg (125.7 lbs)
    val height: Double = 172.0, // 需求4：默认172cm (5 ft 8 in)
    val stepLength: Double = 71.0, // 需求4：默认71cm (2 ft 4 in)
    val unitSystem: UnitSystem = UnitSystem.METRIC // 需求4：默认公制单位
)