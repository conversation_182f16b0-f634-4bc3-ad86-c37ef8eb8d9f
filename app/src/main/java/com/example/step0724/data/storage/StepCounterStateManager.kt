package com.example.step0724.data.storage

import com.tencent.mmkv.MMKV
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 步数计数器状态管理器 - 需求7.6：存储传感器状态用于设备重启后的步数恢复
 */
@Singleton
class StepCounterStateManager @Inject constructor() {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        private const val KEY_LAST_STEP_COUNTER_VALUE = "last_step_counter_value"
        private const val KEY_LAST_SAVE_TIME = "last_save_time"
        private const val KEY_TODAY_BASE_STEPS = "today_base_steps"
        private const val KEY_SERVICE_RUNNING = "service_running"
    }
    
    /**
     * 保存Step Counter传感器的最后值
     * 需求8.7：用于设备重启后的步数恢复
     */
    fun saveLastStepCounterValue(value: Long) {
        mmkv.encode(KEY_LAST_STEP_COUNTER_VALUE, value)
        mmkv.encode(KEY_LAST_SAVE_TIME, System.currentTimeMillis())
    }
    
    /**
     * 获取Step Counter传感器的最后值
     */
    fun getLastStepCounterValue(): Long {
        return mmkv.decodeLong(KEY_LAST_STEP_COUNTER_VALUE, 0L)
    }
    
    /**
     * 保存今日基准步数
     */
    fun saveTodayBaseSteps(steps: Int) {
        mmkv.encode(KEY_TODAY_BASE_STEPS, steps)
    }
    
    /**
     * 获取今日基准步数
     */
    fun getTodayBaseSteps(): Int {
        return mmkv.decodeInt(KEY_TODAY_BASE_STEPS, 0)
    }
    
    /**
     * 保存服务运行状态
     */
    fun setServiceRunning(running: Boolean) {
        mmkv.encode(KEY_SERVICE_RUNNING, running)
    }
    
    /**
     * 获取服务运行状态
     */
    fun isServiceRunning(): Boolean {
        return mmkv.decodeBool(KEY_SERVICE_RUNNING, false)
    }
    
    /**
     * 获取最后保存时间
     */
    fun getLastSaveTime(): Long {
        return mmkv.decodeLong(KEY_LAST_SAVE_TIME, 0L)
    }
    
    /**
     * 清除状态数据（用于重置）
     */
    fun clearState() {
        mmkv.remove(KEY_LAST_STEP_COUNTER_VALUE)
        mmkv.remove(KEY_LAST_SAVE_TIME)
        mmkv.remove(KEY_TODAY_BASE_STEPS)
        mmkv.remove(KEY_SERVICE_RUNNING)
    }
    
    /**
     * 检查是否是新的一天（用于重置日步数）
     */
    fun isNewDay(): Boolean {
        val lastSaveTime = getLastSaveTime()
        if (lastSaveTime == 0L) return true
        
        val currentTime = System.currentTimeMillis()
        val lastDay = lastSaveTime / (24 * 60 * 60 * 1000)
        val currentDay = currentTime / (24 * 60 * 60 * 1000)
        
        return currentDay > lastDay
    }
}