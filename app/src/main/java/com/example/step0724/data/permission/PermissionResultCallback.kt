package com.example.step0724.data.permission

/**
 * 权限结果回调接口
 * 用于在权限授予/拒绝后立即通知UI更新
 */
interface PermissionResultCallback {
    fun onPermissionResult(permission: String, granted: Boolean)
}

/**
 * 权限结果管理器
 * 管理权限结果回调的注册和通知
 */
object PermissionResultManager {
    private var callback: PermissionResultCallback? = null
    
    fun setCallback(callback: PermissionResultCallback?) {
        this.callback = callback
    }
    
    fun notifyPermissionResult(permission: String, granted: Boolean) {
        callback?.onPermissionResult(permission, granted)
    }
}