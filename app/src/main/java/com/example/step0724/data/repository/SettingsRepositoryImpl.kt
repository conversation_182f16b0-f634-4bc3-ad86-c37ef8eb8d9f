package com.example.step0724.data.repository

import com.example.step0724.data.model.Gender
import com.example.step0724.data.model.SensitivityLevel
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.data.model.UserSettings
import com.example.step0724.data.storage.SettingsStorage
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsRepositoryImpl @Inject constructor(
    private val settingsStorage: SettingsStorage
) : SettingsRepository {

    override suspend fun getUserSettings(): Flow<UserSettings> {
        return settingsStorage.getUserSettings()
    }

    override suspend fun updateStepGoal(goal: Int) {
        settingsStorage.updateStepGoal(goal)
    }

    override suspend fun updateSensitivity(level: SensitivityLevel) {
        settingsStorage.updateSensitivity(level)
    }

    override suspend fun updateGender(gender: Gender) {
        settingsStorage.updateGender(gender)
    }

    override suspend fun updateWeight(weight: Double) {
        settingsStorage.updateWeight(weight)
    }

    override suspend fun updateHeight(height: Double) {
        settingsStorage.updateHeight(height)
    }

    override suspend fun updateStepLength(length: Double) {
        settingsStorage.updateStepLength(length)
    }

    override suspend fun updateUnitSystem(system: UnitSystem) {
        settingsStorage.updateUnitSystem(system)
    }

    override suspend fun getLastSensorValue(): Long {
        return settingsStorage.getLastSensorValue()
    }

    override suspend fun saveLastSensorValue(value: Long) {
        settingsStorage.saveLastSensorValue(value)
    }
}