package com.example.step0724.data.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import com.example.step0724.data.model.SensitivityLevel
import com.example.step0724.data.storage.StepCounterStateManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Step Counter传感器检测器 - 需求8.1：优先使用Step Counter传感器
 */
@Singleton
class StepCounterSensorDetector @Inject constructor(
    private val context: Context,
    private val stateManager: StepCounterStateManager
) : StepDetector, SensorEventListener {

    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val stepCounterSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER)
    
    private val _stepCount = MutableStateFlow(0)
    private var baseStepCount = 0L
    private var lastSensorValue = 0L
    private var isDetecting = false
    
    // 需求8.3：灵敏度过滤相关变量
    private var sensitivity = SensitivityLevel.LOW
    private var lastStepTime = 0L
    private var stepBuffer = mutableListOf<Long>()
    private val bufferSize = 10

    override fun startDetection() {
        if (stepCounterSensor != null && !isDetecting) {
            // 需求8.7：设备重启后恢复步数状态
            if (stateManager.isNewDay()) {
                // 新的一天，重置状态
                stateManager.clearState()
                baseStepCount = 0L
                _stepCount.value = 0
            } else {
                // 恢复之前的状态
                val savedBaseSteps = stateManager.getTodayBaseSteps()
                if (savedBaseSteps > 0) {
                    _stepCount.value = savedBaseSteps
                }
            }
            
            sensorManager.registerListener(
                this,
                stepCounterSensor,
                SensorManager.SENSOR_DELAY_UI
            )
            isDetecting = true
        }
    }

    override fun stopDetection() {
        if (isDetecting) {
            sensorManager.unregisterListener(this)
            isDetecting = false
        }
    }

    override fun getStepCount(): Flow<Int> = _stepCount.asStateFlow()

    override fun isSupported(): Boolean = stepCounterSensor != null
    
    override fun isDetecting(): Boolean = isDetecting

    override fun setSensitivity(level: SensitivityLevel) {
        sensitivity = level
    }

    override fun resetStepCount() {
        baseStepCount = lastSensorValue
        _stepCount.value = 0
        stepBuffer.clear()
    }

    override fun onSensorChanged(event: SensorEvent?) {
        event?.let {
            if (it.sensor.type == Sensor.TYPE_STEP_COUNTER) {
                val currentSensorValue = it.values[0].toLong()
                
                if (baseStepCount == 0L) {
                    // 初始化基准值
                    val lastSavedValue = stateManager.getLastStepCounterValue()
                    baseStepCount = if (lastSavedValue > 0L && currentSensorValue >= lastSavedValue) {
                        // 设备重启后，传感器值可能重置，使用保存的值作为基准
                        currentSensorValue - (lastSavedValue - currentSensorValue)
                    } else {
                        currentSensorValue
                    }
                } else {
                    val rawSteps = (currentSensorValue - baseStepCount).toInt()
                    val filteredSteps = applySensitivityFilter(rawSteps)
                    val finalSteps = maxOf(0, filteredSteps)
                    _stepCount.value = finalSteps
                    
                    // 保存状态用于设备重启恢复
                    stateManager.saveLastStepCounterValue(currentSensorValue)
                    stateManager.saveTodayBaseSteps(finalSteps)
                }
                lastSensorValue = currentSensorValue
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // 不需要处理精度变化
    }

    /**
     * 需求8.3：Step Counter传感器的灵敏度后处理过滤
     * 由于传感器本身不支持灵敏度调整，需要对传感器返回的步数进行后处理过滤
     */
    private fun applySensitivityFilter(rawSteps: Int): Int {
        val currentTime = System.currentTimeMillis()
        
        // 根据灵敏度设置最小步数间隔
        val minInterval = when (sensitivity) {
            SensitivityLevel.LOW -> 800L      // 低灵敏度：需要较大的移动幅度
            SensitivityLevel.MEDIUM_LOW -> 600L   // 中度偏低：适中偏低的移动幅度
            SensitivityLevel.MEDIUM_HIGH -> 400L  // 中度偏高：适中偏高的移动幅度
            SensitivityLevel.HIGH -> 200L     // 高灵敏度：较小的移动幅度
        }
        
        // 使用时间间隔过滤步数变化
        if (currentTime - lastStepTime < minInterval) {
            return _stepCount.value // 返回之前的步数，不更新
        }
        
        // 使用滑动窗口平滑步数变化
        stepBuffer.add(currentTime)
        if (stepBuffer.size > bufferSize) {
            stepBuffer.removeAt(0)
        }
        
        // 根据灵敏度决定是否接受步数变化
        val stepChangeThreshold = when (sensitivity) {
            SensitivityLevel.LOW -> 3        // 需要连续3次变化才接受
            SensitivityLevel.MEDIUM_LOW -> 2     // 需要连续2次变化才接受
            SensitivityLevel.MEDIUM_HIGH -> 1    // 需要1次变化就接受
            SensitivityLevel.HIGH -> 1       // 立即接受变化
        }
        
        if (rawSteps > _stepCount.value && stepBuffer.size >= stepChangeThreshold) {
            lastStepTime = currentTime
            return rawSteps
        }
        
        return _stepCount.value
    }
}