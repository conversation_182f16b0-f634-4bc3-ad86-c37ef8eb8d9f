package com.example.step0724.data.storage

import com.example.step0724.data.model.TaskStatus
import com.example.step0724.data.model.TaskType
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * EarnStorage - Earn功能数据存储层
 * 
 * 使用MMKV存储Earn功能相关的所有数据，包括：
 * - 金币余额管理
 * - 签到功能数据持久化
 * - 任务状态存储和查询
 * - 转盘游戏次数统计
 * 
 * Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7
 */
@Singleton
class EarnStorage @Inject constructor(
    private val timeManager: TimeManager
) {
    
    private val mmkv = MMKV.defaultMMKV()
    
    companion object {
        // 金币相关键值
        private const val KEY_COINS = "earn_coins"
        
        // 签到相关键值
        private const val KEY_LAST_CHECK_IN_DATE = "last_check_in_date"
        private const val KEY_CONSECUTIVE_CHECK_INS = "consecutive_check_ins"
        
        // 任务状态相关键值（按日期存储）
        private const val KEY_DAILY_TASKS_PREFIX = "daily_tasks_"
        private const val KEY_OTHER_TASKS_PREFIX = "other_tasks_"
        
        // 转盘游戏相关键值
        private const val KEY_LUCKY_WHEEL_COUNT_PREFIX = "lucky_wheel_count_"
        
        // 其他游戏相关键值
        private const val KEY_EGG_SMASH_COUNT_PREFIX = "egg_smash_count_"
        private const val KEY_LUCKY_SCRATCH_COUNT_PREFIX = "lucky_scratch_count_"
        
        // 奖励配置常量
        const val REWARD_DAILY_CHECK_IN = 10
        const val REWARD_STEPS_1000 = 5
        const val REWARD_STEPS_3000 = 15
        const val REWARD_CHECK_IN_2_DAYS = 20
        const val REWARD_CHECK_IN_3_DAYS = 50
        const val REWARD_LUCKY_WHEEL_DAILY = 30
        
        // 转盘游戏每日限制次数
        const val LUCKY_WHEEL_DAILY_LIMIT = 10
        
        // 其他游戏每日限制次数
        const val EGG_SMASH_DAILY_LIMIT = 5
        const val LUCKY_SCRATCH_DAILY_LIMIT = 3
    }
    
    // ==================== 金币相关操作 ====================
    
    /**
     * 获取当前金币余额
     * 
     * Requirements: 10.1 - 当用户获得金币时，系统应立即保存金币余额到本地存储
     * 
     * @return 当前金币数量
     */
    suspend fun getCoins(): Int = withContext(Dispatchers.IO) {
        try {
            mmkv.decodeInt(KEY_COINS, 0)
        } catch (e: Exception) {
            // 异常处理：返回默认值0
            0
        }
    }
    
    /**
     * 增加金币
     * 
     * Requirements: 10.1 - 当用户获得金币时，系统应立即保存金币余额到本地存储
     * 
     * @param amount 要增加的金币数量
     * @return 增加后的总金币数量
     */
    suspend fun addCoins(amount: Int): Int = withContext(Dispatchers.IO) {
        try {
            val currentCoins = getCoins()
            val newCoins = currentCoins + amount
            mmkv.encode(KEY_COINS, newCoins)
            newCoins
        } catch (e: Exception) {
            // 异常处理：返回当前金币数量
            getCoins()
        }
    }
    
    /**
     * 设置金币余额（主要用于测试）
     * 
     * @param coins 要设置的金币数量
     */
    suspend fun setCoins(coins: Int): Unit = withContext(Dispatchers.IO) {
        try {
            mmkv.encode(KEY_COINS, coins)
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    // ==================== 签到功能相关操作 ====================
    
    /**
     * 执行每日签到
     * 
     * Requirements: 5.2 - 当用户点击签到任务的"Claim"按钮时，系统应更新用户的签到记录
     * Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
     * Requirements: 10.3 - 当用户签到时，系统应保存签到记录和连续签到天数
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     * 
     * 签到逻辑说明：
     * 1. 检查今日是否已签到，如果已签到则返回false
     * 2. 更新最后签到日期为今天
     * 3. 计算连续签到天数：
     *    - 如果昨天有签到，连续天数+1
     *    - 如果昨天没有签到，重新开始计算（设为1）
     * 4. 保存连续签到天数
     * 
     * @return true表示签到成功，false表示今日已签到
     */
    suspend fun checkIn(): Boolean = withContext(Dispatchers.IO) {
        try {
            val today = timeManager.getCurrentDateString()
            val lastCheckInDate = getLastCheckInDate()
            
            // 检查今日是否已签到
            if (today == lastCheckInDate) {
                return@withContext false
            }
            
            // 更新签到日期
            mmkv.encode(KEY_LAST_CHECK_IN_DATE, today)
            
            // 计算连续签到天数
            val consecutiveDays = if (lastCheckInDate.isNotEmpty() && isConsecutiveDay(lastCheckInDate, today)) {
                // 昨天有签到，连续天数+1
                val currentConsecutive = mmkv.decodeInt(KEY_CONSECUTIVE_CHECK_INS, 0)
                currentConsecutive + 1
            } else {
                // 昨天没有签到或首次签到，重新开始计算
                1
            }
            
            mmkv.encode(KEY_CONSECUTIVE_CHECK_INS, consecutiveDays)
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取连续签到天数
     * 
     * Requirements: 10.3 - 当用户签到时，系统应保存签到记录和连续签到天数
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     * 
     * @return 连续签到天数
     */
    suspend fun getConsecutiveCheckIns(): Int = withContext(Dispatchers.IO) {
        try {
            val lastCheckInDate = getLastCheckInDate()
            val today = timeManager.getCurrentDateString()
            
            // 如果上次签到不是昨天或今天，重置连续签到天数
            if (!isConsecutiveDay(lastCheckInDate, today) && lastCheckInDate != today) {
                mmkv.encode(KEY_CONSECUTIVE_CHECK_INS, 0)
                return@withContext 0
            }
            
            mmkv.decodeInt(KEY_CONSECUTIVE_CHECK_INS, 0)
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 检查连续签到是否中断
     * 
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     * 
     * @return true表示连续签到中断，false表示连续签到正常
     */
    suspend fun isCheckInStreakBroken(): Boolean = withContext(Dispatchers.IO) {
        try {
            val lastCheckInDate = getLastCheckInDate()
            val today = timeManager.getCurrentDateString()
            val yesterday = getYesterdayDateString()
            
            // 如果从未签到，不算中断
            if (lastCheckInDate.isEmpty()) {
                return@withContext false
            }
            
            // 如果今天已签到，检查是否与昨天连续
            if (lastCheckInDate == today) {
                // 检查今天之前的连续性
                val storedConsecutiveDays = mmkv.decodeInt(KEY_CONSECUTIVE_CHECK_INS, 0)
                return@withContext storedConsecutiveDays == 1 && !isConsecutiveDay(yesterday, today)
            }
            
            // 如果最后签到不是今天也不是昨天，说明中断了
            return@withContext lastCheckInDate != yesterday
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取昨天的日期字符串
     * 
     * @return 昨天的日期字符串，格式为"yyyy-MM-dd"
     */
    private fun getYesterdayDateString(): String {
        return try {
            val calendar = java.util.Calendar.getInstance()
            calendar.add(java.util.Calendar.DAY_OF_MONTH, -1)
            java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(calendar.time)
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 检查今日是否已签到
     * 
     * Requirements: 10.3 - 当用户签到时，系统应保存签到记录和连续签到天数
     * 
     * @return true表示今日已签到，false表示今日未签到
     */
    suspend fun isCheckedInToday(): Boolean = withContext(Dispatchers.IO) {
        try {
            val today = timeManager.getCurrentDateString()
            val lastCheckInDate = getLastCheckInDate()
            today == lastCheckInDate
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取最后签到日期
     * 
     * @return 最后签到日期字符串，格式为"yyyy-MM-dd"
     */
    private suspend fun getLastCheckInDate(): String = withContext(Dispatchers.IO) {
        try {
            mmkv.decodeString(KEY_LAST_CHECK_IN_DATE, "") ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 检查两个日期是否连续
     * 
     * @param lastDate 上次日期
     * @param currentDate 当前日期
     * @return true表示连续，false表示不连续
     */
    private fun isConsecutiveDay(lastDate: String, currentDate: String): Boolean {
        if (lastDate.isEmpty() || currentDate.isEmpty()) return false
        
        try {
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
            val lastDateObj = dateFormat.parse(lastDate) ?: return false
            val currentDateObj = dateFormat.parse(currentDate) ?: return false
            
            val diffInMillis = currentDateObj.time - lastDateObj.time
            val diffInDays = diffInMillis / (1000 * 60 * 60 * 24)
            
            return diffInDays == 1L
        } catch (e: Exception) {
            return false
        }
    }
    
    // ==================== 任务状态相关操作 ====================
    
    /**
     * 获取任务状态
     * 
     * Requirements: 10.2 - 当用户完成任务时，系统应保存任务完成状态和时间戳
     * 
     * @param taskType 任务类型
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 任务状态
     */
    suspend fun getTaskStatus(taskType: TaskType, date: String = timeManager.getCurrentDateString()): TaskStatus = withContext(Dispatchers.IO) {
        try {
            val key = when (taskType) {
                TaskType.DAILY_CHECK_IN, TaskType.LUCKY_WHEEL, TaskType.STEPS_1000, TaskType.STEPS_3000 -> {
                    "${KEY_DAILY_TASKS_PREFIX}${date}_${taskType.name}"
                }
                TaskType.CHECK_IN_2_DAYS, TaskType.CHECK_IN_3_DAYS -> {
                    "${KEY_OTHER_TASKS_PREFIX}${taskType.name}"
                }
            }
            
            val statusName = mmkv.decodeString(key, TaskStatus.NOT_COMPLETED.name) ?: TaskStatus.NOT_COMPLETED.name
            TaskStatus.valueOf(statusName)
        } catch (e: Exception) {
            TaskStatus.NOT_COMPLETED
        }
    }
    
    /**
     * 更新任务状态
     * 
     * Requirements: 10.2 - 当用户完成任务时，系统应保存任务完成状态和时间戳
     * 
     * @param taskType 任务类型
     * @param status 新的任务状态
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     */
    suspend fun updateTaskStatus(taskType: TaskType, status: TaskStatus, date: String = timeManager.getCurrentDateString()): Unit = withContext(Dispatchers.IO) {
        try {
            val key = when (taskType) {
                TaskType.DAILY_CHECK_IN, TaskType.LUCKY_WHEEL, TaskType.STEPS_1000, TaskType.STEPS_3000 -> {
                    "${KEY_DAILY_TASKS_PREFIX}${date}_${taskType.name}"
                }
                TaskType.CHECK_IN_2_DAYS, TaskType.CHECK_IN_3_DAYS -> {
                    "${KEY_OTHER_TASKS_PREFIX}${taskType.name}"
                }
            }
            
            mmkv.encode(key, status.name)
            
            // 如果是签到任务被标记为已领取，执行签到逻辑
            if (taskType == TaskType.DAILY_CHECK_IN && status == TaskStatus.CLAIMED) {
                checkIn()
            }
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    /**
     * 重置每日任务状态
     * 
     * Requirements: 10.7 - 当新的一天开始时，系统应自动重置每日任务状态但保留历史记录
     * 
     * @param date 要重置的日期，默认为今天
     */
    suspend fun resetDailyTasks(date: String = timeManager.getCurrentDateString()): Unit = withContext(Dispatchers.IO) {
        try {
            val dailyTaskTypes = listOf(
                TaskType.DAILY_CHECK_IN,
                TaskType.LUCKY_WHEEL,
                TaskType.STEPS_1000,
                TaskType.STEPS_3000
            )
            
            dailyTaskTypes.forEach { taskType ->
                val key = "${KEY_DAILY_TASKS_PREFIX}${date}_${taskType.name}"
                mmkv.encode(key, TaskStatus.NOT_COMPLETED.name)
            }
            
            // 重置转盘游戏次数
            resetLuckyWheelCount(date)
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    // ==================== 转盘游戏相关操作 ====================
    
    /**
     * 获取转盘游戏次数
     * 
     * Requirements: 10.5 - 添加转盘游戏次数统计功能
     * 
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 当日转盘游戏次数
     */
    suspend fun getLuckyWheelCount(date: String = timeManager.getCurrentDateString()): Int = withContext(Dispatchers.IO) {
        try {
            val key = "${KEY_LUCKY_WHEEL_COUNT_PREFIX}$date"
            mmkv.decodeInt(key, 0)
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 增加转盘游戏次数
     * 
     * Requirements: 10.5 - 添加转盘游戏次数统计功能
     * 
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 增加后的转盘游戏次数
     */
    suspend fun incrementLuckyWheelCount(date: String = timeManager.getCurrentDateString()): Int = withContext(Dispatchers.IO) {
        try {
            val key = "${KEY_LUCKY_WHEEL_COUNT_PREFIX}$date"
            val currentCount = getLuckyWheelCount(date)
            val newCount = currentCount + 1
            mmkv.encode(key, newCount)
            
            // 如果达到每日限制，更新任务状态为已完成
            if (newCount >= LUCKY_WHEEL_DAILY_LIMIT) {
                updateTaskStatus(TaskType.LUCKY_WHEEL, TaskStatus.COMPLETED, date)
            }
            
            newCount
        } catch (e: Exception) {
            getLuckyWheelCount(date)
        }
    }
    
    /**
     * 重置转盘游戏次数
     * 
     * @param date 要重置的日期
     */
    private suspend fun resetLuckyWheelCount(date: String): Unit = withContext(Dispatchers.IO) {
        try {
            val key = "${KEY_LUCKY_WHEEL_COUNT_PREFIX}$date"
            mmkv.encode(key, 0)
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    /**
     * 获取游戏次数（通用方法）
     * 
     * @param gameType 游戏类型
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 当日游戏次数
     */
    suspend fun getGameCount(gameType: com.example.step0724.data.model.GameType, date: String = timeManager.getCurrentDateString()): Int = withContext(Dispatchers.IO) {
        try {
            val key = when (gameType) {
                com.example.step0724.data.model.GameType.LUCKY_WHEEL -> "${KEY_LUCKY_WHEEL_COUNT_PREFIX}$date"
                com.example.step0724.data.model.GameType.EGG_SMASH -> "${KEY_EGG_SMASH_COUNT_PREFIX}$date"
                com.example.step0724.data.model.GameType.LUCKY_SCRATCH -> "${KEY_LUCKY_SCRATCH_COUNT_PREFIX}$date"
            }
            mmkv.decodeInt(key, 0)
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 增加游戏次数（通用方法）
     * 
     * @param gameType 游戏类型
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 增加后的游戏次数
     */
    suspend fun incrementGameCount(gameType: com.example.step0724.data.model.GameType, date: String = timeManager.getCurrentDateString()): Int = withContext(Dispatchers.IO) {
        try {
            val key = when (gameType) {
                com.example.step0724.data.model.GameType.LUCKY_WHEEL -> "${KEY_LUCKY_WHEEL_COUNT_PREFIX}$date"
                com.example.step0724.data.model.GameType.EGG_SMASH -> "${KEY_EGG_SMASH_COUNT_PREFIX}$date"
                com.example.step0724.data.model.GameType.LUCKY_SCRATCH -> "${KEY_LUCKY_SCRATCH_COUNT_PREFIX}$date"
            }
            
            val currentCount = getGameCount(gameType, date)
            val newCount = currentCount + 1
            mmkv.encode(key, newCount)
            
            // 检查是否达到每日限制，更新对应任务状态
            val dailyLimit = when (gameType) {
                com.example.step0724.data.model.GameType.LUCKY_WHEEL -> LUCKY_WHEEL_DAILY_LIMIT
                com.example.step0724.data.model.GameType.EGG_SMASH -> EGG_SMASH_DAILY_LIMIT
                com.example.step0724.data.model.GameType.LUCKY_SCRATCH -> LUCKY_SCRATCH_DAILY_LIMIT
            }
            
            // 只有转盘游戏有对应的任务，其他游戏暂时没有每日任务
            if (gameType == com.example.step0724.data.model.GameType.LUCKY_WHEEL && newCount >= dailyLimit) {
                updateTaskStatus(TaskType.LUCKY_WHEEL, TaskStatus.COMPLETED, date)
            }
            
            newCount
        } catch (e: Exception) {
            getGameCount(gameType, date)
        }
    }
    
    // ==================== 数据清理和维护 ====================
    
    /**
     * 清理过期数据
     * 
     * Requirements: 10.4 - 当用户重新打开应用时，系统应恢复之前的金币余额和任务状态
     * 
     * @param daysToKeep 保留的天数，默认30天
     */
    suspend fun cleanupOldData(daysToKeep: Int = 30): Unit = withContext(Dispatchers.IO) {
        try {
            val calendar = java.util.Calendar.getInstance()
            calendar.add(java.util.Calendar.DAY_OF_MONTH, -daysToKeep)
            val cutoffDate = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(calendar.time)
            
            // 这里可以实现清理逻辑，但MMKV没有直接的键枚举功能
            // 实际项目中可能需要维护一个键列表或使用其他方式
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    /**
     * 获取所有存储的键值对数量（用于调试）
     * 
     * @return 存储的键值对数量
     */
    suspend fun getStorageInfo(): Map<String, Any> = withContext(Dispatchers.IO) {
        try {
            mapOf(
                "coins" to getCoins(),
                "consecutiveCheckIns" to getConsecutiveCheckIns(),
                "isCheckedInToday" to isCheckedInToday(),
                "todayLuckyWheelCount" to getLuckyWheelCount(),
                "lastCheckInDate" to getLastCheckInDate()
            )
        } catch (e: Exception) {
            emptyMap()
        }
    }
}