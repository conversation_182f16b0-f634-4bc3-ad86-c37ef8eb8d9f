package com.example.step0724.data.repository

import com.example.step0724.R
import com.example.step0724.data.model.EarnData
import com.example.step0724.data.model.Task
import com.example.step0724.data.model.TaskStatus
import com.example.step0724.data.model.TaskType
import com.example.step0724.data.storage.EarnStorage
import com.example.step0724.data.storage.TimeManager
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * EarnRepository - Earn功能业务逻辑层
 * 
 * 整合数据访问，提供Earn功能的核心业务逻辑，包括：
 * - 构建页面数据
 * - 处理任务奖励领取
 * - 任务状态验证和更新
 * - 每日任务和其他任务列表构建
 * 
 * Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8
 */
@Singleton
class EarnRepository @Inject constructor(
    private val earnStorage: EarnStorage,
    private val stepRepository: StepRepository,
    private val timeManager: TimeManager
) {
    
    /**
     * 获取Earn页面完整数据
     * 
     * Requirements: 4.1 - 当用户查看Earn页面时，系统应显示"Daily tasks"区域
     * Requirements: 7.1 - 当用户查看Earn页面时，系统应显示"Other tasks"区域
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     * 
     * @return EarnData包含页面所需的所有数据
     */
    suspend fun getEarnData(): EarnData {
        val today = timeManager.getCurrentDateString()
        val coins = earnStorage.getCoins()
        val currentSteps = stepRepository.getTodaySteps().first()
        val consecutiveCheckIns = earnStorage.getConsecutiveCheckIns()
        
        // 检查并重置中断的连续签到任务
        checkAndResetConsecutiveCheckInTasks()
        
        val dailyTasks = buildDailyTasks(today, currentSteps)
        val otherTasks = buildOtherTasks(consecutiveCheckIns)
        val timeUntilReset = timeManager.getTimeUntilMidnight()
        
        return EarnData(
            coins = coins,
            dailyTasks = dailyTasks,
            otherTasks = otherTasks,
            timeUntilReset = timeUntilReset,
            consecutiveCheckIns = consecutiveCheckIns
        )
    }
    
    /**
     * 处理任务奖励领取
     * 
     * Requirements: 4.5 - 当用户完成任务但未领取奖励时，系统应显示"Claim"按钮
     * Requirements: 5.2 - 当用户点击签到任务的"Claim"按钮时，系统应给用户增加签到奖励金币
     * Requirements: 6.4 - 当用户点击步数任务的"Claim"按钮时，系统应给用户增加步数任务奖励金币
     * Requirements: 7.6 - 当用户点击连续签到任务的"Claim"按钮时，系统应给用户增加连续签到奖励金币
     * 
     * @param taskType 要领取奖励的任务类型
     * @return Result<Int> 成功时返回奖励金币数量，失败时返回错误信息
     */
    suspend fun claimTask(taskType: TaskType): Result<Int> {
        return try {
            // 特殊处理签到任务
            if (taskType == TaskType.DAILY_CHECK_IN) {
                val checkInResult = performDailyCheckIn()
                return checkInResult.map { it.rewardAmount }
            }
            
            val today = timeManager.getCurrentDateString()
            val currentStatus = earnStorage.getTaskStatus(taskType, today)
            
            // 验证任务是否可以领取
            if (currentStatus != TaskStatus.COMPLETED) {
                return Result.failure(Exception("Task is not ready to claim"))
            }
            
            // 获取奖励金币数量
            val rewardAmount = getTaskReward(taskType)
            
            // 验证任务完成条件（二次验证）
            if (!isTaskCompleted(taskType, today)) {
                return Result.failure(Exception("Task completion condition not met"))
            }
            
            // 更新任务状态为已领取
            earnStorage.updateTaskStatus(taskType, TaskStatus.CLAIMED, today)
            
            // 增加金币
            earnStorage.addCoins(rewardAmount)
            
            Result.success(rewardAmount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 构建每日任务列表
     * 
     * Requirements: 4.3 - 当用户查看每日任务时，系统应包含以下任务：Daily check in、Lucky Wheel、1000 steps、3000 steps
     * Requirements: 6.1 - 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取
     * Requirements: 6.2 - 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取
     * 
     * @param date 日期字符串
     * @param currentSteps 当前步数
     * @return 每日任务列表
     */
    suspend fun buildDailyTasks(date: String, currentSteps: Int): List<Task> {
        val tasks = mutableListOf<Task>()
        
        // 每日签到任务
        val checkInStatus = getCheckInTaskStatus()
        tasks.add(
            Task(
                type = TaskType.DAILY_CHECK_IN,
                title = "Daily check in",
                description = "Sign in daily to earn coins",
                reward = EarnStorage.REWARD_DAILY_CHECK_IN,
                status = checkInStatus,
                progress = if (checkInStatus != TaskStatus.NOT_COMPLETED) 1 else 0,
                target = 1,
                iconRes = R.mipmap.task_check
            )
        )
        
        // 转盘游戏任务
        val luckyWheelStatus = getLuckyWheelTaskStatus(date)
        val luckyWheelCount = earnStorage.getLuckyWheelCount(date)
        tasks.add(
            Task(
                type = TaskType.LUCKY_WHEEL,
                title = "Lucky Wheel",
                description = "Play Lucky Wheel ${luckyWheelCount}/${EarnStorage.LUCKY_WHEEL_DAILY_LIMIT} times",
                reward = EarnStorage.REWARD_LUCKY_WHEEL_DAILY,
                status = luckyWheelStatus,
                progress = luckyWheelCount,
                target = EarnStorage.LUCKY_WHEEL_DAILY_LIMIT,
                iconRes = R.mipmap.task_wheel
            )
        )
        
        // 1000步任务
        val steps1000Status = getStepsTaskStatus(TaskType.STEPS_1000, currentSteps, 1000, date)
        tasks.add(
            Task(
                type = TaskType.STEPS_1000,
                title = "1000 steps",
                description = "Walk 1000 steps today ($currentSteps/1000)",
                reward = EarnStorage.REWARD_STEPS_1000,
                status = steps1000Status,
                progress = minOf(currentSteps, 1000),
                target = 1000,
                iconRes = R.mipmap.task_step
            )
        )
        
        // 3000步任务
        val steps3000Status = getStepsTaskStatus(TaskType.STEPS_3000, currentSteps, 3000, date)
        tasks.add(
            Task(
                type = TaskType.STEPS_3000,
                title = "3000 steps",
                description = "Walk 3000 steps today ($currentSteps/3000)",
                reward = EarnStorage.REWARD_STEPS_3000,
                status = steps3000Status,
                progress = minOf(currentSteps, 3000),
                target = 3000,
                iconRes = R.mipmap.task_step
            )
        )
        
        return tasks
    }
    
    /**
     * 构建其他任务列表
     * 
     * Requirements: 7.2 - 当用户查看其他任务时，系统应包含：Check in 2 days、Check in 3 days
     * Requirements: 7.3 - 当用户连续签到达到要求天数但未领取奖励时，系统应显示"Claim"按钮
     * 
     * @param consecutiveCheckIns 连续签到天数
     * @return 其他任务列表
     */
    suspend fun buildOtherTasks(consecutiveCheckIns: Int): List<Task> {
        val tasks = mutableListOf<Task>()
        
        // 连续签到2天任务
        val checkIn2DaysStatus = getConsecutiveCheckInTaskStatus(TaskType.CHECK_IN_2_DAYS, consecutiveCheckIns, 2)
        tasks.add(
            Task(
                type = TaskType.CHECK_IN_2_DAYS,
                title = "Check in 2 days",
                description = "Check in for 2 consecutive days",
                reward = EarnStorage.REWARD_CHECK_IN_2_DAYS,
                status = checkIn2DaysStatus,
                progress = minOf(consecutiveCheckIns, 2),
                target = 2,
                iconRes = R.mipmap.task_check
            )
        )
        
        // 连续签到3天任务
        val checkIn3DaysStatus = getConsecutiveCheckInTaskStatus(TaskType.CHECK_IN_3_DAYS, consecutiveCheckIns, 3)
        tasks.add(
            Task(
                type = TaskType.CHECK_IN_3_DAYS,
                title = "Check in 3 days",
                description = "Check in for 3 consecutive days",
                reward = EarnStorage.REWARD_CHECK_IN_3_DAYS,
                status = checkIn3DaysStatus,
                progress = minOf(consecutiveCheckIns, 3),
                target = 3,
                iconRes = R.mipmap.task_check
            )
        )
        
        return tasks
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 获取签到任务状态
     * 
     * Requirements: 5.1 - 当用户每天首次查看签到任务时，系统应显示"Claim"按钮（如果还未签到）
     * Requirements: 5.3 - 当用户已完成当日签到时，系统应显示勾选标记且不可再次点击
     * Requirements: 5.4 - 当新的一天开始时，系统应重置签到状态，允许用户再次签到
     */
    private suspend fun getCheckInTaskStatus(): TaskStatus {
        val today = timeManager.getCurrentDateString()
        val storedStatus = earnStorage.getTaskStatus(TaskType.DAILY_CHECK_IN, today)
        
        return if (earnStorage.isCheckedInToday()) {
            // 今日已签到，显示已完成状态
            TaskStatus.CLAIMED
        } else {
            // 今日未签到，检查存储的状态
            when (storedStatus) {
                TaskStatus.CLAIMED -> TaskStatus.CLAIMED // 已领取但可能是昨天的数据
                else -> TaskStatus.COMPLETED // 可以签到领取
            }
        }
    }
    
    /**
     * 获取转盘游戏任务状态
     * 
     * Requirements: 4.3 - Lucky Wheel（转盘游戏，0/10次）
     */
    private suspend fun getLuckyWheelTaskStatus(date: String): TaskStatus {
        val currentStatus = earnStorage.getTaskStatus(TaskType.LUCKY_WHEEL, date)
        val luckyWheelCount = earnStorage.getLuckyWheelCount(date)
        
        return when {
            currentStatus == TaskStatus.CLAIMED -> TaskStatus.CLAIMED
            luckyWheelCount >= EarnStorage.LUCKY_WHEEL_DAILY_LIMIT -> TaskStatus.COMPLETED
            else -> TaskStatus.NOT_COMPLETED
        }
    }
    
    /**
     * 获取步数任务状态
     * 
     * Requirements: 6.1 - 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取
     * Requirements: 6.2 - 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取
     * Requirements: 6.6 - 当用户已领取步数任务奖励时，系统应显示勾选标记且不可再次领取
     */
    private suspend fun getStepsTaskStatus(taskType: TaskType, currentSteps: Int, targetSteps: Int, date: String): TaskStatus {
        val storedStatus = earnStorage.getTaskStatus(taskType, date)
        
        // 如果已经领取过奖励，保持已领取状态
        if (storedStatus == TaskStatus.CLAIMED) {
            return TaskStatus.CLAIMED
        }
        
        // 根据当前步数判断任务状态
        val shouldBeCompleted = currentSteps >= targetSteps
        
        return when {
            shouldBeCompleted -> {
                // 步数已达标，如果存储状态不是已完成，则更新为已完成
                if (storedStatus != TaskStatus.COMPLETED) {
                    try {
                        earnStorage.updateTaskStatus(taskType, TaskStatus.COMPLETED, date)
                    } catch (e: Exception) {
                        // 更新失败时记录错误但不影响状态返回
                    }
                }
                TaskStatus.COMPLETED
            }
            else -> {
                // 步数未达标，显示灰色不可点击的Claim按钮
                if (storedStatus == TaskStatus.COMPLETED) {
                    try {
                        earnStorage.updateTaskStatus(taskType, TaskStatus.NOT_ELIGIBLE, date)
                    } catch (e: Exception) {
                        // 更新失败时记录错误但不影响状态返回
                    }
                }
                TaskStatus.NOT_ELIGIBLE
            }
        }
    }
    
    /**
     * 获取连续签到任务状态
     * 
     * Requirements: 7.3 - 当用户连续签到达到要求天数但未领取奖励时，系统应显示"Claim"按钮
     * Requirements: 7.5 - 当用户已领取连续签到奖励时，系统应显示勾选标记且不可再次领取
     */
    private suspend fun getConsecutiveCheckInTaskStatus(taskType: TaskType, consecutiveCheckIns: Int, targetDays: Int): TaskStatus {
        val currentStatus = earnStorage.getTaskStatus(taskType)
        
        return when {
            currentStatus == TaskStatus.CLAIMED -> TaskStatus.CLAIMED
            consecutiveCheckIns >= targetDays -> TaskStatus.COMPLETED
            else -> TaskStatus.NOT_ELIGIBLE // 未达到条件时显示灰色不可点击的Claim按钮
        }
    }
    
    /**
     * 获取任务奖励金币数量
     */
    private fun getTaskReward(taskType: TaskType): Int {
        return when (taskType) {
            TaskType.DAILY_CHECK_IN -> EarnStorage.REWARD_DAILY_CHECK_IN
            TaskType.LUCKY_WHEEL -> EarnStorage.REWARD_LUCKY_WHEEL_DAILY
            TaskType.STEPS_1000 -> EarnStorage.REWARD_STEPS_1000
            TaskType.STEPS_3000 -> EarnStorage.REWARD_STEPS_3000
            TaskType.CHECK_IN_2_DAYS -> EarnStorage.REWARD_CHECK_IN_2_DAYS
            TaskType.CHECK_IN_3_DAYS -> EarnStorage.REWARD_CHECK_IN_3_DAYS
        }
    }
    
    /**
     * 验证任务是否真正完成（二次验证）
     * 
     * Requirements: 任务状态验证和更新逻辑
     */
    private suspend fun isTaskCompleted(taskType: TaskType, date: String): Boolean {
        return when (taskType) {
            TaskType.DAILY_CHECK_IN -> {
                // 签到任务：检查是否还未签到
                !earnStorage.isCheckedInToday()
            }
            TaskType.LUCKY_WHEEL -> {
                // 转盘任务：检查是否达到每日限制
                earnStorage.getLuckyWheelCount(date) >= EarnStorage.LUCKY_WHEEL_DAILY_LIMIT
            }
            TaskType.STEPS_1000 -> {
                // 1000步任务：检查当前步数
                val currentSteps = stepRepository.getTodaySteps().first()
                currentSteps >= 1000
            }
            TaskType.STEPS_3000 -> {
                // 3000步任务：检查当前步数
                val currentSteps = stepRepository.getTodaySteps().first()
                currentSteps >= 3000
            }
            TaskType.CHECK_IN_2_DAYS -> {
                // 连续签到2天：检查连续签到天数
                earnStorage.getConsecutiveCheckIns() >= 2
            }
            TaskType.CHECK_IN_3_DAYS -> {
                // 连续签到3天：检查连续签到天数
                earnStorage.getConsecutiveCheckIns() >= 3
            }
        }
    }
    
    /**
     * 重置每日任务（在新的一天开始时调用）
     * 
     * Requirements: 4.2 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     * Requirements: 5.4 - 当新的一天开始时，系统应重置签到状态，允许用户再次签到
     * Requirements: 6.7 - 当新的一天开始时，系统应重置步数任务状态
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     */
    suspend fun resetDailyTasks(): Result<Unit> {
        return try {
            val today = timeManager.getCurrentDateString()
            
            // 重置每日任务状态
            earnStorage.resetDailyTasks(today)
            
            // 检查并处理连续签到中断
            checkAndResetConsecutiveCheckInTasks()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 更新转盘游戏次数
     * 
     * Requirements: 9.8 - 当用户参与游戏时，系统应记录游戏次数和获得的奖励
     */
    suspend fun incrementLuckyWheelCount(): Result<Int> {
        return try {
            val today = timeManager.getCurrentDateString()
            val newCount = earnStorage.incrementLuckyWheelCount(today)
            Result.success(newCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取当前步数（用于实时更新任务状态）
     * 
     * Requirements: 6.1, 6.2 - 步数任务状态的实时更新
     */
    suspend fun getCurrentSteps(): Int {
        return try {
            stepRepository.getTodaySteps().first()
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 获取今日步数流（用于实时监控）
     * 
     * Requirements: 6.3 - 实现步数任务状态的实时更新逻辑
     * 
     * @return Flow<Int> 今日步数的实时流
     */
    suspend fun getTodayStepsFlow() = stepRepository.getTodaySteps()
    
    /**
     * 检查步数任务是否达成目标
     * 
     * Requirements: 6.3 - 添加步数目标达成检测
     * 
     * @param currentSteps 当前步数
     * @return Pair<Boolean, Boolean> 分别表示是否达成1000步和3000步目标
     */
    fun checkStepGoalAchievement(currentSteps: Int): Pair<Boolean, Boolean> {
        val reached1000 = currentSteps >= 1000
        val reached3000 = currentSteps >= 3000
        return Pair(reached1000, reached3000)
    }
    
    /**
     * 更新步数任务状态（当步数发生变化时调用）
     * 
     * Requirements: 6.1 - 当用户的当日步数达到1000步时，系统应将1000步任务状态改为可领取
     * Requirements: 6.2 - 当用户的当日步数达到3000步时，系统应将3000步任务状态改为可领取
     * Requirements: 6.3 - 实现步数任务状态的实时更新逻辑
     * 
     * @param currentSteps 当前步数
     * @return Result<Unit> 更新结果
     */
    suspend fun updateStepTasksStatus(currentSteps: Int): Result<Unit> {
        return try {
            val today = timeManager.getCurrentDateString()
            val (reached1000, reached3000) = checkStepGoalAchievement(currentSteps)
            
            // 更新1000步任务状态
            val current1000Status = earnStorage.getTaskStatus(TaskType.STEPS_1000, today)
            if (reached1000 && current1000Status == TaskStatus.NOT_COMPLETED) {
                earnStorage.updateTaskStatus(TaskType.STEPS_1000, TaskStatus.COMPLETED, today)
            } else if (!reached1000 && current1000Status == TaskStatus.COMPLETED) {
                // 如果步数下降（虽然不太可能），重置状态
                earnStorage.updateTaskStatus(TaskType.STEPS_1000, TaskStatus.NOT_COMPLETED, today)
            }
            
            // 更新3000步任务状态
            val current3000Status = earnStorage.getTaskStatus(TaskType.STEPS_3000, today)
            if (reached3000 && current3000Status == TaskStatus.NOT_COMPLETED) {
                earnStorage.updateTaskStatus(TaskType.STEPS_3000, TaskStatus.COMPLETED, today)
            } else if (!reached3000 && current3000Status == TaskStatus.COMPLETED) {
                // 如果步数下降（虽然不太可能），重置状态
                earnStorage.updateTaskStatus(TaskType.STEPS_3000, TaskStatus.NOT_COMPLETED, today)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 执行每日签到检查和处理
     * 
     * Requirements: 5.1 - 当用户每天首次查看签到任务时，系统应显示"Claim"按钮（如果还未签到）
     * Requirements: 5.2 - 当用户点击签到任务的"Claim"按钮时，系统应给用户增加签到奖励金币
     * Requirements: 5.3 - 当用户点击签到任务的"Claim"按钮时，系统应将按钮状态改为勾选标记
     * Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     * 
     * 签到处理流程：
     * 1. 验证今日是否已签到
     * 2. 执行签到操作，更新签到记录
     * 3. 计算并更新连续签到天数
     * 4. 更新签到任务状态为已完成
     * 5. 发放签到奖励金币
     * 6. 检查并更新连续签到相关任务状态
     * 
     * @return Result<CheckInResult> 签到结果，包含奖励金币和连续签到天数
     */
    suspend fun performDailyCheckIn(): Result<CheckInResult> {
        return try {
            val today = timeManager.getCurrentDateString()
            
            // 1. 检查今日是否已签到
            if (earnStorage.isCheckedInToday()) {
                return Result.failure(Exception("Already checked in today"))
            }
            
            // 2. 执行签到操作
            val checkInSuccess = earnStorage.checkIn()
            if (!checkInSuccess) {
                return Result.failure(Exception("Check-in operation failed"))
            }
            
            // 3. 获取签到后的连续天数
            val consecutiveCheckIns = earnStorage.getConsecutiveCheckIns()
            
            // 4. 更新签到任务状态为已领取
            earnStorage.updateTaskStatus(TaskType.DAILY_CHECK_IN, TaskStatus.CLAIMED, today)
            
            // 5. 增加签到奖励金币
            val rewardAmount = EarnStorage.REWARD_DAILY_CHECK_IN
            val newCoinBalance = earnStorage.addCoins(rewardAmount)
            
            // 6. 检查并更新连续签到任务状态
            updateConsecutiveCheckInTasks(consecutiveCheckIns)
            
            // 返回签到结果
            Result.success(CheckInResult(rewardAmount, consecutiveCheckIns))
        } catch (e: Exception) {
            Result.failure(Exception("Daily check-in failed: ${e.message}", e))
        }
    }
    
    /**
     * 更新连续签到任务状态
     * 
     * Requirements: 7.3 - 当用户连续签到达到要求天数但未领取奖励时，系统应显示"Claim"按钮
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     */
    private suspend fun updateConsecutiveCheckInTasks(consecutiveCheckIns: Int) {
        // 更新连续签到2天任务状态
        if (consecutiveCheckIns >= 2) {
            val current2DayStatus = earnStorage.getTaskStatus(TaskType.CHECK_IN_2_DAYS)
            if (current2DayStatus == TaskStatus.NOT_COMPLETED) {
                earnStorage.updateTaskStatus(TaskType.CHECK_IN_2_DAYS, TaskStatus.COMPLETED)
            }
        }
        
        // 更新连续签到3天任务状态
        if (consecutiveCheckIns >= 3) {
            val current3DayStatus = earnStorage.getTaskStatus(TaskType.CHECK_IN_3_DAYS)
            if (current3DayStatus == TaskStatus.NOT_COMPLETED) {
                earnStorage.updateTaskStatus(TaskType.CHECK_IN_3_DAYS, TaskStatus.COMPLETED)
            }
        }
    }
    
    /**
     * 检查并重置中断的连续签到任务
     * 
     * Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
     */
    suspend fun checkAndResetConsecutiveCheckInTasks(): Result<Unit> {
        return try {
            val consecutiveCheckIns = earnStorage.getConsecutiveCheckIns()
            
            // 如果连续签到天数小于2，重置2天任务状态
            if (consecutiveCheckIns < 2) {
                val current2DayStatus = earnStorage.getTaskStatus(TaskType.CHECK_IN_2_DAYS)
                if (current2DayStatus != TaskStatus.CLAIMED) {
                    earnStorage.updateTaskStatus(TaskType.CHECK_IN_2_DAYS, TaskStatus.NOT_COMPLETED)
                }
            }
            
            // 如果连续签到天数小于3，重置3天任务状态
            if (consecutiveCheckIns < 3) {
                val current3DayStatus = earnStorage.getTaskStatus(TaskType.CHECK_IN_3_DAYS)
                if (current3DayStatus != TaskStatus.CLAIMED) {
                    earnStorage.updateTaskStatus(TaskType.CHECK_IN_3_DAYS, TaskStatus.NOT_COMPLETED)
                }
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取详细的签到状态信息
     * 
     * Requirements: 5.1 - 当用户每天首次查看签到任务时，系统应显示"Claim"按钮（如果还未签到）
     * Requirements: 5.3 - 当用户已完成当日签到时，系统应显示勾选标记且不可再次点击
     * Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
     * 
     * @return CheckInStatus 包含签到状态的详细信息
     */
    suspend fun getCheckInStatus(): CheckInStatus {
        return try {
            val isCheckedInToday = earnStorage.isCheckedInToday()
            val consecutiveCheckIns = earnStorage.getConsecutiveCheckIns()
            val isStreakBroken = earnStorage.isCheckInStreakBroken()
            
            CheckInStatus(
                isCheckedInToday = isCheckedInToday,
                consecutiveCheckIns = consecutiveCheckIns,
                isStreakBroken = isStreakBroken,
                canCheckIn = !isCheckedInToday
            )
        } catch (e: Exception) {
            CheckInStatus(
                isCheckedInToday = false,
                consecutiveCheckIns = 0,
                isStreakBroken = false,
                canCheckIn = true
            )
        }
    }
    
    /**
     * 签到结果数据类
     */
    data class CheckInResult(
        val rewardAmount: Int,
        val consecutiveCheckIns: Int
    )
    
    /**
     * 签到状态数据类
     */
    data class CheckInStatus(
        val isCheckedInToday: Boolean,
        val consecutiveCheckIns: Int,
        val isStreakBroken: Boolean,
        val canCheckIn: Boolean
    )
    
    // ==================== 游戏集成相关方法 ====================
    
    /**
     * 获取游戏入口列表
     * 
     * Requirements: 9.1, 9.2, 9.3 - 显示三个游戏选项：Lucky Wheel、Egg Smash、Memory Match
     * 
     * @return 游戏入口列表
     */
    fun getGameEntries(): List<com.example.step0724.data.model.GameEntry> {
        return listOf(
            com.example.step0724.data.model.GameEntry(
                type = com.example.step0724.data.model.GameType.LUCKY_WHEEL,
                title = "Lucky Wheel",
                iconRes = R.drawable.ic_play,
                description = "Spin the wheel to win coins"
            ),
            com.example.step0724.data.model.GameEntry(
                type = com.example.step0724.data.model.GameType.EGG_SMASH,
                title = "Egg Smash",
                iconRes = R.drawable.ic_activity,
                description = "Smash eggs to find treasures"
            ),
            com.example.step0724.data.model.GameEntry(
                type = com.example.step0724.data.model.GameType.LUCKY_SCRATCH,
                title = "Lucky Scratch",
                iconRes = R.drawable.ic_profile,
                description = "Match cards to win rewards"
            )
        )
    }
    
    /**
     * 更新游戏次数
     * 
     * Requirements: 9.8 - 当用户参与游戏时，系统应记录游戏次数和获得的奖励
     * 
     * @param gameType 游戏类型
     * @return Result<Int> 更新后的游戏次数
     */
    suspend fun incrementGameCount(gameType: com.example.step0724.data.model.GameType): Result<Int> {
        return try {
            val today = timeManager.getCurrentDateString()
            val newCount = earnStorage.incrementGameCount(gameType, today)
            Result.success(newCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取游戏次数
     * 
     * Requirements: 9.8 - 当用户参与游戏时，系统应记录游戏次数和获得的奖励
     * 
     * @param gameType 游戏类型
     * @return 当日游戏次数
     */
    suspend fun getGameCount(gameType: com.example.step0724.data.model.GameType): Int {
        return try {
            val today = timeManager.getCurrentDateString()
            earnStorage.getGameCount(gameType, today)
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 处理游戏奖励
     * 
     * Requirements: 9.5, 9.8 - 当用户在任何游戏中获得奖励时，系统应更新用户的金币余额
     * 
     * @param gameType 游戏类型
     * @param rewardAmount 奖励金币数量
     * @return Result<Int> 更新后的金币余额
     */
    suspend fun addGameReward(gameType: com.example.step0724.data.model.GameType, rewardAmount: Int): Result<Int> {
        return try {
            // 记录游戏次数
            incrementGameCount(gameType)
            
            // 增加金币奖励
            val newBalance = earnStorage.addCoins(rewardAmount)
            
            Result.success(newBalance)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 刷新Earn页面数据（从游戏页面返回时调用）
     * 
     * Requirements: 9.7 - 当用户从任何游戏页面返回Earn页面时，系统应刷新任务状态和金币余额
     * 
     * @return Result<EarnData> 刷新后的页面数据
     */
    suspend fun refreshDataAfterGame(): Result<EarnData> {
        return try {
            val earnData = getEarnData()
            Result.success(earnData)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取当前金币数量
     * 
     * @return 当前金币数量
     */
    suspend fun getCoins(): Int {
        return earnStorage.getCoins()
    }
    
    /**
     * 添加金币
     * 
     * @param amount 要添加的金币数量
     * @return 添加后的金币总数
     */
    suspend fun addCoins(amount: Int): Int {
        return earnStorage.addCoins(amount)
    }
}
