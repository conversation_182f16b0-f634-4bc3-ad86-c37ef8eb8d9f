package com.example.step0724.data.storage

import android.content.Context
import android.content.SharedPreferences
import java.text.SimpleDateFormat
import java.util.*

class GameStorage(context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences("game_prefs", Context.MODE_PRIVATE)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    companion object {
        private const val KEY_DAILY_CHANCES = "daily_chances"
        private const val KEY_LAST_DATE = "last_date"
        private const val KEY_SCRATCH_USED_CHANCES = "scratch_used_chances"
        private const val KEY_SCRATCH_LAST_DATE = "scratch_last_date"
        private const val DEFAULT_DAILY_CHANCES = 10
        private const val DEFAULT_SCRATCH_DAILY_CHANCES = 3
    }
    
    /**
     * 获取今日剩余次数
     */
    fun getDailyChances(): Int {
        val today = dateFormat.format(Date())
        val lastDate = prefs.getString(KEY_LAST_DATE, "")
        
        return if (today == lastDate) {
            // 同一天，返回剩余次数
            prefs.getInt(KEY_DAILY_CHANCES, DEFAULT_DAILY_CHANCES)
        } else {
            // 新的一天，重置次数
            resetDailyChances()
            DEFAULT_DAILY_CHANCES
        }
    }
    
    /**
     * 消耗一次游戏次数
     */
    fun consumeChance(): Boolean {
        val currentChances = getDailyChances()
        if (currentChances > 0) {
            val newChances = currentChances - 1
            saveDailyChances(newChances)
            return true
        }
        return false
    }
    
    /**
     * 重置每日次数
     */
    private fun resetDailyChances() {
        val today = dateFormat.format(Date())
        prefs.edit()
            .putInt(KEY_DAILY_CHANCES, DEFAULT_DAILY_CHANCES)
            .putString(KEY_LAST_DATE, today)
            .apply()
    }
    
    /**
     * 保存剩余次数
     */
    private fun saveDailyChances(chances: Int) {
        val today = dateFormat.format(Date())
        prefs.edit()
            .putInt(KEY_DAILY_CHANCES, chances)
            .putString(KEY_LAST_DATE, today)
            .apply()
    }
    
    /**
     * 检查是否可以游戏
     */
    fun canPlay(): Boolean {
        return getDailyChances() > 0
    }
    
    /**
     * 获取刮刮乐游戏已使用次数
     */
    fun getScratchGameUsedChances(): Int {
        val today = dateFormat.format(Date())
        val lastDate = prefs.getString(KEY_SCRATCH_LAST_DATE, "")
        
        return if (today == lastDate) {
            // 同一天，返回已使用次数
            prefs.getInt(KEY_SCRATCH_USED_CHANCES, 0)
        } else {
            // 新的一天，重置次数
            resetScratchGameChances()
            0
        }
    }
    
    /**
     * 增加刮刮乐游戏使用次数
     */
    fun incrementScratchGameUsedChances() {
        val today = dateFormat.format(Date())
        val currentUsed = getScratchGameUsedChances()
        val newUsed = (currentUsed + 1).coerceAtMost(DEFAULT_SCRATCH_DAILY_CHANCES)
        
        prefs.edit()
            .putInt(KEY_SCRATCH_USED_CHANCES, newUsed)
            .putString(KEY_SCRATCH_LAST_DATE, today)
            .apply()
    }
    
    /**
     * 重置刮刮乐游戏次数
     */
    private fun resetScratchGameChances() {
        val today = dateFormat.format(Date())
        prefs.edit()
            .putInt(KEY_SCRATCH_USED_CHANCES, 0)
            .putString(KEY_SCRATCH_LAST_DATE, today)
            .apply()
    }
    
    /**
     * 检查刮刮乐游戏是否可以继续
     */
    fun canPlayScratchGame(): Boolean {
        return getScratchGameUsedChances() < DEFAULT_SCRATCH_DAILY_CHANCES
    }
}
