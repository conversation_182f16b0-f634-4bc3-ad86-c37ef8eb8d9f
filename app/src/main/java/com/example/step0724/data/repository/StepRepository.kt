package com.example.step0724.data.repository

import com.example.step0724.data.model.StepRecord
import kotlinx.coroutines.flow.Flow

/**
 * 步数数据仓库接口 - 基于需求7的数据存储要求
 */
interface StepRepository {
    
    /**
     * 获取当前日期的实时步数 - 需求2：获取当前日期的实时步数
     */
    suspend fun getTodaySteps(): Flow<Int>
    
    /**
     * 获取指定日期的步数记录
     */
    suspend fun getStepRecord(date: String): StepRecord?
    
    /**
     * 保存步数记录
     */
    suspend fun saveStepRecord(record: StepRecord)
    
    /**
     * 获取指定日期的小时数据 - 需求3：支持按天、周、月的数据查询
     */
    suspend fun getHourlyData(date: String): List<Int>
    
    /**
     * 获取周数据 - 需求3：周日到周一
     */
    suspend fun getWeeklyData(startDate: String): List<StepRecord>
    
    /**
     * 获取月数据 - 需求3：1-31号数据
     */
    suspend fun getMonthlyData(year: Int, month: Int): List<StepRecord>
    
    /**
     * 更新今日步数 - 需求8.4：前台服务实时更新步数
     */
    suspend fun updateTodaySteps(steps: Int)
    
    /**
     * 清理旧数据
     */
    suspend fun cleanOldData(cutoffDate: String)
    
    /**
     * 保存完整的步数记录，包含计算好的统计数据
     * 用于保存包含距离、卡路里、时间等计算结果的完整记录
     */
    suspend fun saveCompleteStepRecord(
        date: String,
        steps: Int,
        distance: Double,
        calories: Double,
        walkingTime: Long,
        hourlyData: List<Int>
    )
}