package com.example.step0724.data.permission

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.app.NotificationManagerCompat
import com.tencent.mmkv.MMKV
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        const val ACTIVITY_RECOGNITION_REQUEST_CODE = 1001
        const val NOTIFICATION_REQUEST_CODE = 1002
        private const val PREFS_NAME = "permission_prefs"
        private const val KEY_ACTIVITY_RECOGNITION_DENIED = "activity_recognition_denied"
        private const val KEY_NOTIFICATION_DENIED = "notification_denied"
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val mmkv = MMKV.defaultMMKV()
    
    /**
     * 检查运动识别权限
     */
    fun hasActivityRecognitionPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 10以下不需要此权限
        }
    }
    
    /**
     * 请求运动识别权限
     */
    fun requestActivityRecognitionPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.ACTIVITY_RECOGNITION),
                ACTIVITY_RECOGNITION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 智能请求运动识别权限
     * 如果可以直接弹出权限弹窗就直接弹出，否则跳转到设置页面
     */
    fun smartRequestActivityRecognitionPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (canRequestActivityRecognitionPermission(activity)) {
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.ACTIVITY_RECOGNITION),
                    ACTIVITY_RECOGNITION_REQUEST_CODE
                )
            } else {
                openActivityRecognitionSettings(activity)
            }
        }
    }
    
    /**
     * 检查是否可以直接请求运动识别权限
     */
    private fun canRequestActivityRecognitionPermission(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED
            
            if (hasPermission) return false
            
            val shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACTIVITY_RECOGNITION
            )
            
            val wasPreviouslyDenied = prefs.getBoolean(KEY_ACTIVITY_RECOGNITION_DENIED, false)
            
            if (shouldShowRationale) return true
            
            if (wasPreviouslyDenied && !shouldShowRationale) {
                return false
            }
            
            return true
        } else {
            false
        }
    }
    
    /**
     * 记录运动识别权限被拒绝
     */
    fun recordActivityRecognitionDenied() {
        prefs.edit().putBoolean(KEY_ACTIVITY_RECOGNITION_DENIED, true).apply()
    }
    
    /**
     * 打开运动识别权限设置页面
     */
    private fun openActivityRecognitionSettings(activity: Activity) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.parse("package:${context.packageName}")
        }
        try {
            activity.startActivity(intent)
        } catch (e: Exception) {
            val generalIntent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(generalIntent)
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowRationale(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACTIVITY_RECOGNITION
            )
        } else {
            false
        }
    }
    
    /**
     * 检查电池优化权限（是否在白名单中）
     */
    fun hasBatteryOptimizationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true
        }
    }
    
    /**
     * 请求电池优化权限
     */
    fun requestBatteryOptimizationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:${context.packageName}")
            }
            try {
                activity.startActivity(intent)
            } catch (e: Exception) {
                val generalIntent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                activity.startActivity(generalIntent)
            }
        }
    }
    
    /**
     * 检查通知权限
     */
    fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }
    
    /**
     * 检查通知功能是否启用（系统权限 + 用户设置）
     */
    fun isNotificationEnabled(): Boolean {
        return hasNotificationPermission() && getNotificationSetting()
    }
    
    /**
     * 获取通知设置状态
     */
    fun getNotificationSetting(): Boolean {
        return mmkv.decodeBool(KEY_NOTIFICATION_ENABLED, true)
    }
    
    /**
     * 设置通知开关状态
     */
    fun setNotificationEnabled(enabled: Boolean) {
        mmkv.encode(KEY_NOTIFICATION_ENABLED, enabled)
    }
    
    /**
     * 当获得通知权限后，自动启用通知设置
     */
    fun onNotificationPermissionGranted() {
        setNotificationEnabled(true)
    }
    
    /**
     * 请求通知权限
     */
    fun requestNotificationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                NOTIFICATION_REQUEST_CODE
            )
        } else {
            openNotificationSettings(activity)
        }
    }
    
    /**
     * 智能请求通知权限
     * 如果可以直接弹出权限弹窗就直接弹出，否则跳转到设置页面
     */
    fun smartRequestNotificationPermission(activity: Activity, launcher: androidx.activity.result.ActivityResultLauncher<String>? = null) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (canRequestNotificationPermission(activity)) {
                if (launcher != null) {
                    launcher.launch(Manifest.permission.POST_NOTIFICATIONS)
                } else {
                    ActivityCompat.requestPermissions(
                        activity,
                        arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                        NOTIFICATION_REQUEST_CODE
                    )
                }
            } else {
                openNotificationSettings(activity)
            }
        } else {
            openNotificationSettings(activity)
        }
    }
    
    /**
     * 检查是否可以直接请求通知权限
     */
    private fun canRequestNotificationPermission(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
            
            if (hasPermission) return false
            
            val shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            )
            
            val wasPreviouslyDenied = prefs.getBoolean(KEY_NOTIFICATION_DENIED, false)
            
            if (shouldShowRationale) return true
            
            if (wasPreviouslyDenied && !shouldShowRationale) {
                return false // 跳转到设置页面
            }
            
            return true
        } else {
            false
        }
    }
    
    /**
     * 记录通知权限被拒绝
     */
    fun recordNotificationDenied() {
        prefs.edit().putBoolean(KEY_NOTIFICATION_DENIED, true).apply()
    }
    
    /**
     * 打开通知设置页面
     */
    fun openNotificationSettings(activity: Activity) {
        val intent = Intent().apply {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                    putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                }
                else -> {
                    action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    data = Uri.parse("package:${context.packageName}")
                }
            }
        }
        try {
            activity.startActivity(intent)
        } catch (e: Exception) {
            val generalIntent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(generalIntent)
        }
    }
}