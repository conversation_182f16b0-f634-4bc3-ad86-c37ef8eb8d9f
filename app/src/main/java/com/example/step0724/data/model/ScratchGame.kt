package com.example.step0724.data.model

/**
 * 水果类型枚举
 */
enum class FruitType(val resourceId: Int) {
    WATERMELON(com.example.step0724.R.mipmap.sc_fruit_1),     // 西瓜
    GRAPE(com.example.step0724.R.mipmap.sc_fruit_2),          // 葡萄
    ORANGE(com.example.step0724.R.mipmap.sc_fruit_3),         // 橙子
    AVOCADO(com.example.step0724.R.mipmap.sc_fruit_4),        // 牛油果
    PEAR(com.example.step0724.R.mipmap.sc_fruit_5),           // 梨子
    CHERRY(com.example.step0724.R.mipmap.sc_fruit_6)          // 樱桃
}

/**
 * 刮刮卡数据模型
 */
data class ScratchCard(
    val id: Int,
    val fruitType: FruitType,
    val isScratched: Boolean = false,
    val scratchedPercentage: Float = 0f
)

/**
 * 刮刮乐奖励类型
 */
enum class ScratchRewardType {
    JACKPOT,        // 顶格奖励（全部图案相同）
    STANDARD        // 标准奖励（图案不同）
}

/**
 * 刮刮乐游戏结果
 */
data class ScratchGameResult(
    val rewardType: ScratchRewardType,
    val coinReward: Int,
    val matchingFruit: FruitType? = null  // 如果是顶格奖励，记录匹配的水果类型
)

/**
 * 刮刮乐游戏状态
 */
data class ScratchGameState(
    val cards: List<ScratchCard> = emptyList(),
    val isGameComplete: Boolean = false,
    val canScratch: Boolean = true,
    val dailyFreeChances: Int = 3,  // 每日免费次数
    val usedChances: Int = 0,
    val gameResult: ScratchGameResult? = null,
    val isLoading: Boolean = false
)