package com.example.step0724.data.model

/**
 * 任务类型枚举
 * 定义Earn功能中支持的所有任务类型
 */
enum class TaskType {
    /**
     * 每日签到任务
     */
    DAILY_CHECK_IN,
    
    /**
     * 转盘游戏任务
     */
    LUCKY_WHEEL,
    
    /**
     * 1000步任务
     */
    STEPS_1000,
    
    /**
     * 3000步任务
     */
    STEPS_3000,
    
    /**
     * 连续签到2天任务
     */
    CHECK_IN_2_DAYS,
    
    /**
     * 连续签到3天任务
     */
    CHECK_IN_3_DAYS
}