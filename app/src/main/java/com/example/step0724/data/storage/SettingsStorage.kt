package com.example.step0724.data.storage

import com.example.step0724.data.model.Gender
import com.example.step0724.data.model.SensitivityLevel
import com.example.step0724.data.model.UnitSystem
import com.example.step0724.data.model.UserSettings
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsStorage @Inject constructor() {

    private val mmkv = MMKV.defaultMMKV()
    
    private val _userSettings = MutableStateFlow(loadUserSettings())
    
    companion object {
        private const val KEY_STEP_GOAL = "step_goal"
        private const val KEY_SENSITIVITY = "sensitivity"
        private const val KEY_GENDER = "gender"
        private const val KEY_WEIGHT = "weight"
        private const val KEY_HEIGHT = "height"
        private const val KEY_STEP_LENGTH = "step_length"
        private const val KEY_UNIT_SYSTEM = "unit_system"
        private const val KEY_LAST_SENSOR_VALUE = "last_sensor_value"
        private const val KEY_FIRST_LAUNCH = "first_launch"
        
        // 任务16：需求4.2-4.10 - 默认值定义
        private const val DEFAULT_STEP_GOAL = 6000 // 需求4.2：默认6000步
        private const val DEFAULT_WEIGHT_KG = 57.0 // 需求4.6：默认57.0kg
        private const val DEFAULT_HEIGHT_CM = 172.0 // 需求4.7：默认172cm
        private const val DEFAULT_STEP_LENGTH_CM = 71.0 // 需求4.8：默认71cm
    }
    
    init {
        // 任务16：实现默认值的初始化
        initializeDefaultValues()
    }

    fun getUserSettings(): Flow<UserSettings> = _userSettings.asStateFlow()

    /**
     * 任务16：实现默认值的初始化
     * 需求4.2-4.10：各项设置的默认值
     */
    private fun initializeDefaultValues() {
        val isFirstLaunch = mmkv.getBoolean(KEY_FIRST_LAUNCH, true)
        if (isFirstLaunch) {
            // 需求4.9：首次使用时，使用默认步长公式：步长(cm) = 身高(cm) × 0.415
            val defaultStepLength = DEFAULT_HEIGHT_CM * 0.415
            
            // 设置所有默认值
            mmkv.putInt(KEY_STEP_GOAL, DEFAULT_STEP_GOAL)
            mmkv.putString(KEY_SENSITIVITY, SensitivityLevel.LOW.name)
            mmkv.putString(KEY_GENDER, Gender.OTHERS.name)
            mmkv.putFloat(KEY_WEIGHT, DEFAULT_WEIGHT_KG.toFloat())
            mmkv.putFloat(KEY_HEIGHT, DEFAULT_HEIGHT_CM.toFloat())
            mmkv.putFloat(KEY_STEP_LENGTH, defaultStepLength.toFloat())
            mmkv.putString(KEY_UNIT_SYSTEM, UnitSystem.METRIC.name)
            
            // 标记已完成初始化
            mmkv.putBoolean(KEY_FIRST_LAUNCH, false)
        }
    }
    
    private fun loadUserSettings(): UserSettings {
        return UserSettings(
            stepGoal = mmkv.getInt(KEY_STEP_GOAL, DEFAULT_STEP_GOAL),
            sensitivity = SensitivityLevel.valueOf(
                mmkv.getString(KEY_SENSITIVITY, SensitivityLevel.LOW.name) ?: SensitivityLevel.LOW.name
            ),
            gender = Gender.valueOf(
                mmkv.getString(KEY_GENDER, Gender.OTHERS.name) ?: Gender.OTHERS.name
            ),
            weight = mmkv.getFloat(KEY_WEIGHT, DEFAULT_WEIGHT_KG.toFloat()).toDouble(),
            height = mmkv.getFloat(KEY_HEIGHT, DEFAULT_HEIGHT_CM.toFloat()).toDouble(),
            stepLength = mmkv.getFloat(KEY_STEP_LENGTH, DEFAULT_STEP_LENGTH_CM.toFloat()).toDouble(),
            unitSystem = UnitSystem.valueOf(
                mmkv.getString(KEY_UNIT_SYSTEM, UnitSystem.METRIC.name) ?: UnitSystem.METRIC.name
            )
        )
    }

    /**
     * 任务16：实现设置变更的实时保存
     * 需求7.2：修改设置时立即保存设置信息
     */
    suspend fun updateStepGoal(goal: Int) {
        mmkv.putInt(KEY_STEP_GOAL, goal)
        _userSettings.value = _userSettings.value.copy(stepGoal = goal)
    }

    suspend fun updateSensitivity(level: SensitivityLevel) {
        mmkv.putString(KEY_SENSITIVITY, level.name)
        _userSettings.value = _userSettings.value.copy(sensitivity = level)
    }

    suspend fun updateGender(gender: Gender) {
        mmkv.putString(KEY_GENDER, gender.name)
        _userSettings.value = _userSettings.value.copy(gender = gender)
    }

    suspend fun updateWeight(weight: Double) {
        mmkv.putFloat(KEY_WEIGHT, weight.toFloat())
        _userSettings.value = _userSettings.value.copy(weight = weight)
    }

    suspend fun updateHeight(height: Double) {
        mmkv.putFloat(KEY_HEIGHT, height.toFloat())
        _userSettings.value = _userSettings.value.copy(height = height)
    }

    suspend fun updateStepLength(length: Double) {
        mmkv.putFloat(KEY_STEP_LENGTH, length.toFloat())
        _userSettings.value = _userSettings.value.copy(stepLength = length)
    }

    suspend fun updateUnitSystem(system: UnitSystem) {
        mmkv.putString(KEY_UNIT_SYSTEM, system.name)
        _userSettings.value = _userSettings.value.copy(unitSystem = system)
    }

    suspend fun getLastSensorValue(): Long {
        return mmkv.getLong(KEY_LAST_SENSOR_VALUE, 0L)
    }

    suspend fun saveLastSensorValue(value: Long) {
        mmkv.putLong(KEY_LAST_SENSOR_VALUE, value)
    }
}