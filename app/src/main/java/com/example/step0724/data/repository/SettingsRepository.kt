package com.example.step0724.data.repository

import com.example.step0724.data.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 设置数据仓库接口 - 基于需求7的数据存储要求
 */
interface SettingsRepository {
    
    /**
     * 获取用户设置 - 需求4：用户设置的获取和更新
     */
    suspend fun getUserSettings(): Flow<UserSettings>
    
    /**
     * 更新步数目标 - 需求4：步数目标设置
     */
    suspend fun updateStepGoal(goal: Int)
    
    /**
     * 更新灵敏度 - 需求4：灵敏度设置
     */
    suspend fun updateSensitivity(level: SensitivityLevel)
    
    /**
     * 更新性别 - 需求4：性别设置
     */
    suspend fun updateGender(gender: Gender)
    
    /**
     * 更新体重 - 需求4：体重设置
     */
    suspend fun updateWeight(weight: Double)
    
    /**
     * 更新身高 - 需求4：身高设置
     */
    suspend fun updateHeight(height: Double)
    
    /**
     * 更新步长 - 需求4：步长设置
     */
    suspend fun updateStepLength(length: Double)
    
    /**
     * 更新单位制式 - 需求4：单位制式设置
     */
    suspend fun updateUnitSystem(system: UnitSystem)
    
    /**
     * 获取上次传感器值 - 需求7：传感器状态保存（用于设备重启后恢复）
     */
    suspend fun getLastSensorValue(): Long
    
    /**
     * 保存传感器值 - 需求7：传感器状态保存
     */
    suspend fun saveLastSensorValue(value: Long)
}