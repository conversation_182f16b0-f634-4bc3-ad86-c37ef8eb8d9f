package com.example.step0724.data.service

import android.util.Log
import com.example.step0724.data.model.SensitivityLevel
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.sensor.StepDetector
import com.example.step0724.data.sensor.StepDetectorFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class StepCountingServiceImpl @Inject constructor(
    private val stepDetectorFactory: StepDetectorFactory,
    private val stepRepository: StepRepository
) : StepCountingService {

    private var stepDetector: StepDetector? = null
    private var isRunning = false
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    override fun startCounting() {
        if (!isRunning) {
            try {
                Log.d("StepCountingService", "Starting step counting service...")
                stepDetector = stepDetectorFactory.createDetector()
                stepDetector?.startDetection()
                
                // 连接传感器数据流到Repository - 需求9：连接传感器数据流到UI
                stepDetector?.getStepCount()
                    ?.onEach { steps ->
                        // 实时更新Repository中的步数数据
                        stepRepository.updateTodaySteps(steps)
                        Log.d("StepCountingService", "Updated today steps: $steps")
                    }
                    ?.launchIn(serviceScope)
                
                isRunning = true
                Log.d("StepCountingService", "Step counting service started successfully")
            } catch (e: Exception) {
                Log.e("StepCountingService", "Failed to start step counting service", e)
                isRunning = false
            }
        } else {
            Log.d("StepCountingService", "Step counting service is already running")
        }
    }

    override fun stopCounting() {
        if (isRunning) {
            Log.d("StepCountingService", "Stopping step counting service...")
            stepDetector?.stopDetection()
            isRunning = false
            Log.d("StepCountingService", "Step counting service stopped")
        }
    }

    override fun getCurrentSteps(): Flow<Int> {
        return stepDetector?.getStepCount() ?: kotlinx.coroutines.flow.flowOf(0)
    }

    override fun resetDailySteps() {
        when (val detector = stepDetector) {
            is com.example.step0724.data.sensor.StepCounterSensorDetector -> detector.resetStepCount()
            is com.example.step0724.data.sensor.AccelerometerStepDetector -> detector.resetStepCount()
        }
    }

    override fun isRunning(): Boolean = isRunning

    override fun updateSensitivity(level: SensitivityLevel) {
        stepDetector?.setSensitivity(level)
        Log.d("StepCountingService", "Updated sensitivity to: $level")
    }

    fun getSensorType(): String {
        return stepDetectorFactory.getSensorType()
    }
}