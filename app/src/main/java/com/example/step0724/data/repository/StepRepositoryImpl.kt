package com.example.step0724.data.repository

import com.example.step0724.data.database.StepRecordDao
import com.example.step0724.data.model.StepRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import org.json.JSONArray
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 步数数据仓库实现 - 基于需求7的Room数据库存储
 */
@Singleton
class StepRepositoryImpl @Inject constructor(
    private val stepRecordDao: StepRecordDao
) : StepRepository {
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    // 需求2：实时步数数据流，用于UI实时更新
    private val _todaySteps = MutableStateFlow(0)
    private var lastUpdateDate = ""
    
    // 存储当前小时数据，用于实时更新 - 记录每小时的实际步数
    private val currentHourlySteps = MutableList(24) { 0 }
    private var lastRecordedSteps = 0
    private var lastRecordedHour = -1
    
    override suspend fun getTodaySteps(): Flow<Int> {
        val today = dateFormat.format(Date())
        
        // 检查是否是新的一天，如果是则重置步数
        if (lastUpdateDate != today) {
            _todaySteps.value = 0
            lastUpdateDate = today
            
            // 重置小时数据
            currentHourlySteps.fill(0)
            lastRecordedSteps = 0
            lastRecordedHour = -1
            
            // 从数据库加载今日已有的步数和小时数据（如果存在）
            val existingRecord = stepRecordDao.getStepRecord(today)
            if (existingRecord != null) {
                _todaySteps.value = existingRecord.steps
                lastRecordedSteps = existingRecord.steps
                
                // 加载小时数据
                try {
                    if (existingRecord.hourlyData.isNotEmpty()) {
                        val jsonArray = JSONArray(existingRecord.hourlyData)
                        for (i in 0 until minOf(24, jsonArray.length())) {
                            currentHourlySteps[i] = jsonArray.getInt(i)
                        }
                    }
                } catch (e: Exception) {
                    // 如果加载失败，保持默认值
                }
            }
        }
        
        // 需求2：返回实时步数流，确保UI能够实时更新
        return _todaySteps.asStateFlow()
    }
    
    override suspend fun getStepRecord(date: String): StepRecord? {
        return stepRecordDao.getStepRecord(date)
    }
    
    override suspend fun saveStepRecord(record: StepRecord) {
        stepRecordDao.insertOrUpdateStepRecord(record)
    }
    
    /**
     * 需求3.3：按小时查询(0-24时累计数据)
     * 获取指定日期的小时数据，如果是今天则返回实时数据
     */
    override suspend fun getHourlyData(date: String): List<Int> {
        val today = dateFormat.format(Date())
        
        // 如果查询的是今天的数据，返回实时的小时数据
        if (date == today) {
            updateCurrentHourlyData()
            return getCurrentHourlyCumulativeData()
        }
        
        // 查询历史数据
        val record = stepRecordDao.getStepRecord(date)
        return if (record != null) {
            try {
                val jsonArray = JSONArray(record.hourlyData)
                val hourlyStepsData = mutableListOf<Int>()
                
                // 读取每小时的实际步数
                for (i in 0 until 24) {
                    if (i < jsonArray.length()) {
                        hourlyStepsData.add(jsonArray.getInt(i))
                    } else {
                        hourlyStepsData.add(0)
                    }
                }
                
                // 转换为累计数据用于图表显示
                // 对于历史数据，显示完整的累计数据
                val cumulativeData = mutableListOf<Int>()
                var cumulative = 0
                for (i in 0 until 24) {
                    cumulative += hourlyStepsData[i]
                    cumulativeData.add(cumulative)
                }
                
                cumulativeData
            } catch (e: Exception) {
                // 如果JSON解析失败，返回24个0
                List(24) { 0 }
            }
        } else {
            List(24) { 0 }
        }
    }
    
    /**
     * 更新当前小时数据 - 真正按小时记录步数增量
     * 记录每小时的实际步数变化，而不是简单的平均分配
     */
    private fun updateCurrentHourlyData() {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        val currentSteps = _todaySteps.value
        
        // 计算步数增量
        val stepIncrement = currentSteps - lastRecordedSteps
        
        if (stepIncrement > 0) {
            // 如果小时发生变化，需要处理跨小时的步数分配
            if (lastRecordedHour != -1 && lastRecordedHour != currentHour) {
                // 跨小时了，将增量分配给当前小时
                currentHourlySteps[currentHour] += stepIncrement
            } else {
                // 同一小时内，直接累加到当前小时
                currentHourlySteps[currentHour] += stepIncrement
            }
        }
        
        // 更新记录
        lastRecordedSteps = currentSteps
        lastRecordedHour = currentHour
    }
    
    /**
     * 获取当前小时数据的累计值列表
     * 返回每小时的累计步数（0点到当前时间的累计）
     * 未来的小时显示0
     */
    private fun getCurrentHourlyCumulativeData(): List<Int> {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        val cumulativeData = MutableList(24) { 0 }
        var cumulative = 0
        
        // 只计算到当前小时的累计数据
        for (i in 0..currentHour) {
            cumulative += currentHourlySteps[i]
            cumulativeData[i] = cumulative
        }
        
        // 未来的小时保持为0（已经在初始化时设置）
        // for (i in (currentHour + 1) until 24) {
        //     cumulativeData[i] = 0  // 已经是0了
        // }
        
        return cumulativeData
    }
    
    override suspend fun getWeeklyData(startDate: String): List<StepRecord> {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        calendar.time = dateFormat.parse(startDate) ?: Date()
        
        val endCalendar = calendar.clone() as Calendar
        endCalendar.add(Calendar.DAY_OF_YEAR, 6)
        
        val endDate = dateFormat.format(endCalendar.time)
        return stepRecordDao.getStepRecordsInRange(startDate, endDate)
    }
    
    override suspend fun getMonthlyData(year: Int, month: Int): List<StepRecord> {
        val yearMonth = String.format("%04d-%02d", year, month)
        return stepRecordDao.getMonthlyRecords(yearMonth)
    }
    
    override suspend fun updateTodaySteps(steps: Int) {
        val today = dateFormat.format(Date())
        
        // 需求2：实时更新步数流，确保UI能够立即响应
        _todaySteps.value = steps
        lastUpdateDate = today
        
        // 更新当前小时数据
        updateCurrentHourlyData()
        
        // 同时更新数据库记录，包含小时数据
        val existingRecord = stepRecordDao.getStepRecord(today)
        val hourlyDataJson = JSONArray(currentHourlySteps).toString()
        
        if (existingRecord != null) {
            // 更新现有记录
            val updatedRecord = existingRecord.copy(
                steps = steps,
                hourlyData = hourlyDataJson
            )
            stepRecordDao.insertOrUpdateStepRecord(updatedRecord)
        } else {
            // 创建新记录
            val newRecord = StepRecord(
                date = today,
                steps = steps,
                distance = 0.0, // 将在UI层计算
                calories = 0.0, // 将在UI层计算
                walkingTime = 0L, // 将在UI层计算
                hourlyData = hourlyDataJson
            )
            stepRecordDao.insertOrUpdateStepRecord(newRecord)
        }
    }
    
    override suspend fun cleanOldData(cutoffDate: String) {
        stepRecordDao.deleteOldRecords(cutoffDate)
    }
    
    override suspend fun saveCompleteStepRecord(
        date: String,
        steps: Int,
        distance: Double,
        calories: Double,
        walkingTime: Long,
        hourlyData: List<Int>
    ) {
        val hourlyDataJson = JSONArray(hourlyData).toString()
        val record = StepRecord(
            date = date,
            steps = steps,
            distance = distance,
            calories = calories,
            walkingTime = walkingTime,
            hourlyData = hourlyDataJson
        )
        stepRecordDao.insertOrUpdateStepRecord(record)
    }
}