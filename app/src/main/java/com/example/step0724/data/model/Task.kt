package com.example.step0724.data.model

import androidx.annotation.DrawableRes

/**
 * 任务数据模型
 * 定义Earn功能中任务的完整结构
 */
data class Task(
    /**
     * 任务类型
     */
    val type: TaskType,
    
    /**
     * 任务标题
     */
    val title: String,
    
    /**
     * 任务描述（可选）
     */
    val description: String = "",
    
    /**
     * 奖励金币数量
     */
    val reward: Int,
    
    /**
     * 任务状态
     */
    val status: TaskStatus,
    
    /**
     * 当前进度
     */
    val progress: Int = 0,
    
    /**
     * 目标值
     */
    val target: Int = 1,
    
    /**
     * 任务图标资源ID
     */
    @DrawableRes
    val iconRes: Int
)