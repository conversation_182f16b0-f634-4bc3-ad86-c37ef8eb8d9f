package com.example.step0724.data.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.example.step0724.MainActivity
import com.example.step0724.R
import com.example.step0724.data.sensor.StepDetector
import com.example.step0724.data.sensor.StepDetectorFactory
import com.example.step0724.data.repository.StepRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 前台服务用于后台计步 - 需求8.4：当用户在后台运行应用时，系统应通过前台服务继续进行步数统计
 */
@AndroidEntryPoint
class StepCountingForegroundService : Service() {

    @Inject
    lateinit var stepDetectorFactory: StepDetectorFactory
    
    @Inject
    lateinit var stepRepository: StepRepository

    private var stepDetector: StepDetector? = null
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private var stepCountJob: Job? = null
    
    private var currentSteps = 0
    private var isServiceRunning = false

    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "step_counting_channel"
        const val ACTION_START_SERVICE = "START_SERVICE"
        const val ACTION_STOP_SERVICE = "STOP_SERVICE"
        
        fun startService(context: Context) {
            val intent = Intent(context, StepCountingForegroundService::class.java).apply {
                action = ACTION_START_SERVICE
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, StepCountingForegroundService::class.java).apply {
                action = ACTION_STOP_SERVICE
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_SERVICE -> {
                if (!isServiceRunning) {
                    startStepCounting()
                }
            }
            ACTION_STOP_SERVICE -> {
                stopStepCounting()
                stopSelf()
            }
        }
        return START_STICKY // 服务被系统杀死后会自动重启
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        stopStepCounting()
        serviceScope.cancel()
    }

    /**
     * 开始步数统计
     */
    private fun startStepCounting() {
        if (isServiceRunning) return
        
        try {
            // 创建步数检测器
            stepDetector = stepDetectorFactory.createDetector()
            
            // 检查传感器是否支持
            if (stepDetector?.isSupported() != true) {
                stopSelf()
                return
            }
            
            // 启动前台服务
            startForeground(NOTIFICATION_ID, createNotification())
            
            // 开始检测步数
            stepDetector?.startDetection()
            
            // 监听步数变化
            stepCountJob = stepDetector?.getStepCount()
                ?.onEach { steps ->
                    currentSteps = steps
                    updateNotification()
                    saveStepData(steps)
                }
                ?.launchIn(serviceScope)
            
            isServiceRunning = true
            
        } catch (e: Exception) {
            // 处理启动异常
            stopSelf()
        }
    }

    /**
     * 停止步数统计
     */
    private fun stopStepCounting() {
        if (!isServiceRunning) return
        
        stepCountJob?.cancel()
        stepDetector?.stopDetection()
        stepDetector = null
        isServiceRunning = false
    }

    /**
     * 保存步数数据到数据库
     */
    private fun saveStepData(steps: Int) {
        serviceScope.launch {
            try {
                stepRepository.updateTodaySteps(steps)
            } catch (e: Exception) {
                // 记录错误但不影响服务运行
            }
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "步数统计服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "后台计步服务通知"
                setShowBadge(false)
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("计步服务运行中")
            .setContentText("今日步数: $currentSteps")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()
    }

    /**
     * 更新通知内容
     */
    private fun updateNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification())
    }
}