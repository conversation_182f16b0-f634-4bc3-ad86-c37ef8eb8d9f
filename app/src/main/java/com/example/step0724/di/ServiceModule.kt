package com.example.step0724.di

import android.content.Context
import com.example.step0724.data.service.StepCountingService
import com.example.step0724.data.service.StepCountingServiceImpl
import com.example.step0724.data.service.StepCountingServiceManager
import com.example.step0724.data.storage.StepCounterStateManager
import com.example.step0724.data.storage.GameStorage
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class ServiceModule {

    @Binds
    @Singleton
    abstract fun bindStepCountingService(
        stepCountingServiceImpl: StepCountingServiceImpl
    ): StepCountingService

    companion object {
        @Provides
        @Singleton
        fun provideStepCounterStateManager(): StepCounterStateManager {
            return StepCounterStateManager()
        }

        @Provides
        @Singleton
        fun provideStepCountingServiceManager(
            @ApplicationContext context: Context
        ): StepCountingServiceManager {
            return StepCountingServiceManager(context)
        }
        
        @Provides
        @Singleton
        fun provideGameStorage(@ApplicationContext context: Context): GameStorage {
            return GameStorage(context)
        }
    }
}