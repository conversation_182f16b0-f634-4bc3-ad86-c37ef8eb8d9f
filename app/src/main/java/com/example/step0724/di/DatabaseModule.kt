package com.example.step0724.di

import android.content.Context
import androidx.room.Room
import com.example.step0724.data.database.StepCounterDatabase
import com.example.step0724.data.database.StepRecordDao
import com.example.step0724.data.repository.SettingsRepository
import com.example.step0724.data.repository.SettingsRepositoryImpl
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.repository.StepRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideStepCounterDatabase(@ApplicationContext context: Context): StepCounterDatabase {
        return Room.databaseBuilder(
            context,
            StepCounterDatabase::class.java,
            StepCounterDatabase.DATABASE_NAME
        ).build()
    }

    @Provides
    fun provideStepRecordDao(database: StepCounterDatabase): StepRecordDao {
        return database.stepRecordDao()
    }
}

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindStepRepository(
        stepRepositoryImpl: StepRepositoryImpl
    ): StepRepository

    @Binds
    @Singleton
    abstract fun bindSettingsRepository(
        settingsRepositoryImpl: SettingsRepositoryImpl
    ): SettingsRepository
}