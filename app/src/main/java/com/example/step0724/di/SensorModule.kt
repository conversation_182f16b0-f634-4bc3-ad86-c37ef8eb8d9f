package com.example.step0724.di

import android.content.Context
import android.hardware.SensorManager
import com.example.step0724.data.sensor.StepDetector
import com.example.step0724.data.sensor.StepDetectorFactory
import com.example.step0724.data.storage.StepCounterStateManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SensorModule {

    @Provides
    @Singleton
    fun provideSensorManager(@ApplicationContext context: Context): SensorManager {
        return context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    }

    @Provides
    @Singleton
    fun provideStepDetectorFactory(
        @ApplicationContext context: Context,
        stateManager: StepCounterStateManager
    ): StepDetectorFactory {
        return StepDetectorFactory(context, stateManager)
    }

    @Provides
    @Singleton
    fun provideStepDetector(factory: StepDetectorFactory): StepDetector {
        return factory.createDetector()
    }
}