package com.example.step0724.di

import com.example.step0724.domain.usecase.CalculateDefaultStepLengthUseCase
import com.example.step0724.domain.usecase.CalculateStatsUseCase
import com.example.step0724.domain.usecase.UnitConversionUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object UseCaseModule {

    @Provides
    @Singleton
    fun provideUnitConversionUseCase(): UnitConversionUseCase {
        return UnitConversionUseCase()
    }

    @Provides
    @Singleton
    fun provideCalculateDefaultStepLengthUseCase(): CalculateDefaultStepLengthUseCase {
        return CalculateDefaultStepLengthUseCase()
    }

    @Provides
    @Singleton
    fun provideCalculateStatsUseCase(
        unitConversionUseCase: UnitConversionUseCase
    ): CalculateStatsUseCase {
        return CalculateStatsUseCase(unitConversionUseCase)
    }
}