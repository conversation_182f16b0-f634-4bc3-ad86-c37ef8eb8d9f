package com.example.step0724.ui.components

import com.example.step0724.R
import com.example.step0724.data.model.Task
import com.example.step0724.data.model.TaskStatus
import com.example.step0724.data.model.TaskType
import org.junit.Test
import org.junit.Assert.*

/**
 * TaskItem组件的单元测试
 * 测试任务项组件的基本功能和状态处理
 */
class TaskItemTest {

    @Test
    fun task_creation_withAllProperties() {
        // Given
        val task = Task(
            type = TaskType.DAILY_CHECK_IN,
            title = "Daily check in",
            description = "Check in daily to earn coins",
            reward = 10,
            status = TaskStatus.NOT_COMPLETED,
            progress = 0,
            target = 1,
            iconRes = R.drawable.ic_check
        )

        // Then
        assertEquals(TaskType.DAILY_CHECK_IN, task.type)
        assertEquals("Daily check in", task.title)
        assertEquals("Check in daily to earn coins", task.description)
        assertEquals(10, task.reward)
        assertEquals(TaskStatus.NOT_COMPLETED, task.status)
        assertEquals(0, task.progress)
        assertEquals(1, task.target)
        assertEquals(R.drawable.ic_check, task.iconRes)
    }

    @Test
    fun task_progressCalculation_isCorrect() {
        // Given
        val task = Task(
            type = TaskType.LUCKY_WHEEL,
            title = "Lucky Wheel",
            description = "Play 10 times",
            reward = 30,
            status = TaskStatus.NOT_COMPLETED,
            progress = 3,
            target = 10,
            iconRes = R.drawable.ic_play
        )

        // When
        val progressPercentage = task.progress.toFloat() / task.target.toFloat()

        // Then
        assertEquals(0.3f, progressPercentage, 0.01f)
        assertTrue(task.target > 1) // Should show progress indicator
    }

    @Test
    fun task_statusTransitions_areValid() {
        // Test all possible task statuses
        val statuses = listOf(
            TaskStatus.NOT_COMPLETED,
            TaskStatus.COMPLETED,
            TaskStatus.CLAIMED
        )

        statuses.forEach { status ->
            val task = Task(
                type = TaskType.STEPS_1000,
                title = "1000 steps",
                description = "",
                reward = 5,
                status = status,
                progress = if (status == TaskStatus.NOT_COMPLETED) 500 else 1000,
                target = 1000,
                iconRes = R.drawable.ic_steps
            )

            // Verify status is set correctly
            assertEquals(status, task.status)
            
            // Verify progress makes sense for status
            when (status) {
                TaskStatus.NOT_COMPLETED -> assertTrue(task.progress < task.target)
                TaskStatus.COMPLETED, TaskStatus.CLAIMED -> assertTrue(task.progress >= task.target)
            }
        }
    }

    @Test
    fun task_rewardValues_arePositive() {
        val taskTypes = listOf(
            TaskType.DAILY_CHECK_IN to 10,
            TaskType.STEPS_1000 to 5,
            TaskType.STEPS_3000 to 15,
            TaskType.LUCKY_WHEEL to 30,
            TaskType.CHECK_IN_2_DAYS to 20,
            TaskType.CHECK_IN_3_DAYS to 50
        )

        taskTypes.forEach { (type, expectedReward) ->
            val task = Task(
                type = type,
                title = type.name,
                description = "",
                reward = expectedReward,
                status = TaskStatus.NOT_COMPLETED,
                progress = 0,
                target = 1,
                iconRes = R.drawable.ic_check
            )

            assertTrue("Reward should be positive for $type", task.reward > 0)
            assertEquals("Reward should match expected value for $type", expectedReward, task.reward)
        }
    }

    @Test
    fun task_withoutDescription_isValid() {
        // Given
        val task = Task(
            type = TaskType.DAILY_CHECK_IN,
            title = "Daily check in",
            description = "", // Empty description
            reward = 10,
            status = TaskStatus.NOT_COMPLETED,
            progress = 0,
            target = 1,
            iconRes = R.drawable.ic_check
        )

        // Then
        assertTrue(task.description.isEmpty())
        assertFalse(task.title.isEmpty()) // Title should not be empty
    }

    @Test
    fun task_progressTracking_worksCorrectly() {
        // Given - Task with progress tracking
        val progressTask = Task(
            type = TaskType.LUCKY_WHEEL,
            title = "Lucky Wheel",
            description = "Play 10 times",
            reward = 30,
            status = TaskStatus.NOT_COMPLETED,
            progress = 7,
            target = 10,
            iconRes = R.drawable.ic_play
        )

        // Given - Task without progress tracking
        val simpleTask = Task(
            type = TaskType.DAILY_CHECK_IN,
            title = "Daily check in",
            description = "",
            reward = 10,
            status = TaskStatus.NOT_COMPLETED,
            progress = 0,
            target = 1,
            iconRes = R.drawable.ic_check
        )

        // Then
        assertTrue("Progress task should show progress", progressTask.target > 1)
        assertFalse("Simple task should not show progress", simpleTask.target > 1)
        
        // Progress should be within valid range
        assertTrue(progressTask.progress >= 0)
        assertTrue(progressTask.progress <= progressTask.target)
    }
}