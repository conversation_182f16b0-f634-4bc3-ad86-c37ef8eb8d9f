package com.example.step0724.ui.earn

import com.example.step0724.data.repository.EarnRepository
import com.example.step0724.data.repository.StepRepository
import com.example.step0724.data.storage.TimeManager
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * EarnViewModel倒计时功能单元测试
 * 
 * 测试任务13的倒计时功能实现，包括：
 * - 倒计时协程的启动和停止
 * - 每分钟更新倒计时显示
 * - 午夜时间重置任务状态逻辑
 * - 页面进入时倒计时的准确显示
 * 
 * Requirements: 8.1, 8.2, 8.3, 8.4, 8.5
 */
@OptIn(ExperimentalCoroutinesApi::class)
class EarnViewModelCountdownTest {

    private lateinit var viewModel: EarnViewModel
    private lateinit var earnRepository: EarnRepository
    private lateinit var stepRepository: StepRepository
    private lateinit var timeManager: TimeManager
    private lateinit var testDispatcher: TestDispatcher

    @Before
    fun setUp() {
        testDispatcher = StandardTestDispatcher()
        Dispatchers.setMain(testDispatcher)
        
        earnRepository = mockk(relaxed = true)
        stepRepository = mockk(relaxed = true)
        timeManager = mockk(relaxed = true)
        
        // 设置默认的mock返回值
        every { timeManager.getCurrentDateString() } returns "2024-01-15"
        every { timeManager.getTimeUntilMidnight() } returns "12:30"
        every { timeManager.isNewDay(any()) } returns false
        every { timeManager.isMidnightPassed() } returns false
        
        viewModel = EarnViewModel(earnRepository, stepRepository, timeManager)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    /**
     * 测试倒计时定时器的启动
     * 
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     */
    @Test
    fun `startCountdownTimer should start countdown job`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } returns "23:45"
        
        // When
        viewModel.restartCountdownTimer()
        
        // Then
        assertTrue("倒计时定时器应该正在运行", viewModel.isCountdownTimerRunning())
        
        // 验证TimeManager被调用
        verify { timeManager.getTimeUntilMidnight() }
    }

    /**
     * 测试倒计时定时器的停止
     */
    @Test
    fun `stopCountdownTimer should stop countdown job`() = runTest {
        // Given
        viewModel.restartCountdownTimer()
        assertTrue("倒计时定时器应该正在运行", viewModel.isCountdownTimerRunning())
        
        // When
        viewModel.stopCountdownTimer()
        
        // Then
        assertFalse("倒计时定时器应该已停止", viewModel.isCountdownTimerRunning())
    }

    /**
     * 测试页面进入时的倒计时初始化
     * 
     * Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
     */
    @Test
    fun `onPageEntered should update countdown and restart timer`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } returns "15:30"
        
        // When
        viewModel.onPageEntered()
        advanceUntilIdle()
        
        // Then
        assertTrue("倒计时定时器应该正在运行", viewModel.isCountdownTimerRunning())
        verify { timeManager.getTimeUntilMidnight() }
        
        // 验证UI状态更新
        val currentState = viewModel.state.value
        assertEquals("倒计时显示应该更新", "15:30", currentState.timeUntilReset)
    }

    /**
     * 测试页面离开时的资源清理
     */
    @Test
    fun `onPageLeft should stop countdown timer`() = runTest {
        // Given
        viewModel.restartCountdownTimer()
        assertTrue("倒计时定时器应该正在运行", viewModel.isCountdownTimerRunning())
        
        // When
        viewModel.onPageLeft()
        
        // Then
        assertFalse("倒计时定时器应该已停止", viewModel.isCountdownTimerRunning())
    }

    /**
     * 测试新一天的任务重置逻辑
     * 
     * Requirements: 8.3 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     * Requirements: 8.4 - 当倒计时到达00:00时，系统应更新倒计时为下一天的24小时
     */
    @Test
    fun `updateCountdown should reset tasks when new day starts`() = runTest {
        // Given
        every { timeManager.isNewDay(any()) } returns true
        every { timeManager.getCurrentDateString() } returns "2024-01-16"
        every { timeManager.getTimeUntilMidnight() } returns "24:00"
        coEvery { earnRepository.resetDailyTasks() } returns Result.success(Unit)
        
        // When
        viewModel.sendIntent(EarnContract.Intent.UpdateCountdown)
        advanceUntilIdle()
        
        // Then
        coVerify { earnRepository.resetDailyTasks() }
        verify { timeManager.isNewDay(any()) }
    }

    /**
     * 测试倒计时到达00:00时的处理
     * 
     * Requirements: 8.3 - 当倒计时到达00:00时，系统应重置所有每日任务状态
     */
    @Test
    fun `updateCountdown should handle midnight reset`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } returns "00:00"
        every { timeManager.isNewDay(any()) } returns true
        every { timeManager.getCurrentDateString() } returns "2024-01-16"
        coEvery { earnRepository.resetDailyTasks() } returns Result.success(Unit)
        
        // When
        viewModel.sendIntent(EarnContract.Intent.UpdateCountdown)
        advanceUntilIdle()
        
        // Then
        coVerify { earnRepository.resetDailyTasks() }
        
        // 验证UI状态更新
        val currentState = viewModel.state.value
        assertEquals("倒计时应该显示00:00", "00:00", currentState.timeUntilReset)
    }

    /**
     * 测试倒计时提醒功能
     * 
     * Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
     */
    @Test
    fun `updateCountdown should show reminder at specific times`() = runTest {
        // Given - 测试1分钟提醒
        every { timeManager.getTimeUntilMidnight() } returns "00:01"
        every { timeManager.isNewDay(any()) } returns false
        
        // When
        viewModel.sendIntent(EarnContract.Intent.UpdateCountdown)
        advanceUntilIdle()
        
        // Then
        val currentState = viewModel.state.value
        assertEquals("倒计时应该显示00:01", "00:01", currentState.timeUntilReset)
    }

    /**
     * 测试手动更新倒计时功能
     */
    @Test
    fun `manualUpdateCountdown should update countdown immediately`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } returns "10:45"
        
        // When
        viewModel.manualUpdateCountdown()
        advanceUntilIdle()
        
        // Then
        verify { timeManager.getTimeUntilMidnight() }
        
        val currentState = viewModel.state.value
        assertEquals("倒计时应该立即更新", "10:45", currentState.timeUntilReset)
    }

    /**
     * 测试倒计时状态信息获取
     */
    @Test
    fun `getCountdownStatus should return correct status information`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } returns "08:30"
        viewModel.restartCountdownTimer()
        
        // When
        val status = viewModel.getCountdownStatus()
        
        // Then
        assertTrue("状态信息应该包含运行状态", status.contains("运行中"))
        assertTrue("状态信息应该包含当前倒计时", status.contains("08:30"))
        assertTrue("状态信息应该包含上次更新日期", status.contains("2024-01-15"))
    }

    /**
     * 测试倒计时更新失败的错误处理
     */
    @Test
    fun `updateCountdown should handle errors gracefully`() = runTest {
        // Given
        every { timeManager.getTimeUntilMidnight() } throws RuntimeException("时间获取失败")
        
        // When
        viewModel.sendIntent(EarnContract.Intent.UpdateCountdown)
        advanceUntilIdle()
        
        // Then
        // 验证错误被正确处理，不会导致应用崩溃
        // 这里可以检查是否发送了错误Effect，但由于我们使用的是mock，
        // 主要是确保异常被捕获而不是传播出去
        assertTrue("测试应该完成而不抛出异常", true)
    }

    /**
     * 测试倒计时定时器重启功能
     */
    @Test
    fun `restartCountdownTimer should stop old timer and start new one`() = runTest {
        // Given
        viewModel.restartCountdownTimer()
        assertTrue("第一个定时器应该正在运行", viewModel.isCountdownTimerRunning())
        
        // When
        viewModel.restartCountdownTimer()
        
        // Then
        assertTrue("重启后定时器应该仍在运行", viewModel.isCountdownTimerRunning())
    }
}