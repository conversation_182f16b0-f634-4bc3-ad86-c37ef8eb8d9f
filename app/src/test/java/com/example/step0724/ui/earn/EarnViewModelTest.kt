package com.example.step0724.ui.earn

import com.example.step0724.data.model.TaskType
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * EarnViewModel单元测试
 * 
 * 测试EarnViewModel的基本功能，包括：
 * - 初始状态验证
 * - Intent类型验证
 * - 基本数据结构验证
 * - MVI架构契约验证
 * - 状态管理逻辑验证
 * 
 * Requirements: 2.1, 2.2, 2.3, 2.4, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 
 *              5.1, 5.2, 5.3, 5.4, 5.5, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 
 *              7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 8.1, 8.2, 8.3, 8.4, 8.5
 * 
 * 注意：由于项目没有MockK和协程测试依赖，这里主要测试契约定义和数据结构
 */
class EarnViewModelTest {

    @Before
    fun setup() {
        // 基本设置
    }

    @Test
    fun `EarnContract Intent types should be defined correctly`() {
        // Verify all Intent types are properly defined
        val loadDataIntent = EarnContract.Intent.LoadData
        val refreshDataIntent = EarnContract.Intent.RefreshData
        val claimTaskIntent = EarnContract.Intent.ClaimTask(TaskType.DAILY_CHECK_IN)
        val navigateToTaskIntent = EarnContract.Intent.NavigateToTask(TaskType.STEPS_1000)
        val navigateToLuckyWheelIntent = EarnContract.Intent.NavigateToLuckyWheel
        val navigateToEggSmashIntent = EarnContract.Intent.NavigateToEggSmash
        val navigateToMemoryMatchIntent = EarnContract.Intent.NavigateToMemoryMatch
        val navigateToStepsIntent = EarnContract.Intent.NavigateToSteps
        val updateCountdownIntent = EarnContract.Intent.UpdateCountdown
        
        // Verify they are not null and are of correct types
        assertNotNull(loadDataIntent)
        assertNotNull(refreshDataIntent)
        assertNotNull(claimTaskIntent)
        assertNotNull(navigateToTaskIntent)
        assertNotNull(navigateToLuckyWheelIntent)
        assertNotNull(navigateToEggSmashIntent)
        assertNotNull(navigateToMemoryMatchIntent)
        assertNotNull(navigateToStepsIntent)
        assertNotNull(updateCountdownIntent)
        
        // Verify ClaimTask intent contains correct task type
        assertEquals(TaskType.DAILY_CHECK_IN, claimTaskIntent.taskType)
        assertEquals(TaskType.STEPS_1000, navigateToTaskIntent.taskType)
    }

    @Test
    fun `EarnContract UiState should have correct default values`() {
        // Create a default UiState
        val defaultState = EarnContract.UiState()
        
        // Verify default values
        assertEquals(0, defaultState.coins)
        assertTrue(defaultState.dailyTasks.isEmpty())
        assertTrue(defaultState.otherTasks.isEmpty())
        assertEquals("24:00", defaultState.timeUntilReset)
        assertEquals(0, defaultState.consecutiveCheckIns)
        assertFalse(defaultState.isLoading)
        assertEquals(0, defaultState.currentSteps)
        assertNull(defaultState.errorMessage)
    }

    @Test
    fun `EarnContract Effect types should be defined correctly`() {
        // Verify all Effect types are properly defined
        val navigateToStepsEffect = EarnContract.Effect.NavigateToSteps
        val navigateToLuckyWheelEffect = EarnContract.Effect.NavigateToLuckyWheel
        val navigateToEggSmashEffect = EarnContract.Effect.NavigateToEggSmash
        val navigateToMemoryMatchEffect = EarnContract.Effect.NavigateToMemoryMatch
        val showRewardAnimationEffect = EarnContract.Effect.ShowRewardAnimation(10)
        val showErrorEffect = EarnContract.Effect.ShowError("Test error")
        val showSuccessEffect = EarnContract.Effect.ShowSuccess("Test success")
        
        // Verify they are not null
        assertNotNull(navigateToStepsEffect)
        assertNotNull(navigateToLuckyWheelEffect)
        assertNotNull(navigateToEggSmashEffect)
        assertNotNull(navigateToMemoryMatchEffect)
        assertNotNull(showRewardAnimationEffect)
        assertNotNull(showErrorEffect)
        assertNotNull(showSuccessEffect)
        
        // Verify data effects contain correct values
        assertEquals(10, showRewardAnimationEffect.coins)
        assertEquals("Test error", showErrorEffect.message)
        assertEquals("Test success", showSuccessEffect.message)
    }

    @Test
    fun `TaskType enum should contain all required task types`() {
        // Verify all required task types are defined
        val dailyCheckIn = TaskType.DAILY_CHECK_IN
        val luckyWheel = TaskType.LUCKY_WHEEL
        val steps1000 = TaskType.STEPS_1000
        val steps3000 = TaskType.STEPS_3000
        val checkIn2Days = TaskType.CHECK_IN_2_DAYS
        val checkIn3Days = TaskType.CHECK_IN_3_DAYS
        
        // Verify they are not null
        assertNotNull(dailyCheckIn)
        assertNotNull(luckyWheel)
        assertNotNull(steps1000)
        assertNotNull(steps3000)
        assertNotNull(checkIn2Days)
        assertNotNull(checkIn3Days)
        
        // Verify enum values
        assertEquals("DAILY_CHECK_IN", dailyCheckIn.name)
        assertEquals("LUCKY_WHEEL", luckyWheel.name)
        assertEquals("STEPS_1000", steps1000.name)
        assertEquals("STEPS_3000", steps3000.name)
        assertEquals("CHECK_IN_2_DAYS", checkIn2Days.name)
        assertEquals("CHECK_IN_3_DAYS", checkIn3Days.name)
    }

    @Test
    fun `UiState copy function should work correctly`() {
        // Create initial state
        val initialState = EarnContract.UiState(
            coins = 100,
            timeUntilReset = "12:30",
            consecutiveCheckIns = 5,
            isLoading = true,
            currentSteps = 1500
        )
        
        // Copy with changes
        val updatedState = initialState.copy(
            coins = 200,
            isLoading = false,
            errorMessage = "Test error"
        )
        
        // Verify changes
        assertEquals(200, updatedState.coins)
        assertFalse(updatedState.isLoading)
        assertEquals("Test error", updatedState.errorMessage)
        
        // Verify unchanged values
        assertEquals("12:30", updatedState.timeUntilReset)
        assertEquals(5, updatedState.consecutiveCheckIns)
        assertEquals(1500, updatedState.currentSteps)
    }

    @Test
    fun `Intent sealed class hierarchy should be correct`() {
        // Test that all intents are instances of the base Intent class
        val intents = listOf(
            EarnContract.Intent.LoadData,
            EarnContract.Intent.RefreshData,
            EarnContract.Intent.ClaimTask(TaskType.DAILY_CHECK_IN),
            EarnContract.Intent.NavigateToTask(TaskType.STEPS_1000),
            EarnContract.Intent.NavigateToLuckyWheel,
            EarnContract.Intent.NavigateToEggSmash,
            EarnContract.Intent.NavigateToMemoryMatch,
            EarnContract.Intent.NavigateToSteps,
            EarnContract.Intent.UpdateCountdown
        )
        
        // Verify all are instances of Intent
        intents.forEach { intent ->
            assertTrue("Intent should be instance of EarnContract.Intent", 
                intent is EarnContract.Intent)
        }
    }

    @Test
    fun `Effect sealed class hierarchy should be correct`() {
        // Test that all effects are instances of the base Effect class
        val effects = listOf(
            EarnContract.Effect.NavigateToSteps,
            EarnContract.Effect.NavigateToLuckyWheel,
            EarnContract.Effect.NavigateToEggSmash,
            EarnContract.Effect.NavigateToMemoryMatch,
            EarnContract.Effect.ShowRewardAnimation(10),
            EarnContract.Effect.ShowError("Test"),
            EarnContract.Effect.ShowSuccess("Test")
        )
        
        // Verify all are instances of Effect
        effects.forEach { effect ->
            assertTrue("Effect should be instance of EarnContract.Effect", 
                effect is EarnContract.Effect)
        }
    }

    @Test
    fun `ClaimTask intent should handle all task types correctly`() {
        // Test ClaimTask intent with all possible task types
        val taskTypes = listOf(
            TaskType.DAILY_CHECK_IN,
            TaskType.LUCKY_WHEEL,
            TaskType.STEPS_1000,
            TaskType.STEPS_3000,
            TaskType.CHECK_IN_2_DAYS,
            TaskType.CHECK_IN_3_DAYS
        )
        
        taskTypes.forEach { taskType ->
            val claimTaskIntent = EarnContract.Intent.ClaimTask(taskType)
            assertNotNull("ClaimTask intent should not be null for $taskType", claimTaskIntent)
            assertEquals("Task type should match", taskType, claimTaskIntent.taskType)
        }
    }

    @Test
    fun `NavigateToTask intent should handle all task types correctly`() {
        // Test NavigateToTask intent with all possible task types
        val taskTypes = listOf(
            TaskType.DAILY_CHECK_IN,
            TaskType.LUCKY_WHEEL,
            TaskType.STEPS_1000,
            TaskType.STEPS_3000,
            TaskType.CHECK_IN_2_DAYS,
            TaskType.CHECK_IN_3_DAYS
        )
        
        taskTypes.forEach { taskType ->
            val navigateToTaskIntent = EarnContract.Intent.NavigateToTask(taskType)
            assertNotNull("NavigateToTask intent should not be null for $taskType", navigateToTaskIntent)
            assertEquals("Task type should match", taskType, navigateToTaskIntent.taskType)
        }
    }

    @Test
    fun `ShowRewardAnimation effect should handle different coin amounts`() {
        // Test ShowRewardAnimation effect with different coin amounts
        val coinAmounts = listOf(0, 5, 10, 15, 20, 30, 50, 100)
        
        coinAmounts.forEach { amount ->
            val effect = EarnContract.Effect.ShowRewardAnimation(amount)
            assertNotNull("ShowRewardAnimation effect should not be null for $amount coins", effect)
            assertEquals("Coin amount should match", amount, effect.coins)
        }
    }

    @Test
    fun `UiState should support all required fields for Earn functionality`() {
        // Test UiState with all fields populated
        val testTasks = emptyList<com.example.step0724.data.model.Task>()
        
        val fullState = EarnContract.UiState(
            coins = 1000,
            dailyTasks = testTasks,
            otherTasks = testTasks,
            timeUntilReset = "23:59",
            consecutiveCheckIns = 7,
            isLoading = true,
            currentSteps = 5000,
            errorMessage = "Test error message"
        )
        
        // Verify all fields
        assertEquals("Coins should match", 1000, fullState.coins)
        assertEquals("Daily tasks should match", testTasks, fullState.dailyTasks)
        assertEquals("Other tasks should match", testTasks, fullState.otherTasks)
        assertEquals("Time until reset should match", "23:59", fullState.timeUntilReset)
        assertEquals("Consecutive check-ins should match", 7, fullState.consecutiveCheckIns)
        assertTrue("Loading state should be true", fullState.isLoading)
        assertEquals("Current steps should match", 5000, fullState.currentSteps)
        assertEquals("Error message should match", "Test error message", fullState.errorMessage)
    }

    @Test
    fun `UiState should handle null error message correctly`() {
        // Test UiState with null error message
        val stateWithNullError = EarnContract.UiState(errorMessage = null)
        assertNull("Error message should be null", stateWithNullError.errorMessage)
        
        // Test UiState with non-null error message
        val stateWithError = EarnContract.UiState(errorMessage = "Error occurred")
        assertEquals("Error message should match", "Error occurred", stateWithError.errorMessage)
    }

    @Test
    fun `All navigation intents should be properly defined`() {
        // Test all navigation-related intents
        val navigationIntents = listOf(
            EarnContract.Intent.NavigateToLuckyWheel,
            EarnContract.Intent.NavigateToEggSmash,
            EarnContract.Intent.NavigateToMemoryMatch,
            EarnContract.Intent.NavigateToSteps
        )
        
        navigationIntents.forEach { intent ->
            assertNotNull("Navigation intent should not be null", intent)
            assertTrue("Should be instance of Intent", intent is EarnContract.Intent)
        }
    }

    @Test
    fun `All navigation effects should be properly defined`() {
        // Test all navigation-related effects
        val navigationEffects = listOf(
            EarnContract.Effect.NavigateToSteps,
            EarnContract.Effect.NavigateToLuckyWheel,
            EarnContract.Effect.NavigateToEggSmash,
            EarnContract.Effect.NavigateToMemoryMatch
        )
        
        navigationEffects.forEach { effect ->
            assertNotNull("Navigation effect should not be null", effect)
            assertTrue("Should be instance of Effect", effect is EarnContract.Effect)
        }
    }

    @Test
    fun `Error and success effects should handle different message types`() {
        // Test error effects with different message types
        val errorMessages = listOf(
            "",
            "Simple error",
            "Error with numbers 123",
            "Error with special chars !@#$%",
            "Very long error message that contains multiple words and should be handled properly by the UI"
        )
        
        errorMessages.forEach { message ->
            val errorEffect = EarnContract.Effect.ShowError(message)
            assertNotNull("Error effect should not be null", errorEffect)
            assertEquals("Error message should match", message, errorEffect.message)
        }
        
        // Test success effects with different message types
        val successMessages = listOf(
            "",
            "Success!",
            "Claimed 10 coins!",
            "Task completed successfully"
        )
        
        successMessages.forEach { message ->
            val successEffect = EarnContract.Effect.ShowSuccess(message)
            assertNotNull("Success effect should not be null", successEffect)
            assertEquals("Success message should match", message, successEffect.message)
        }
    }

    @Test
    fun `UiState copy should preserve unmodified fields`() {
        // Create initial state with all fields set
        val initialState = EarnContract.UiState(
            coins = 500,
            dailyTasks = emptyList(),
            otherTasks = emptyList(),
            timeUntilReset = "12:00",
            consecutiveCheckIns = 3,
            isLoading = false,
            currentSteps = 2000,
            errorMessage = "Initial error"
        )
        
        // Test copying only coins
        val coinsOnlyUpdate = initialState.copy(coins = 600)
        assertEquals("Coins should be updated", 600, coinsOnlyUpdate.coins)
        assertEquals("Time should be preserved", "12:00", coinsOnlyUpdate.timeUntilReset)
        assertEquals("Check-ins should be preserved", 3, coinsOnlyUpdate.consecutiveCheckIns)
        assertEquals("Steps should be preserved", 2000, coinsOnlyUpdate.currentSteps)
        
        // Test copying only loading state
        val loadingOnlyUpdate = initialState.copy(isLoading = true)
        assertTrue("Loading should be updated", loadingOnlyUpdate.isLoading)
        assertEquals("Coins should be preserved", 500, loadingOnlyUpdate.coins)
        assertEquals("Error should be preserved", "Initial error", loadingOnlyUpdate.errorMessage)
        
        // Test copying error message to null
        val errorClearUpdate = initialState.copy(errorMessage = null)
        assertNull("Error should be cleared", errorClearUpdate.errorMessage)
        assertEquals("Other fields should be preserved", 500, errorClearUpdate.coins)
    }

    @Test
    fun `Intent and Effect classes should implement proper interfaces`() {
        // Verify Intent implements UiIntent
        val intent = EarnContract.Intent.LoadData
        assertTrue("Intent should implement UiIntent", 
            intent is com.example.step0724.core.mvi.UiIntent)
        
        // Verify UiState implements UiState interface
        val state = EarnContract.UiState()
        assertTrue("UiState should implement UiState interface", 
            state is com.example.step0724.core.mvi.UiState)
        
        // Verify Effect implements UiEffect
        val effect = EarnContract.Effect.NavigateToSteps
        assertTrue("Effect should implement UiEffect", 
            effect is com.example.step0724.core.mvi.UiEffect)
    }
    
    // ==================== Step Task Integration Tests ====================
    
    @Test
    fun `step task intents should be properly defined`() {
        // Requirements: 6.5 - 添加跳转到Steps页面的导航逻辑
        val navigateToStepsIntent = EarnContract.Intent.NavigateToSteps
        val navigateToSteps1000Intent = EarnContract.Intent.NavigateToTask(TaskType.STEPS_1000)
        val navigateToSteps3000Intent = EarnContract.Intent.NavigateToTask(TaskType.STEPS_3000)
        val claimSteps1000Intent = EarnContract.Intent.ClaimTask(TaskType.STEPS_1000)
        val claimSteps3000Intent = EarnContract.Intent.ClaimTask(TaskType.STEPS_3000)
        
        // Verify intents are not null
        assertNotNull("NavigateToSteps intent should not be null", navigateToStepsIntent)
        assertNotNull("NavigateToTask(STEPS_1000) intent should not be null", navigateToSteps1000Intent)
        assertNotNull("NavigateToTask(STEPS_3000) intent should not be null", navigateToSteps3000Intent)
        assertNotNull("ClaimTask(STEPS_1000) intent should not be null", claimSteps1000Intent)
        assertNotNull("ClaimTask(STEPS_3000) intent should not be null", claimSteps3000Intent)
        
        // Verify task types are correct
        assertEquals("STEPS_1000 task type should match", TaskType.STEPS_1000, navigateToSteps1000Intent.taskType)
        assertEquals("STEPS_3000 task type should match", TaskType.STEPS_3000, navigateToSteps3000Intent.taskType)
        assertEquals("STEPS_1000 claim task type should match", TaskType.STEPS_1000, claimSteps1000Intent.taskType)
        assertEquals("STEPS_3000 claim task type should match", TaskType.STEPS_3000, claimSteps3000Intent.taskType)
    }
    
    @Test
    fun `step task effects should be properly defined`() {
        // Requirements: 6.5 - 添加跳转到Steps页面的导航逻辑
        val navigateToStepsEffect = EarnContract.Effect.NavigateToSteps
        val showStepRewardEffect = EarnContract.Effect.ShowRewardAnimation(5) // 1000 steps reward
        val showStep3000RewardEffect = EarnContract.Effect.ShowRewardAnimation(15) // 3000 steps reward
        val showStepSuccessEffect = EarnContract.Effect.ShowSuccess("1000 steps completed!")
        val showStepErrorEffect = EarnContract.Effect.ShowError("You need to reach 1000 steps first!")
        
        // Verify effects are not null
        assertNotNull("NavigateToSteps effect should not be null", navigateToStepsEffect)
        assertNotNull("Step reward animation effect should not be null", showStepRewardEffect)
        assertNotNull("Step 3000 reward animation effect should not be null", showStep3000RewardEffect)
        assertNotNull("Step success effect should not be null", showStepSuccessEffect)
        assertNotNull("Step error effect should not be null", showStepErrorEffect)
        
        // Verify reward amounts match step task rewards
        assertEquals("1000 steps reward should be 5", 5, showStepRewardEffect.coins)
        assertEquals("3000 steps reward should be 15", 15, showStep3000RewardEffect.coins)
        
        // Verify messages are appropriate
        assertTrue("Success message should mention steps", 
            showStepSuccessEffect.message.contains("steps"))
        assertTrue("Error message should mention steps requirement", 
            showStepErrorEffect.message.contains("steps"))
    }
    
    @Test
    fun `UiState should track current steps correctly`() {
        // Requirements: 6.3 - 实现步数任务状态的实时更新逻辑
        
        // Test default current steps
        val defaultState = EarnContract.UiState()
        assertEquals("Default current steps should be 0", 0, defaultState.currentSteps)
        
        // Test with different step counts
        val stepCounts = listOf(0, 500, 1000, 1500, 3000, 5000)
        
        stepCounts.forEach { steps ->
            val state = EarnContract.UiState(currentSteps = steps)
            assertEquals("Current steps should match for $steps", steps, state.currentSteps)
            assertTrue("Current steps should be non-negative", state.currentSteps >= 0)
        }
        
        // Test step count updates
        val initialState = EarnContract.UiState(currentSteps = 500)
        val updatedState = initialState.copy(currentSteps = 1200)
        
        assertEquals("Initial steps should be 500", 500, initialState.currentSteps)
        assertEquals("Updated steps should be 1200", 1200, updatedState.currentSteps)
        assertNotEquals("Step counts should be different", 
            initialState.currentSteps, updatedState.currentSteps)
    }
    
    @Test
    fun `step task navigation logic should be consistent`() {
        // Requirements: 6.5 - 当用户点击步数任务的"Go"按钮时，系统应跳转到Steps页面
        
        // Test that step task types should navigate to Steps page
        val stepTaskTypes = listOf(TaskType.STEPS_1000, TaskType.STEPS_3000)
        
        stepTaskTypes.forEach { taskType ->
            val navigateIntent = EarnContract.Intent.NavigateToTask(taskType)
            assertNotNull("Navigate intent should not be null for $taskType", navigateIntent)
            assertEquals("Task type should match", taskType, navigateIntent.taskType)
            
            // Verify task type is a step task
            assertTrue("Task type should be a step task", 
                taskType == TaskType.STEPS_1000 || taskType == TaskType.STEPS_3000)
        }
        
        // Test NavigateToSteps effect
        val navigateEffect = EarnContract.Effect.NavigateToSteps
        assertNotNull("NavigateToSteps effect should not be null", navigateEffect)
        assertTrue("Should be instance of Effect", navigateEffect is EarnContract.Effect)
    }
    
    @Test
    fun `step task reward claiming should be properly supported`() {
        // Requirements: 6.4 - 实现步数任务的奖励发放
        
        // Test step task claim intents
        val claim1000Intent = EarnContract.Intent.ClaimTask(TaskType.STEPS_1000)
        val claim3000Intent = EarnContract.Intent.ClaimTask(TaskType.STEPS_3000)
        
        assertNotNull("Claim 1000 steps intent should not be null", claim1000Intent)
        assertNotNull("Claim 3000 steps intent should not be null", claim3000Intent)
        
        assertEquals("1000 steps task type should match", TaskType.STEPS_1000, claim1000Intent.taskType)
        assertEquals("3000 steps task type should match", TaskType.STEPS_3000, claim3000Intent.taskType)
        
        // Test step task reward effects
        val reward1000Effect = EarnContract.Effect.ShowRewardAnimation(5)
        val reward3000Effect = EarnContract.Effect.ShowRewardAnimation(15)
        
        assertEquals("1000 steps reward should be 5 coins", 5, reward1000Effect.coins)
        assertEquals("3000 steps reward should be 15 coins", 15, reward3000Effect.coins)
        
        // Verify reward progression
        assertTrue("3000 steps reward should be greater than 1000 steps reward",
            reward3000Effect.coins > reward1000Effect.coins)
    }
    
    @Test
    fun `step task success and error messages should be appropriate`() {
        // Requirements: 6.4 - 步数任务奖励发放的用户反馈
        
        // Test success messages for step tasks
        val success1000 = EarnContract.Effect.ShowSuccess("Congratulations! 1000 steps completed! Earned 5 coins!")
        val success3000 = EarnContract.Effect.ShowSuccess("Amazing! 3000 steps completed! Earned 15 coins!")
        
        assertNotNull("1000 steps success message should not be null", success1000)
        assertNotNull("3000 steps success message should not be null", success3000)
        
        assertTrue("1000 steps success should mention 1000", success1000.message.contains("1000"))
        assertTrue("3000 steps success should mention 3000", success3000.message.contains("3000"))
        assertTrue("1000 steps success should mention 5 coins", success1000.message.contains("5"))
        assertTrue("3000 steps success should mention 15 coins", success3000.message.contains("15"))
        
        // Test error messages for step tasks
        val error1000 = EarnContract.Effect.ShowError("You need to reach 1000 steps first!")
        val error3000 = EarnContract.Effect.ShowError("You need to reach 3000 steps first!")
        
        assertNotNull("1000 steps error message should not be null", error1000)
        assertNotNull("3000 steps error message should not be null", error3000)
        
        assertTrue("1000 steps error should mention 1000", error1000.message.contains("1000"))
        assertTrue("3000 steps error should mention 3000", error3000.message.contains("3000"))
        assertTrue("Error messages should mention requirement", 
            error1000.message.contains("need") && error3000.message.contains("need"))
    }
    
    @Test
    fun `step task integration should support real-time updates`() {
        // Requirements: 6.3 - 实现步数任务状态的实时更新逻辑
        
        // Test that UiState can track step changes for real-time updates
        val states = listOf(
            EarnContract.UiState(currentSteps = 0),
            EarnContract.UiState(currentSteps = 500),
            EarnContract.UiState(currentSteps = 999),
            EarnContract.UiState(currentSteps = 1000),
            EarnContract.UiState(currentSteps = 1001),
            EarnContract.UiState(currentSteps = 2999),
            EarnContract.UiState(currentSteps = 3000),
            EarnContract.UiState(currentSteps = 3001)
        )
        
        // Verify each state tracks steps correctly
        states.forEachIndexed { index, state ->
            val expectedSteps = when (index) {
                0 -> 0
                1 -> 500
                2 -> 999
                3 -> 1000
                4 -> 1001
                5 -> 2999
                6 -> 3000
                7 -> 3001
                else -> 0
            }
            assertEquals("State $index should have correct steps", expectedSteps, state.currentSteps)
        }
        
        // Test step progression simulation
        val progressionStates = (0..3000 step 500).map { steps ->
            EarnContract.UiState(currentSteps = steps)
        }
        
        progressionStates.forEachIndexed { index, state ->
            val expectedSteps = index * 500
            assertEquals("Progression state $index should have $expectedSteps steps", 
                expectedSteps, state.currentSteps)
        }
    }
    
    // ==================== Countdown Functionality Tests ====================
    
    @Test
    fun `UpdateCountdown intent should be properly defined`() {
        // Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
        val updateCountdownIntent = EarnContract.Intent.UpdateCountdown
        
        assertNotNull("UpdateCountdown intent should not be null", updateCountdownIntent)
        assertTrue("UpdateCountdown should be instance of Intent", 
            updateCountdownIntent is EarnContract.Intent)
    }
    
    @Test
    fun `UiState should track countdown time correctly`() {
        // Requirements: 8.1 - 系统应使用"HH:MM"格式显示剩余时间
        // Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
        
        // Test default countdown time
        val defaultState = EarnContract.UiState()
        assertEquals("Default countdown should be 24:00", "24:00", defaultState.timeUntilReset)
        
        // Test various countdown formats
        val countdownTimes = listOf(
            "24:00", "23:59", "12:30", "06:15", "01:00", "00:30", "00:01", "00:00"
        )
        
        countdownTimes.forEach { time ->
            val state = EarnContract.UiState(timeUntilReset = time)
            assertEquals("Countdown time should match for $time", time, state.timeUntilReset)
            
            // Verify format is HH:MM
            assertTrue("Time format should be HH:MM for $time", 
                time.matches(Regex("\\d{2}:\\d{2}")))
        }
    }
    
    @Test
    fun `countdown time format should be valid`() {
        // Requirements: 8.1 - 系统应使用"HH:MM"格式显示剩余时间
        
        val validTimes = listOf(
            "00:00", "00:01", "00:59", "01:00", "12:00", "23:59", "24:00"
        )
        
        val invalidTimes = listOf(
            "0:00", "00:0", "0:0", "25:00", "24:60", "12:60", "", "invalid"
        )
        
        // Test valid time formats
        validTimes.forEach { time ->
            assertTrue("$time should be valid HH:MM format", 
                time.matches(Regex("\\d{2}:\\d{2}")))
            
            val state = EarnContract.UiState(timeUntilReset = time)
            assertEquals("State should store valid time $time", time, state.timeUntilReset)
        }
        
        // Test that invalid formats can still be stored (validation should happen elsewhere)
        invalidTimes.forEach { time ->
            val state = EarnContract.UiState(timeUntilReset = time)
            assertEquals("State should store time $time even if invalid", time, state.timeUntilReset)
        }
    }
    
    @Test
    fun `countdown updates should preserve other state fields`() {
        // Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
        
        // Create initial state with all fields populated
        val initialState = EarnContract.UiState(
            coins = 500,
            dailyTasks = emptyList(),
            otherTasks = emptyList(),
            timeUntilReset = "12:30",
            consecutiveCheckIns = 5,
            isLoading = false,
            currentSteps = 2000,
            errorMessage = null
        )
        
        // Update only countdown time
        val updatedState = initialState.copy(timeUntilReset = "12:29")
        
        // Verify countdown was updated
        assertEquals("Countdown should be updated", "12:29", updatedState.timeUntilReset)
        
        // Verify other fields were preserved
        assertEquals("Coins should be preserved", 500, updatedState.coins)
        assertEquals("Check-ins should be preserved", 5, updatedState.consecutiveCheckIns)
        assertEquals("Steps should be preserved", 2000, updatedState.currentSteps)
        assertEquals("Loading state should be preserved", false, updatedState.isLoading)
        assertEquals("Error message should be preserved", null, updatedState.errorMessage)
    }
    
    @Test
    fun `countdown progression should be logical`() {
        // Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
        // Requirements: 8.3 - 当倒计时到达00:00时，系统应重置所有每日任务状态
        
        // Test countdown progression from high to low
        val countdownProgression = listOf(
            "24:00", "23:59", "23:58", "12:00", "01:00", "00:59", "00:01", "00:00"
        )
        
        countdownProgression.forEachIndexed { index, time ->
            val state = EarnContract.UiState(timeUntilReset = time)
            assertEquals("Countdown at step $index should be $time", time, state.timeUntilReset)
            
            // Verify time components
            val parts = time.split(":")
            assertEquals("Should have 2 time parts", 2, parts.size)
            
            val hours = parts[0].toIntOrNull()
            val minutes = parts[1].toIntOrNull()
            
            assertNotNull("Hours should be valid integer", hours)
            assertNotNull("Minutes should be valid integer", minutes)
            
            assertTrue("Hours should be 0-24", hours!! in 0..24)
            assertTrue("Minutes should be 0-59", minutes!! in 0..59)
        }
    }
    
    @Test
    fun `midnight reset should be properly supported`() {
        // Requirements: 8.3 - 当倒计时到达00:00时，系统应重置所有每日任务状态
        // Requirements: 8.4 - 当倒计时到达00:00时，系统应更新倒计时为下一天的24小时
        
        // Test state before midnight
        val beforeMidnight = EarnContract.UiState(
            timeUntilReset = "00:01",
            coins = 100,
            consecutiveCheckIns = 3
        )
        
        assertEquals("Before midnight countdown should be 00:01", "00:01", beforeMidnight.timeUntilReset)
        
        // Test state at midnight (reset condition)
        val atMidnight = EarnContract.UiState(
            timeUntilReset = "00:00",
            coins = 100,
            consecutiveCheckIns = 3
        )
        
        assertEquals("At midnight countdown should be 00:00", "00:00", atMidnight.timeUntilReset)
        
        // Test state after reset (new day)
        val afterReset = EarnContract.UiState(
            timeUntilReset = "24:00",
            coins = 100,
            consecutiveCheckIns = 4  // Potentially incremented after check-in
        )
        
        assertEquals("After reset countdown should be 24:00", "24:00", afterReset.timeUntilReset)
    }
    
    @Test
    fun `countdown should support edge cases`() {
        // Requirements: 8.5 - 当用户重新进入Earn页面时，系统应显示准确的剩余时间
        
        // Test edge case times
        val edgeCases = listOf(
            "00:00",  // Exactly midnight
            "00:01",  // One minute after midnight
            "23:59",  // One minute before midnight
            "12:00",  // Noon
            "24:00"   // Full day
        )
        
        edgeCases.forEach { time ->
            val state = EarnContract.UiState(timeUntilReset = time)
            assertEquals("Edge case time $time should be stored correctly", time, state.timeUntilReset)
            
            // Test copying with edge case times
            val copiedState = state.copy(coins = 999)
            assertEquals("Copied state should preserve edge case time $time", time, copiedState.timeUntilReset)
            assertEquals("Copied state should update other fields", 999, copiedState.coins)
        }
    }
    
    @Test
    fun `countdown intent should be handled in all scenarios`() {
        // Requirements: 8.2 - 当用户停留在Earn页面时，系统应每分钟更新一次倒计时显示
        
        // Test UpdateCountdown intent creation
        val updateIntent = EarnContract.Intent.UpdateCountdown
        assertNotNull("UpdateCountdown intent should be created", updateIntent)
        
        // Test that UpdateCountdown is a singleton object
        val anotherUpdateIntent = EarnContract.Intent.UpdateCountdown
        assertSame("UpdateCountdown should be singleton", updateIntent, anotherUpdateIntent)
        
        // Test UpdateCountdown in list of intents
        val intentList = listOf(
            EarnContract.Intent.LoadData,
            EarnContract.Intent.RefreshData,
            EarnContract.Intent.UpdateCountdown
        )
        
        assertTrue("Intent list should contain UpdateCountdown", 
            intentList.contains(EarnContract.Intent.UpdateCountdown))
        
        // Verify UpdateCountdown is distinct from other intents
        assertNotEquals("UpdateCountdown should be different from LoadData", 
            EarnContract.Intent.UpdateCountdown, EarnContract.Intent.LoadData)
        assertNotEquals("UpdateCountdown should be different from RefreshData", 
            EarnContract.Intent.UpdateCountdown, EarnContract.Intent.RefreshData)
    }
}