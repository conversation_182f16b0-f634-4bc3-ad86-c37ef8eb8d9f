package com.example.step0724.data.storage

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.text.SimpleDateFormat
import java.util.*

/**
 * TimeManager单元测试
 * 
 * 测试TimeManager类的所有功能：
 * - getCurrentDateString方法测试
 * - getTimeUntilMidnight方法测试
 * - isNewDay方法测试
 * - 边界条件和异常情况测试
 * 
 * Requirements: 8.1, 8.2, 8.3, 8.4, 8.5
 */
class TimeManagerTest {

    private lateinit var timeManager: TimeManager
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    @Before
    fun setUp() {
        timeManager = TimeManager()
    }

    @Test
    fun `getCurrentDateString should return current date in correct format`() {
        // Requirements: 8.1 - 获取当前日期字符串
        val result = timeManager.getCurrentDateString()
        val expectedDate = dateFormatter.format(Date())
        
        assertEquals(expectedDate, result)
        
        // 验证日期格式
        assertTrue("Date should match yyyy-MM-dd format", result.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }

    @Test
    fun `getTimeUntilMidnight should return time in HH_MM format`() {
        // Requirements: 8.2 - 倒计时应使用"HH:MM"格式显示剩余时间
        val result = timeManager.getTimeUntilMidnight()
        
        // 验证格式是否为HH:MM
        assertTrue("Time should match HH:MM format", result.matches(Regex("\\d{2}:\\d{2}")))
        
        // 验证时间范围是否合理（0-23小时，0-59分钟）
        val parts = result.split(":")
        val hours = parts[0].toInt()
        val minutes = parts[1].toInt()
        
        assertTrue("Hours should be between 0-23", hours in 0..23)
        assertTrue("Minutes should be between 0-59", minutes in 0..59)
    }

    @Test
    fun `getTimeUntilMidnight should never return negative time`() {
        // Requirements: 8.4 - 防止时间异常时返回负值
        val result = timeManager.getTimeUntilMidnight()
        val parts = result.split(":")
        val hours = parts[0].toInt()
        val minutes = parts[1].toInt()
        
        assertTrue("Hours should not be negative", hours >= 0)
        assertTrue("Minutes should not be negative", minutes >= 0)
    }

    @Test
    fun `isNewDay should return true for different dates`() {
        // Requirements: 8.3, 8.5 - 判断日期变更
        val yesterday = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, -1)
        }
        val yesterdayString = dateFormatter.format(yesterday.time)
        
        val result = timeManager.isNewDay(yesterdayString)
        
        assertTrue("Should return true for different dates", result)
    }

    @Test
    fun `isNewDay should return false for same date`() {
        // Requirements: 8.3, 8.5 - 同一天应返回false
        val today = timeManager.getCurrentDateString()
        
        val result = timeManager.isNewDay(today)
        
        assertFalse("Should return false for same date", result)
    }

    @Test
    fun `isNewDay should handle future dates correctly`() {
        // 测试未来日期的处理
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_MONTH, 1)
        }
        val tomorrowString = dateFormatter.format(tomorrow.time)
        
        val result = timeManager.isNewDay(tomorrowString)
        
        assertTrue("Should return true for future dates", result)
    }

    @Test
    fun `getMillisUntilMidnight should return positive value`() {
        // 测试获取到午夜的毫秒数
        val result = timeManager.getMillisUntilMidnight()
        
        assertTrue("Millis until midnight should be positive or zero", result >= 0)
        
        // 验证不会超过24小时的毫秒数
        val maxMillisInDay = 24 * 60 * 60 * 1000L
        assertTrue("Millis should not exceed 24 hours", result <= maxMillisInDay)
    }

    @Test
    fun `isMidnightPassed should work correctly`() {
        // 测试午夜检查功能
        val result = timeManager.isMidnightPassed()
        
        // 在正常情况下，应该返回false（除非正好在午夜时刻运行测试）
        // 这个测试主要验证方法不会抛出异常
        assertNotNull("Method should return a boolean value", result)
    }

    @Test
    fun `getCurrentDateString should be consistent across multiple calls`() {
        // 测试多次调用的一致性
        val firstCall = timeManager.getCurrentDateString()
        
        // 短时间内多次调用应该返回相同结果
        Thread.sleep(10) // 等待10毫秒
        val secondCall = timeManager.getCurrentDateString()
        
        assertEquals("Multiple calls should return same date", firstCall, secondCall)
    }

    @Test
    fun `getTimeUntilMidnight should decrease over time`() {
        // 测试倒计时是否随时间递减
        val firstTime = timeManager.getTimeUntilMidnight()
        
        // 等待一小段时间
        Thread.sleep(1000) // 等待1秒
        
        val secondTime = timeManager.getTimeUntilMidnight()
        
        // 由于时间精度问题，这里只验证格式正确性
        assertTrue("First time should be valid format", firstTime.matches(Regex("\\d{2}:\\d{2}")))
        assertTrue("Second time should be valid format", secondTime.matches(Regex("\\d{2}:\\d{2}")))
    }

    @Test
    fun `isNewDay should handle edge cases`() {
        // 测试边界情况
        val emptyString = ""
        val invalidDate = "invalid-date"
        val validOldDate = "2020-01-01"
        
        // 空字符串应该被认为是新的一天
        assertTrue("Empty string should be considered new day", timeManager.isNewDay(emptyString))
        
        // 无效日期应该被认为是新的一天
        assertTrue("Invalid date should be considered new day", timeManager.isNewDay(invalidDate))
        
        // 很久以前的日期应该被认为是新的一天
        assertTrue("Old date should be considered new day", timeManager.isNewDay(validOldDate))
    }

    @Test
    fun `time format should be zero padded`() {
        // 验证时间格式是否正确补零
        val result = timeManager.getTimeUntilMidnight()
        val parts = result.split(":")
        
        assertEquals("Hours should be 2 digits", 2, parts[0].length)
        assertEquals("Minutes should be 2 digits", 2, parts[1].length)
    }
}