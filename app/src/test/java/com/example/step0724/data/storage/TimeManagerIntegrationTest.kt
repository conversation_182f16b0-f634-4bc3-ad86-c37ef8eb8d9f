package com.example.step0724.data.storage

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * TimeManager集成测试
 * 
 * 验证TimeManager能够正确地实例化和使用，以及与其他组件的集成
 */
class TimeManagerIntegrationTest {

    private lateinit var timeManager: TimeManager

    @Before
    fun setUp() {
        timeManager = TimeManager()
    }

    @Test
    fun `timeManager should be instantiated successfully`() {
        // 验证TimeManager能够被正确实例化
        assertNotNull("TimeManager should be instantiated", timeManager)
    }

    @Test
    fun `timeManager should provide consistent functionality across multiple calls`() {
        // 验证TimeManager功能的一致性
        val currentDate1 = timeManager.getCurrentDateString()
        val currentDate2 = timeManager.getCurrentDateString()
        
        // 短时间内多次调用应该返回相同结果
        assertEquals("Multiple calls should return same date", currentDate1, currentDate2)
        
        // 验证日期格式
        assertTrue("Date should match yyyy-MM-dd format", 
            currentDate1.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }

    @Test
    fun `timeManager should handle edge cases properly`() {
        // 测试边界情况处理
        val timeUntilMidnight = timeManager.getTimeUntilMidnight()
        val millisUntilMidnight = timeManager.getMillisUntilMidnight()
        val isMidnightPassed = timeManager.isMidnightPassed()
        
        // 验证返回值的合理性
        assertTrue("Time should match HH:MM format", 
            timeUntilMidnight.matches(Regex("\\d{2}:\\d{2}")))
        assertTrue("Millis should be non-negative", millisUntilMidnight >= 0)
        assertNotNull("Midnight check should return boolean", isMidnightPassed)
    }

    @Test
    fun `timeManager should handle date comparison correctly`() {
        // 测试日期比较功能
        val today = timeManager.getCurrentDateString()
        val yesterday = "2020-01-01"
        val emptyString = ""
        val invalidDate = "invalid-date"
        
        // 验证日期比较逻辑
        assertFalse("Same date should return false", timeManager.isNewDay(today))
        assertTrue("Old date should return true", timeManager.isNewDay(yesterday))
        assertTrue("Empty string should return true", timeManager.isNewDay(emptyString))
        assertTrue("Invalid date should return true", timeManager.isNewDay(invalidDate))
    }

    @Test
    fun `timeManager should be thread safe for basic operations`() {
        // 基本的线程安全测试
        val results = mutableListOf<String>()
        val threads = mutableListOf<Thread>()
        
        // 创建多个线程同时调用getCurrentDateString
        repeat(5) {
            val thread = Thread {
                results.add(timeManager.getCurrentDateString())
            }
            threads.add(thread)
            thread.start()
        }
        
        // 等待所有线程完成
        threads.forEach { it.join() }
        
        // 验证所有结果都是相同的日期（在短时间内）
        val uniqueDates = results.toSet()
        assertEquals("All threads should return same date", 1, uniqueDates.size)
    }
}