package com.example.step0724.data.storage

import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * EarnStorage集成测试
 * 
 * 测试EarnStorage与TimeManager的集成，验证：
 * - 时间相关功能的正确性
 * - 日期处理逻辑
 * - 基本的业务逻辑流程
 * 
 * Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7
 */
class EarnStorageIntegrationTest {
    
    private lateinit var timeManager: TimeManager
    
    @Before
    fun setUp() {
        timeManager = TimeManager()
    }
    
    @Test
    fun `timeManager should provide consistent date format`() {
        // Requirements: 8.1 - 获取当前日期字符串
        val date1 = timeManager.getCurrentDateString()
        Thread.sleep(10) // 等待10毫秒
        val date2 = timeManager.getCurrentDateString()
        
        // 短时间内应该返回相同的日期
        assertEquals("Date should be consistent in short time", date1, date2)
        
        // 验证日期格式
        assertTrue("Date should match yyyy-MM-dd format", 
            date1.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }
    
    @Test
    fun `timeManager should handle day comparison correctly`() {
        // Requirements: 8.3 - 判断日期变更
        val today = timeManager.getCurrentDateString()
        val sameDay = today
        val differentDay = "2020-01-01"
        val emptyString = ""
        
        assertFalse("Same day should return false", timeManager.isNewDay(sameDay))
        assertTrue("Different day should return true", timeManager.isNewDay(differentDay))
        assertTrue("Empty string should return true", timeManager.isNewDay(emptyString))
    }
    
    @Test
    fun `timeManager should provide valid countdown`() {
        // Requirements: 8.2 - 倒计时显示
        val countdown = timeManager.getTimeUntilMidnight()
        
        assertNotNull("Countdown should not be null", countdown)
        assertTrue("Countdown should match HH:MM format", 
            countdown.matches(Regex("\\d{2}:\\d{2}")))
        
        val parts = countdown.split(":")
        val hours = parts[0].toInt()
        val minutes = parts[1].toInt()
        
        assertTrue("Hours should be valid (0-23)", hours in 0..23)
        assertTrue("Minutes should be valid (0-59)", minutes in 0..59)
    }
    
    @Test
    fun `consecutive day logic should work with real dates`() {
        // Requirements: 10.3 - 签到功能数据持久化逻辑
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val calendar = java.util.Calendar.getInstance()
        
        // 今天
        val today = dateFormat.format(calendar.time)
        
        // 昨天
        calendar.add(java.util.Calendar.DAY_OF_MONTH, -1)
        val yesterday = dateFormat.format(calendar.time)
        
        // 前天
        calendar.add(java.util.Calendar.DAY_OF_MONTH, -1)
        val dayBeforeYesterday = dateFormat.format(calendar.time)
        
        // 验证日期不同
        assertNotEquals("Today and yesterday should be different", today, yesterday)
        assertNotEquals("Yesterday and day before should be different", yesterday, dayBeforeYesterday)
        
        // 验证日期格式
        assertTrue("Today should match format", today.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
        assertTrue("Yesterday should match format", yesterday.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }
    
    @Test
    fun `storage key generation should be consistent`() {
        // Requirements: 10.1, 10.2 - 存储键值生成的一致性
        val testDate = timeManager.getCurrentDateString()
        
        // 模拟键值生成逻辑
        val coinKey = "earn_coins"
        val checkInKey = "last_check_in_date"
        val consecutiveKey = "consecutive_check_ins"
        val dailyTaskKey = "daily_tasks_${testDate}_DAILY_CHECK_IN"
        val otherTaskKey = "other_tasks_CHECK_IN_2_DAYS"
        val luckyWheelKey = "lucky_wheel_count_$testDate"
        
        // 验证键值不为空且格式正确
        assertFalse("Coin key should not be empty", coinKey.isEmpty())
        assertFalse("Check in key should not be empty", checkInKey.isEmpty())
        assertFalse("Consecutive key should not be empty", consecutiveKey.isEmpty())
        assertTrue("Daily task key should contain date", dailyTaskKey.contains(testDate))
        assertTrue("Other task key should contain task type", otherTaskKey.contains("CHECK_IN_2_DAYS"))
        assertTrue("Lucky wheel key should contain date", luckyWheelKey.contains(testDate))
    }
    
    @Test
    fun `reward calculation should be deterministic`() {
        // Requirements: 11.1-11.6 - 奖励计算的确定性
        
        // 模拟奖励计算
        val baseCoins = 100
        val dailyCheckInReward = EarnStorage.REWARD_DAILY_CHECK_IN
        val steps1000Reward = EarnStorage.REWARD_STEPS_1000
        val steps3000Reward = EarnStorage.REWARD_STEPS_3000
        
        val afterDailyCheckIn = baseCoins + dailyCheckInReward
        val afterSteps1000 = afterDailyCheckIn + steps1000Reward
        val afterSteps3000 = afterSteps1000 + steps3000Reward
        
        assertEquals("Daily check in calculation should be correct", 110, afterDailyCheckIn)
        assertEquals("Steps 1000 calculation should be correct", 115, afterSteps1000)
        assertEquals("Steps 3000 calculation should be correct", 130, afterSteps3000)
    }
    
    @Test
    fun `task status progression should be logical`() {
        // Requirements: 10.2 - 任务状态存储和查询方法
        
        // 验证任务状态的逻辑顺序
        val statuses = listOf(
            com.example.step0724.data.model.TaskStatus.NOT_COMPLETED,
            com.example.step0724.data.model.TaskStatus.COMPLETED,
            com.example.step0724.data.model.TaskStatus.CLAIMED
        )
        
        // 验证状态转换的逻辑性
        assertEquals("First status should be NOT_COMPLETED", 
            com.example.step0724.data.model.TaskStatus.NOT_COMPLETED, statuses[0])
        assertEquals("Second status should be COMPLETED", 
            com.example.step0724.data.model.TaskStatus.COMPLETED, statuses[1])
        assertEquals("Third status should be CLAIMED", 
            com.example.step0724.data.model.TaskStatus.CLAIMED, statuses[2])
    }
    
    @Test
    fun `lucky wheel count progression should be valid`() {
        // Requirements: 10.5 - 转盘游戏次数统计功能
        
        val dailyLimit = EarnStorage.LUCKY_WHEEL_DAILY_LIMIT
        
        // 模拟转盘游戏次数递增
        for (i in 0..dailyLimit) {
            assertTrue("Count $i should be valid", i >= 0)
            assertTrue("Count $i should not exceed reasonable limit", i <= 20)
        }
        
        // 验证达到限制时的逻辑
        assertTrue("Should be able to reach daily limit", dailyLimit > 0)
        assertTrue("Daily limit should be reasonable", dailyLimit <= 20)
    }
    
    @Test
    fun `date boundary handling should work correctly`() {
        // Requirements: 8.4, 8.5 - 日期边界处理
        
        val currentDate = timeManager.getCurrentDateString()
        val timeUntilMidnight = timeManager.getTimeUntilMidnight()
        
        // 验证当前时间不会是负值
        assertNotNull("Current date should not be null", currentDate)
        assertNotNull("Time until midnight should not be null", timeUntilMidnight)
        
        // 验证时间格式
        assertTrue("Time should be in HH:MM format", 
            timeUntilMidnight.matches(Regex("\\d{2}:\\d{2}")))
        
        // 验证时间值的合理性
        val parts = timeUntilMidnight.split(":")
        val hours = parts[0].toInt()
        val minutes = parts[1].toInt()
        
        assertTrue("Hours should be non-negative", hours >= 0)
        assertTrue("Minutes should be non-negative", minutes >= 0)
        assertTrue("Hours should be less than 24", hours < 24)
        assertTrue("Minutes should be less than 60", minutes < 60)
    }
}