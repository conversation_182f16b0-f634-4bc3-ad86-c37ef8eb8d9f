package com.example.step0724.data.storage

import com.example.step0724.data.model.TaskStatus
import com.example.step0724.data.model.TaskType
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * EarnStorage单元测试
 * 
 * 测试EarnStorage类的常量和基本功能，包括：
 * - 奖励常量验证
 * - 基本数据结构验证
 * 
 * Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7
 * 
 * 注意：由于项目没有MockK依赖，这里只测试不需要MMKV的功能
 */
class EarnStorageTest {
    
    private lateinit var timeManager: TimeManager
    
    private val testDate = "2024-01-15"
    private val testDateYesterday = "2024-01-14"
    private val testDateTomorrow = "2024-01-16"
    
    @Before
    fun setUp() {
        timeManager = TimeManager()
    }
    
    // ==================== 奖励常量测试 ====================
    
    @Test
    fun `reward constants should have correct values`() {
        // Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6 - 验证奖励金币配置
        assertEquals("Daily check in reward should be 10", 10, EarnStorage.REWARD_DAILY_CHECK_IN)
        assertEquals("1000 steps reward should be 5", 5, EarnStorage.REWARD_STEPS_1000)
        assertEquals("3000 steps reward should be 15", 15, EarnStorage.REWARD_STEPS_3000)
        assertEquals("2 days check in reward should be 20", 20, EarnStorage.REWARD_CHECK_IN_2_DAYS)
        assertEquals("3 days check in reward should be 50", 50, EarnStorage.REWARD_CHECK_IN_3_DAYS)
        assertEquals("Lucky wheel daily reward should be 30", 30, EarnStorage.REWARD_LUCKY_WHEEL_DAILY)
    }
    
    @Test
    fun `lucky wheel daily limit should be correct`() {
        // Requirements: 10.5 - 转盘游戏次数统计功能
        assertEquals("Lucky wheel daily limit should be 10", 10, EarnStorage.LUCKY_WHEEL_DAILY_LIMIT)
    }
    
    // ==================== 任务类型验证测试 ====================
    
    @Test
    fun `all task types should be supported`() {
        // Requirements: 10.2 - 任务状态存储和查询方法
        val supportedTaskTypes = listOf(
            TaskType.DAILY_CHECK_IN,
            TaskType.LUCKY_WHEEL,
            TaskType.STEPS_1000,
            TaskType.STEPS_3000,
            TaskType.CHECK_IN_2_DAYS,
            TaskType.CHECK_IN_3_DAYS
        )
        
        // 验证所有任务类型都存在
        supportedTaskTypes.forEach { taskType ->
            assertNotNull("Task type $taskType should exist", taskType)
        }
    }
    
    @Test
    fun `task status enum should have all required values`() {
        // Requirements: 10.2 - 任务状态存储和查询方法
        val requiredStatuses = listOf(
            TaskStatus.NOT_COMPLETED,
            TaskStatus.COMPLETED,
            TaskStatus.CLAIMED
        )
        
        // 验证所有任务状态都存在
        requiredStatuses.forEach { status ->
            assertNotNull("Task status $status should exist", status)
        }
    }
    
    // ==================== 时间管理器集成测试 ====================
    
    @Test
    fun `time manager should provide current date`() {
        // Requirements: 8.1 - 获取当前日期字符串
        val currentDate = timeManager.getCurrentDateString()
        
        assertNotNull("Current date should not be null", currentDate)
        assertTrue("Date should match yyyy-MM-dd format", 
            currentDate.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }
    
    @Test
    fun `time manager should detect new day correctly`() {
        // Requirements: 8.3 - 判断日期变更
        val today = timeManager.getCurrentDateString()
        val yesterday = "2024-01-01" // A date in the past
        
        assertFalse("Same date should not be new day", timeManager.isNewDay(today))
        assertTrue("Different date should be new day", timeManager.isNewDay(yesterday))
    }
    
    // ==================== 数据结构验证测试 ====================
    
    @Test
    fun `storage keys should follow naming convention`() {
        // Requirements: 10.1, 10.2, 10.3 - 数据持久化键值命名规范
        
        // 验证键值命名规范（通过反射或直接验证常量）
        // 这里我们验证键值的格式是否符合预期
        val testDate = "2024-01-15"
        val expectedDailyTaskKey = "daily_tasks_${testDate}_${TaskType.DAILY_CHECK_IN.name}"
        val expectedOtherTaskKey = "other_tasks_${TaskType.CHECK_IN_2_DAYS.name}"
        val expectedLuckyWheelKey = "lucky_wheel_count_$testDate"
        
        // 验证键值格式
        assertTrue("Daily task key should contain date and task type", 
            expectedDailyTaskKey.contains(testDate) && expectedDailyTaskKey.contains("DAILY_CHECK_IN"))
        assertTrue("Other task key should contain task type", 
            expectedOtherTaskKey.contains("CHECK_IN_2_DAYS"))
        assertTrue("Lucky wheel key should contain date", 
            expectedLuckyWheelKey.contains(testDate))
    }
    
    @Test
    fun `consecutive day calculation logic should be testable`() {
        // Requirements: 10.3 - 签到功能数据持久化逻辑
        
        // 测试日期格式和连续性逻辑的基础
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val today = java.util.Date()
        val yesterday = java.util.Date(today.time - 24 * 60 * 60 * 1000)
        
        val todayString = dateFormat.format(today)
        val yesterdayString = dateFormat.format(yesterday)
        
        assertNotNull("Today string should not be null", todayString)
        assertNotNull("Yesterday string should not be null", yesterdayString)
        assertNotEquals("Today and yesterday should be different", todayString, yesterdayString)
    }
    
    // ==================== 错误处理验证测试 ====================
    
    @Test
    fun `task status enum should handle string conversion`() {
        // Requirements: 10.2 - 任务状态存储和查询方法的错误处理
        
        // 测试枚举值的字符串转换
        val status = TaskStatus.COMPLETED
        val statusString = status.name
        val convertedStatus = TaskStatus.valueOf(statusString)
        
        assertEquals("Status conversion should work correctly", status, convertedStatus)
    }
    
    @Test
    fun `task type enum should handle string conversion`() {
        // Requirements: 10.2 - 任务类型的字符串转换
        
        val taskType = TaskType.DAILY_CHECK_IN
        val taskTypeString = taskType.name
        val convertedTaskType = TaskType.valueOf(taskTypeString)
        
        assertEquals("Task type conversion should work correctly", taskType, convertedTaskType)
    }
    
    // ==================== 业务逻辑验证测试 ====================
    
    @Test
    fun `reward amounts should be reasonable`() {
        // Requirements: 11.1-11.6 - 奖励金币配置合理性验证
        
        // 验证奖励金额的合理性
        assertTrue("Daily check in reward should be positive", EarnStorage.REWARD_DAILY_CHECK_IN > 0)
        assertTrue("Steps rewards should be positive", EarnStorage.REWARD_STEPS_1000 > 0)
        assertTrue("Steps rewards should be positive", EarnStorage.REWARD_STEPS_3000 > 0)
        assertTrue("Consecutive check in rewards should be positive", EarnStorage.REWARD_CHECK_IN_2_DAYS > 0)
        assertTrue("Consecutive check in rewards should be positive", EarnStorage.REWARD_CHECK_IN_3_DAYS > 0)
        assertTrue("Lucky wheel reward should be positive", EarnStorage.REWARD_LUCKY_WHEEL_DAILY > 0)
        
        // 验证奖励递增逻辑
        assertTrue("3000 steps should reward more than 1000 steps", 
            EarnStorage.REWARD_STEPS_3000 > EarnStorage.REWARD_STEPS_1000)
        assertTrue("3 days check in should reward more than 2 days", 
            EarnStorage.REWARD_CHECK_IN_3_DAYS > EarnStorage.REWARD_CHECK_IN_2_DAYS)
    }
    
    @Test
    fun `lucky wheel limit should be reasonable`() {
        // Requirements: 10.5 - 转盘游戏次数统计功能
        
        assertTrue("Lucky wheel daily limit should be positive", EarnStorage.LUCKY_WHEEL_DAILY_LIMIT > 0)
        assertTrue("Lucky wheel daily limit should not be too high", EarnStorage.LUCKY_WHEEL_DAILY_LIMIT <= 20)
    }
    
    // ==================== 签到功能逻辑测试 ====================
    
    @Test
    fun `check in date format should be consistent`() {
        // Requirements: 5.4 - 当新的一天开始时，系统应重置签到状态，允许用户再次签到
        // Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
        
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val testDate = dateFormat.format(java.util.Date())
        
        assertNotNull("Date format should not be null", testDate)
        assertTrue("Date should match yyyy-MM-dd format", 
            testDate.matches(Regex("\\d{4}-\\d{2}-\\d{2}")))
    }
    
    @Test
    fun `consecutive day calculation should work correctly`() {
        // Requirements: 5.5 - 当用户连续签到时，系统应记录连续签到天数
        // Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
        
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val today = java.util.Date()
        val yesterday = java.util.Date(today.time - 24 * 60 * 60 * 1000)
        val twoDaysAgo = java.util.Date(today.time - 2 * 24 * 60 * 60 * 1000)
        
        val todayString = dateFormat.format(today)
        val yesterdayString = dateFormat.format(yesterday)
        val twoDaysAgoString = dateFormat.format(twoDaysAgo)
        
        // Test consecutive day logic
        assertNotEquals("Today and yesterday should be different dates", todayString, yesterdayString)
        assertNotEquals("Yesterday and two days ago should be different dates", yesterdayString, twoDaysAgoString)
        
        // Test date difference calculation
        val diffInMillis = today.time - yesterday.time
        val diffInDays = diffInMillis / (1000 * 60 * 60 * 24)
        assertEquals("Difference should be 1 day", 1L, diffInDays)
    }
    
    @Test
    fun `check in streak interruption logic should be testable`() {
        // Requirements: 7.8 - 当用户的连续签到记录中断时，系统应重置连续签到任务进度
        
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val today = java.util.Date()
        val threeDaysAgo = java.util.Date(today.time - 3 * 24 * 60 * 60 * 1000)
        
        val todayString = dateFormat.format(today)
        val threeDaysAgoString = dateFormat.format(threeDaysAgo)
        
        // Test interruption detection logic
        val diffInMillis = today.time - threeDaysAgo.time
        val diffInDays = diffInMillis / (1000 * 60 * 60 * 24)
        
        assertTrue("Three days difference should be greater than 1", diffInDays > 1)
        assertNotEquals("Today and three days ago should be different", todayString, threeDaysAgoString)
    }
    
    @Test
    fun `consecutive check in task thresholds should be correct`() {
        // Requirements: 7.2 - 当用户查看其他任务时，系统应包含：Check in 2 days、Check in 3 days
        // Requirements: 7.3 - 当用户连续签到达到要求天数但未领取奖励时，系统应显示"Claim"按钮
        
        // Test that the thresholds for consecutive check-in tasks are reasonable
        val twodayThreshold = 2
        val threeDayThreshold = 3
        
        assertTrue("2-day threshold should be positive", twodayThreshold > 0)
        assertTrue("3-day threshold should be positive", threeDayThreshold > 0)
        assertTrue("3-day threshold should be greater than 2-day threshold", 
            threeDayThreshold > twodayThreshold)
        
        // Test reward progression
        assertTrue("3-day reward should be greater than 2-day reward",
            EarnStorage.REWARD_CHECK_IN_3_DAYS > EarnStorage.REWARD_CHECK_IN_2_DAYS)
    }
}