package com.example.step0724.domain.usecase

import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class UnitConversionUseCaseTest {

    private lateinit var unitConversionUseCase: UnitConversionUseCase

    @Before
    fun setUp() {
        unitConversionUseCase = UnitConversionUseCase()
    }

    @Test
    fun `cmToFeetInches should convert correctly`() {
        // 需求9.3：1英尺(ft) = 12英寸(in) = 30.48000cm
        val cm = 182.88 // 6 feet exactly
        val expectedFeet = 6
        val expectedInches = 0
        
        val result = unitConversionUseCase.cmToFeetInches(cm)
        
        assertEquals(expectedFeet, result.first)
        assertEquals(expectedInches, result.second)
    }

    @Test
    fun `feetInchesToCm should convert correctly`() {
        // 需求9.3：1英尺(ft) = 12英寸(in) = 30.48000cm
        val feet = 5
        val inches = 8
        val expectedCm = (5 * 12 + 8) * 2.54 // = 172.72 cm
        
        val result = unitConversionUseCase.feetInchesToCm(feet, inches)
        
        assertEquals(expectedCm, result, 0.01)
    }

    @Test
    fun `kgToLbs should convert correctly`() {
        // 需求9.3：1英镑(lbs) = 0.45359237kg
        val kg = 70.0
        val expectedLbs = kg / 0.45359237
        
        val result = unitConversionUseCase.kgToLbs(kg)
        
        assertEquals(expectedLbs, result, 0.001)
    }

    @Test
    fun `lbsToKg should convert correctly`() {
        // 需求9.3：1英镑(lbs) = 0.45359237kg
        val lbs = 154.32
        val expectedKg = lbs * 0.45359237
        
        val result = unitConversionUseCase.lbsToKg(lbs)
        
        assertEquals(expectedKg, result, 0.001)
    }

    @Test
    fun `kmToMiles should convert correctly`() {
        // 需求9.3：1km = 0.62137119英里(mi)
        val km = 10.0
        val expectedMiles = km * 0.62137119
        
        val result = unitConversionUseCase.kmToMiles(km)
        
        assertEquals(expectedMiles, result, 0.001)
    }

    @Test
    fun `milesToKm should convert correctly`() {
        // 需求9.3：1km = 0.62137119英里(mi)
        val miles = 6.2137119
        val expectedKm = miles / 0.62137119
        
        val result = unitConversionUseCase.milesToKm(miles)
        
        assertEquals(expectedKm, result, 0.001)
    }
}