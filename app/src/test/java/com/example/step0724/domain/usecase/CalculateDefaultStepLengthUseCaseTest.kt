package com.example.step0724.domain.usecase

import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class CalculateDefaultStepLengthUseCaseTest {

    private lateinit var calculateDefaultStepLengthUseCase: CalculateDefaultStepLengthUseCase

    @Before
    fun setUp() {
        calculateDefaultStepLengthUseCase = CalculateDefaultStepLengthUseCase()
    }

    @Test
    fun `calculate should use correct formula`() {
        // 需求4.9：步长(cm) = 身高(cm) × 0.415
        val heightCm = 172.0
        val expectedStepLength = heightCm * 0.415 // = 71.38 cm
        
        val result = calculateDefaultStepLengthUseCase.calculate(heightCm)
        
        assertEquals(expectedStepLength, result, 0.001)
    }

    @Test
    fun `calculate should work with different heights`() {
        // Test with different height values
        val testCases = listOf(
            160.0 to 66.4,  // 160 * 0.415 = 66.4
            180.0 to 74.7,  // 180 * 0.415 = 74.7
            150.0 to 62.25  // 150 * 0.415 = 62.25
        )
        
        testCases.forEach { (height, expected) ->
            val result = calculateDefaultStepLengthUseCase.calculate(height)
            assertEquals("Failed for height $height", expected, result, 0.001)
        }
    }
}