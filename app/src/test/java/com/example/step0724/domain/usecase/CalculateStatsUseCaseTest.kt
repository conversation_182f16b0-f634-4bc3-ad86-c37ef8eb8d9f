package com.example.step0724.domain.usecase

import com.example.step0724.data.model.UnitSystem
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class CalculateStatsUseCaseTest {

    private lateinit var unitConversionUseCase: UnitConversionUseCase
    private lateinit var calculateStatsUseCase: CalculateStatsUseCase

    @Before
    fun setUp() {
        unitConversionUseCase = UnitConversionUseCase()
        calculateStatsUseCase = CalculateStatsUseCase(unitConversionUseCase)
    }

    @Test
    fun `calculateDistance should use correct formula for metric system`() {
        // 需求2.3：走路距离(km) = 步长(cm) × 步数 ÷ 100 ÷ 1000
        val steps = 1000
        val stepLengthCm = 70.0
        val expectedDistanceKm = 70.0 * 1000 / 100.0 / 1000.0 // = 0.7 km
        
        val result = calculateStatsUseCase.calculateDistance(steps, stepLengthCm, UnitSystem.METRIC)
        
        assertEquals(expectedDistanceKm, result, 0.001)
    }

    @Test
    fun `calculateDistance should convert to miles for imperial system`() {
        val steps = 1000
        val stepLengthCm = 70.0
        val distanceKm = 70.0 * 1000 / 100.0 / 1000.0 // = 0.7 km
        val expectedDistanceMi = distanceKm * 0.62137119 // km to miles
        
        val result = calculateStatsUseCase.calculateDistance(steps, stepLengthCm, UnitSystem.IMPERIAL)
        
        assertEquals(expectedDistanceMi, result, 0.001)
    }

    @Test
    fun `calculateCalories should use correct formula`() {
        // 需求2.3：消耗卡路里 = 走路时间(minutes) × 3.0 × 3.5 × 体重(kg) ÷ 200
        val steps = 1000
        val weightKg = 70.0
        val walkingTimeMinutes = (steps * 0.6) / 60.0 // = 10 minutes
        val expectedCalories = walkingTimeMinutes * 3.0 * 3.5 * weightKg / 200.0
        
        val result = calculateStatsUseCase.calculateCalories(steps, weightKg)
        
        assertEquals(expectedCalories, result, 0.001)
    }

    @Test
    fun `calculateWalkingTimeSeconds should use correct formula`() {
        // 需求2.3：走路时间(seconds) = 步数 × 0.6
        val steps = 1000
        val expectedSeconds = (steps * 0.6).toLong() // = 600 seconds
        
        val result = calculateStatsUseCase.calculateWalkingTimeSeconds(steps)
        
        assertEquals(expectedSeconds, result)
    }

    @Test
    fun `formatWalkingTime should return correct hours and minutes`() {
        val steps = 10000 // 6000 seconds = 1 hour 40 minutes
        val expectedHours = 1
        val expectedMinutes = 40
        
        val result = calculateStatsUseCase.formatWalkingTime(steps)
        
        assertEquals(expectedHours, result.first)
        assertEquals(expectedMinutes, result.second)
    }
}