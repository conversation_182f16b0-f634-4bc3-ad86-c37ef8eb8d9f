# 任务13：倒计时功能实现总结

## 实现概述

任务13要求实现倒计时功能，包括在EarnViewModel中添加倒计时协程、实现每分钟更新倒计时显示、添加午夜时间重置任务状态逻辑，以及实现页面进入时倒计时的准确显示。

## 完成的功能

### 1. 倒计时协程实现 (Requirements: 8.1, 8.2)

在`EarnViewModel`中实现了`startCountdownTimer()`方法：
- 使用协程每分钟精确更新倒计时显示
- 计算到下一分钟整点的精确延迟时间
- 确保延迟时间合理（1秒到60秒之间）
- 添加错误处理和重试机制

### 2. 每分钟更新倒计时显示 (Requirements: 8.2)

- 实现精确的分钟级更新机制
- 使用`System.currentTimeMillis()`计算精确延迟
- 在每个整分钟时刻更新UI状态
- 支持手动触发倒计时更新

### 3. 午夜时间重置任务状态逻辑 (Requirements: 8.3, 8.4)

在`updateCountdown()`方法中实现：
- 检查是否是新的一天
- 自动重置每日任务状态
- 显示新一天开始的提示信息
- 处理重置失败的异常情况
- 支持倒计时到达00:00时的即时重置

### 4. 页面进入时倒计时的准确显示 (Requirements: 8.5)

实现了页面生命周期管理：
- `onPageEntered()`: 页面进入时立即更新倒计时并重启定时器
- `onPageLeft()`: 页面离开时停止定时器节省资源
- `manualUpdateCountdown()`: 支持手动更新倒计时
- 在`EarnScreen`中正确调用生命周期方法

## 核心实现细节

### 1. 倒计时定时器管理

```kotlin
private fun startCountdownTimer() {
    countdownJob?.cancel()
    countdownJob = viewModelScope.launch {
        // 首次立即更新倒计时显示，确保页面进入时显示准确时间
        updateCountdown()
        
        while (true) {
            try {
                // 计算到下一分钟整点的精确延迟时间
                val currentTimeMillis = System.currentTimeMillis()
                val nextMinuteMillis = ((currentTimeMillis / 60_000) + 1) * 60_000
                val delayMillis = nextMinuteMillis - currentTimeMillis
                
                // 确保延迟时间合理（1秒到60秒之间）
                val safeDelayMillis = delayMillis.coerceIn(1000L, 60_000L)
                
                // 延迟到下一分钟整点
                delay(safeDelayMillis)
                
                // 更新倒计时
                updateCountdown()
                
                // 检查是否需要重置任务（午夜时间检查）
                if (timeManager.isMidnightPassed()) {
                    // 立即触发任务重置
                    val currentDate = timeManager.getCurrentDateString()
                    if (timeManager.isNewDay(lastUpdateDate)) {
                        lastUpdateDate = currentDate
                        loadEarnData() // 重新加载数据以反映重置状态
                    }
                }
                
            } catch (e: Exception) {
                // 错误处理和重试机制
                if (e !is kotlinx.coroutines.CancellationException) {
                    setEffect { EarnContract.Effect.ShowError("倒计时更新失败: ${e.message}") }
                    delay(30_000) // 发生错误时等待30秒后重试
                } else {
                    break
                }
            }
        }
    }
}
```

### 2. 倒计时更新逻辑

```kotlin
private suspend fun updateCountdown() {
    try {
        val currentDate = timeManager.getCurrentDateString()
        val timeUntilReset = timeManager.getTimeUntilMidnight()
        
        // 检查是否是新的一天
        if (timeManager.isNewDay(lastUpdateDate)) {
            // 新的一天开始，重置每日任务
            val resetResult = earnRepository.resetDailyTasks()
            
            resetResult.onSuccess {
                lastUpdateDate = currentDate
                setEffect { 
                    EarnContract.Effect.ShowSuccess("新的一天开始了！每日任务已重置。") 
                }
                loadEarnData()
            }.onFailure { exception ->
                setEffect { 
                    EarnContract.Effect.ShowError("重置每日任务失败: ${exception.message}") 
                }
                setState { copy(timeUntilReset = timeUntilReset) }
            }
        } else {
            // 同一天内，只更新倒计时显示
            setState { copy(timeUntilReset = timeUntilReset) }
            
            // 检查倒计时是否接近00:00（提供不同时间点的提醒）
            when (timeUntilReset) {
                "00:01" -> {
                    setEffect { 
                        EarnContract.Effect.ShowSuccess("每日任务将在1分钟后重置！") 
                    }
                }
                "00:05" -> {
                    setEffect { 
                        EarnContract.Effect.ShowSuccess("每日任务将在5分钟后重置，请及时完成任务！") 
                    }
                }
                "00:00" -> {
                    // 倒计时到达00:00，立即触发重置检查
                    if (timeManager.isNewDay(lastUpdateDate)) {
                        val resetResult = earnRepository.resetDailyTasks()
                        resetResult.onSuccess {
                            lastUpdateDate = currentDate
                            setEffect { 
                                EarnContract.Effect.ShowSuccess("午夜时间到！每日任务已自动重置。") 
                            }
                            loadEarnData()
                        }
                    }
                }
            }
        }
        
        // 检查是否需要更新步数相关任务状态
        val currentSteps = earnRepository.getCurrentSteps()
        if (currentSteps != state.value.currentSteps) {
            refreshEarnData()
        }
        
    } catch (e: Exception) {
        setEffect { EarnContract.Effect.ShowError("更新倒计时失败: ${e.message}") }
    }
}
```

### 3. 页面生命周期管理

```kotlin
// 页面进入时的倒计时初始化
fun onPageEntered() {
    viewModelScope.launch {
        // 立即更新倒计时显示，确保显示准确的剩余时间
        updateCountdown()
        
        // 重启倒计时定时器，确保定时器与当前时间同步
        startCountdownTimer()
    }
}

// 页面离开时的资源清理
fun onPageLeft() {
    // 停止倒计时定时器以节省资源
    stopCountdownTimer()
}
```

### 4. EarnScreen中的生命周期集成

```kotlin
// 页面进入时的初始化
LaunchedEffect(Unit) {
    viewModel.sendIntent(EarnContract.Intent.LoadData)
    // 确保倒计时显示准确的剩余时间
    viewModel.onPageEntered()
}

// 页面离开时的清理
DisposableEffect(Unit) {
    onDispose {
        // 页面离开时停止倒计时定时器以节省资源
        viewModel.onPageLeft()
    }
}
```

## 新增的辅助方法

1. **`manualUpdateCountdown()`**: 手动触发倒计时更新
2. **`isCountdownTimerRunning()`**: 检查倒计时定时器是否正在运行
3. **`getCountdownStatus()`**: 获取当前倒计时状态信息（用于调试）
4. **`restartCountdownTimer()`**: 重启倒计时定时器

## 错误处理和边界情况

1. **网络异常处理**: 当TimeManager或EarnRepository调用失败时，显示错误信息但不中断倒计时
2. **时间异常处理**: 确保延迟时间在合理范围内，防止异常的时间计算
3. **协程取消处理**: 正确处理协程取消异常，避免不必要的错误日志
4. **重试机制**: 发生错误时等待30秒后重试，避免频繁错误

## 性能优化

1. **资源管理**: 页面不可见时停止倒计时定时器，节省系统资源
2. **精确计时**: 使用精确的毫秒级计算，确保倒计时准确性
3. **状态缓存**: 避免不必要的UI状态更新，只在数据真正变化时更新

## 测试覆盖

创建了`EarnViewModelCountdownTest.kt`测试文件，覆盖以下测试场景：
- 倒计时定时器的启动和停止
- 页面进入时的倒计时初始化
- 新一天的任务重置逻辑
- 倒计时到达00:00时的处理
- 倒计时提醒功能
- 错误处理机制

## 总结

任务13的倒计时功能已完全实现，满足所有需求：
- ✅ 8.1: 系统使用"HH:MM"格式显示剩余时间
- ✅ 8.2: 当用户停留在Earn页面时，系统每分钟更新一次倒计时显示
- ✅ 8.3: 当倒计时到达00:00时，系统重置所有每日任务状态
- ✅ 8.4: 当倒计时到达00:00时，系统更新倒计时为下一天的24小时
- ✅ 8.5: 当用户重新进入Earn页面时，系统显示准确的剩余时间

实现包括完整的错误处理、性能优化、资源管理和测试覆盖，确保功能的稳定性和可靠性。